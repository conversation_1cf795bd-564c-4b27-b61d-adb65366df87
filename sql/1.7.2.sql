ALTER TABLE `survey`.`survey_question` ADD `area_type` INT  NULL  DEFAULT NULL  COMMENT '地区类型'  AFTER `code`;
ALTER TABLE `survey`.`survey_response` ADD COLUMN  `channel_id` BIGINT DEFAULT '0' COMMENT '渠道id';
ALTER TABLE `survey`.`survey_response` ADD   KEY `index_sid_cid` (`s_id`,`channel_id`);

DROP TABLE IF EXISTS `survey`.`survey_channel`;
CREATE TABLE `survey_channel` (
                                  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '渠道id',
                                  `s_id` bigint NOT NULL COMMENT '问卷id',
                                  `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型,0通用类型,1wechat短链接,2短信,3微信服务号,4页面嵌入,5社区surveyplus,6场景互动',
                                  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '名称',
                                  `status` tinyint NOT NULL COMMENT '状态（0:未设置，1:回收中，2:暂停，3:已完成，4:关闭）',
                                  `recovery_amount` int DEFAULT '0' COMMENT '回收数量',
                                  `create_user` varchar(20) NOT NULL DEFAULT '' COMMENT '创建人',
                                  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                  `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
                                  `enable_wechat_limit` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启只能在微信填答限制',
                                  `enable_ip_limit` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启IP限制',
                                  `enable_device_limit` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启设备限制',
                                  `enable_data_limit` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启调查日期限制',
                                  `start_time` datetime DEFAULT NULL COMMENT '问卷开始时间',
                                  `end_time` datetime DEFAULT NULL COMMENT '问卷结束时间',
                                  `send_time` datetime DEFAULT NULL COMMENT '问卷发送时间',
                                  `task_type` varchar(20) DEFAULT NULL COMMENT '问卷发送',
                                  `configure` varchar(1500) COMMENT '渠道配置信息，json格式',
                                  PRIMARY KEY (`id`) USING BTREE,
                                  KEY `idx_sid_type` (`s_id`,`type`) USING BTREE,
                                  KEY `id_deleted` (`id`,`deleted`) USING BTREE
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COMMENT='问卷渠道表';

DROP TABLE IF EXISTS `survey`.`survey_send_record`;
CREATE TABLE `survey_send_record` (
                                      `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键id',
                                      `channel_id` bigint NOT NULL DEFAULT '0' COMMENT '渠道id',
                                      `client_id` varchar(100) DEFAULT NULL COMMENT '客户端id',
                                      `name` varchar(20) DEFAULT '' COMMENT '接收人姓名',
                                      `account` varchar(64) NOT NULL DEFAULT '' COMMENT '接收人账号(微信openid或者手机号)',
                                      `content` varchar(500) NOT NULL DEFAULT '' COMMENT '推送内容',
                                      `send_url` varchar(200) NOT NULL DEFAULT '' COMMENT '问卷地址',
                                      `send_count` int NOT NULL DEFAULT '0' COMMENT '发送次数',
                                      `send_time` datetime DEFAULT NULL COMMENT '发送时间',
                                      `send_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '发送状态(0:待发送,1:发送成功,2:发送失败)',
                                      `reply_status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '答题状态(0:未访问,1:未提交,2:已提交)',
                                      `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '完成状态(0:未完成,1:已完成)',
                                      `type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '类型,0通用类型,1wechat短链接,2短信,3微信服务号,4页面嵌入,5社区surveyplus,6场景互动',
                                      `send_id` varchar(32) NOT NULL DEFAULT '' COMMENT '回执id',
                                      `result` varchar(200) NOT NULL DEFAULT '' COMMENT '短信发送返回结果',
                                      `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                      `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                      `s_id` bigint DEFAULT NULL COMMENT '问卷id',
                                      `customer_id` bigint DEFAULT NULL COMMENT '用户id',
                                      PRIMARY KEY (`id`),
                                      KEY `idx_cid_status` (`channel_id`,`status`),
                                      KEY `client_id` (`client_id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4  COMMENT='问卷推送记录表';
