ALTER TABLE `survey`.`survey_quota` ADD `name` VARCHAR(20)  NULL  DEFAULT NULL  AFTER `modify_time`;
ALTER TABLE `survey`.`survey_quota` ADD `expression_hash` VARCHAR(128)  NULL  DEFAULT NULL  AFTER `name`;
ALTER TABLE `survey`.`survey` ADD `quota_status` tinyint NOT NULL DEFAULT '0' COMMENT '配额计算状态 0计算初始状态 1计算中' after `enable_quota`;
ALTER TABLE `survey`.`survey_quota` MODIFY COLUMN  `expression` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' comment '配额表达式';
