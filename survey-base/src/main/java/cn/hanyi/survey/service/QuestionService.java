package cn.hanyi.survey.service;

import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyQuestionColumnRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionItemRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.dto.question.QuestionGroupDto;
import cn.hanyi.survey.dto.question.QuestionSequenceDto;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QuestionService extends CustomEmbeddedService<SurveyQuestion, SurveyQuestionDto, SurveyQuestionRepository> {

    @Autowired
    SurveyQuestionRepository repository;
    @Autowired
    private SurveyQuestionColumnRepository columnRepository;
    @Autowired
    private SurveyQuestionItemRepository itemRepository;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    @Lazy
    private SurveyService surveyService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;
    @Autowired
    private QuestionsDynamicItemService questionsDynamicItemService;


    @Override
    protected Object requireParent(long l) {
        return surveyService.require(l);
    }

    @Override
    public Page<SurveyQuestionDto> findAllEmbeddedMany(Long entityId, String embeddedMapperBy, ResourceEntityQueryDto<SurveyQuestionDto> queryDto) {
        if (queryDto.getSorts().isUnsorted()) {

            queryDto.setSorts(Sort.by(Sort.Direction.ASC, "sequence"));
        }
        return super.findAllEmbeddedMany(entityId, embeddedMapperBy, queryDto);
    }

    @Override
    public Boolean deleteOneEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId) {
        surveyEventTrigger.questionDelete(entityId, embeddedId);
        return super.deleteOneEmbeddedMany(entityId, embeddedMapperBy, embeddedId);
    }

    @Override
    public void delete(SurveyQuestion entity) {
        surveyService.checkSurveyByOrgId(entity.getSurvey().getId());
        surveyEventTrigger.questionDelete(entity.getSurvey().getId(), entity.getId());
        super.delete(entity);
    }

    @Override
    public SurveyQuestionDto updateOneEmbeddedMany(Long rootId, String rootFieldNameInEmbedded, Long embeddedId, SurveyQuestionDto change) {
        surveyService.checkSurveyByOrgId(rootId);
        return super.updateOneEmbeddedMany(rootId, rootFieldNameInEmbedded, embeddedId, change);
    }

    /**
     * 获取题型
     *
     * @param questionId
     * @return
     */
    public SurveyQuestion requireQuestion(Long questionId) {
        Optional<SurveyQuestion> questionOptional = repository.findById(questionId);
        if (questionOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        return questionOptional.get();
    }

    /**
     * 手动获取题型
     *
     * @param survey
     */
    @Deprecated(since = "1.8.0 使用EAGER加载")
    public void customLoadQuestion(Survey survey) {
        if (survey == null) {
            return;
        }

        entityManager.detach(survey);

        HashMap<Long, Long> qIds = new HashMap<>();
        List<SurveyQuestion> questions = survey.getQuestions();
        questions.forEach(i -> qIds.put(i.getId(), i.getId()));

        if (!qIds.isEmpty()) {
            List<SurveyQuestionColumn> columns = columnRepository.findAll((Specification<SurveyQuestionColumn>) (root, query, cb) -> root.get("question").in(qIds.keySet()), Sort.by(Sort.Direction.ASC, "sequence"));
            List<SurveyQuestionItem> items = itemRepository.findAll((Specification<SurveyQuestionItem>) (root, query, cb) -> root.get("question").in(qIds.keySet()), Sort.by(Sort.Direction.ASC, "sequence"));

            HashMap<Long, List<SurveyQuestionColumn>> columnMap = new HashMap<>();
            HashMap<Long, List<SurveyQuestionItem>> itemMap = new HashMap<>();

            columns.forEach(i -> {
                columnMap.computeIfAbsent(i.getQuestion().getId(), k -> new ArrayList<>()).add(i);
            });
            items.forEach(i -> {
                itemMap.computeIfAbsent(i.getQuestion().getId(), k -> new ArrayList<>()).add(i);
            });

            questions.forEach(i -> {
                List<SurveyQuestionItem> is = itemMap.getOrDefault(i.getId(), new ArrayList<>());
                List<SurveyQuestionColumn> cs = columnMap.getOrDefault(i.getId(), new ArrayList<>());
                is.sort(Comparator.comparingInt(SurveyQuestionItem::getSequence));
                cs.sort(Comparator.comparingInt(SurveyQuestionColumn::getSequence));
                i.setItems(is);
                i.setColumns(cs);
            });
        }

    }

    /**
     * 手动保存
     */
    public SurveyQuestion save(SurveyQuestion surveyQuestion) {
        SurveyQuestion saved = repository.save(surveyQuestion);
        return saved;
    }

    public SurveyQuestion find(SurveyQuestion surveyQuestion) {
        Optional<SurveyQuestion> saved = repository.findById(surveyQuestion.getId());
        return saved.get();
    }

    /**
     * 更新题型sequence + 1
     */
    @Transactional
    public void increaseSequence(Long surveyId, Number sequence, int step) {
        repository.increaseSequenceBySurveyIdAndSequence(step, surveyId, sequence);
    }

    /**
     * 删除题组和题目
     * @param surveyId
     * @param dto
     */
    @Transactional
    public Boolean batchDelQuestionAndLogic(Long surveyId, QuestionGroupDto dto) {
        surveyService.checkSurveyByOrgId(surveyId);
        dto.getLogicId().forEach(logicId -> surveyService.deleteLogic(logicId));
        // 无法直接获取到survey，只能从question中获取
        if (CollectionUtils.isNotEmpty(dto.getQid())) {
            var question = requireQuestion(dto.getQid().get(0));
            dto.getQid().forEach(id -> surveyEventTrigger.questionDelete(question.getSurvey().getId(), id));
        }
        repository.deleteByIdIn(dto.getQid());
        return true;
    }

    public void graphQuestion(Survey survey) {
        repository.findAllBySurvey(survey).forEach(i -> {
        });
    }

    @Transactional
    public boolean updateSequence(Long surveyId, List<QuestionSequenceDto> data) {
        surveyService.checkSurveyByOrgId(surveyId);
        Survey survey = surveyService.requireWithFilter(surveyId);
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(i -> {
                if (i.getId() != null && i.getId() > 0 && i.getSequence() != null) {
                    repository.updateSequenceBySurveyAndId(BigDecimal.valueOf(i.getSequence()), survey, i.getId());
                }
            });
            return true;
        }
        return false;
    }

    public void replaceDynamicItems(List<SurveyQuestion> questions) {
        questions.forEach(i -> {
            if (i.getIsDynamicItem() != null && i.getIsDynamicItem()) {
                List<SurveyQuestionItem> items = questionsDynamicItemService.findAllByQuestion(i.getId());
                if (!items.isEmpty()) {
                    i.setItems(items);
                }
            }
        });
    }
}