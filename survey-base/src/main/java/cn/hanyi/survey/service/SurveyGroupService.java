package cn.hanyi.survey.service;


import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyGroup;
import cn.hanyi.survey.core.entity.SurveyGroupDto;
import cn.hanyi.survey.core.repository.SurveyGroupRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.dto.CountDto;
import cn.hanyi.survey.dto.SurveySimpleListDto;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SurveyGroupService extends BaseMixedGroupService<
        SurveyGroup, SurveyGroupDto, SurveyGroupRepository,
        Survey, SurveySimpleListDto, SurveyCustomQueryService> {

    @Autowired
    private UserService userService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SurveyRepository surveyRepository;

    @Override
    public <S extends BaseEntityDTO<SurveyGroup>> SurveyGroupDto updateOne(long id, S change) {
        if (!currentIsAdmin()) {
            return scopeQuery(EntityScopeStrategyType.OWNER_CORPORATION, () -> super.updateOne(id, change));
        }
        return super.updateOne(id, change);
    }

    @Override
    public void afterMapToDto(SurveyGroup entity, SurveyGroupDto dto) {
        if (entity != null && dto != null) {
            ArrayList<SurveyGroupDto> dtoArrayList = new ArrayList<>(1);
            dtoArrayList.add(dto);
            afterMapToDto(List.of(entity), dtoArrayList);
        }
        dto.setSurveys(surveyRepository.findAllByGroupId(dto.getId(), Sort.by("modifyTime").descending()));
    }

    @Override
    public void afterMapToDto(List<SurveyGroup> entity, List<SurveyGroupDto> dto) {
        Set<Long> userIds = new HashSet<>();
        Set<Long> ids = new HashSet<>();
        dto.forEach(d -> {
            SurveyGroup e = d.getEntity();
            ids.add(e.getId());
            Optional.ofNullable(e.getEditorId()).ifPresent(userIds::add);
            Optional.ofNullable(e.getUserId()).ifPresent(userIds::add);
        });
        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(userIds), User::getId, SimpleUser::fromUser);
        Map<Long, CountDto> countSurveyMap = countSurveyMap(ids);
        List<SurveyGroupDto> missingCreator = new ArrayList<>();
        dto.forEach(d -> {
            SurveyGroup e = d.getEntity();
            Optional.ofNullable(e.getEditorId()).ifPresent(i -> d.setEditorUser(userMap.get(i)));
            Optional.ofNullable(e.getUserId()).ifPresent(i -> d.setCreator(userMap.get(i)));
            if (d.getCreator() == null) {
                missingCreator.add(d);
            }
            CountDto countDto = countSurveyMap.get(e.getId());
            if (countDto == null) {
                d.setCountSurvey(0);
            } else {
                d.setCountSurvey(countDto.getCount());
                Date modifyTime = countDto.getModifyTime();
                d.setModifyTime(modifyTime != null && d.getModifyTime().before(modifyTime) ? modifyTime : d.getModifyTime());
            }
        });
        Collections.sort(dto, Comparator.comparing(SurveyGroupDto::getModifyTime).reversed());
        if (!missingCreator.isEmpty()) {
            SimpleUser superAdmin = userService.getAdminUser(TenantContext.getCurrentTenant());
            if (superAdmin != null) {
                missingCreator.forEach(i -> i.setCreator(superAdmin));
                List<Long> resetIds = missingCreator.stream().map(SurveyGroupDto::getId).collect(Collectors.toList());
                repository.updateUserIdByOrgIdAndIdIn(superAdmin.getId(), TenantContext.getCurrentTenant(), resetIds);
            }
        }
    }

    private void setCustomStrategy() {
        TenantContext.addCustomEntityScopeStrategy(SurveyGroup.class, EntityScopeStrategyType.OWNER);
    }

    @Override
    public List<SurveyGroupDto> findAll(ResourceEntityQueryDto<SurveyGroupDto> queryDto) {
        setCustomStrategy();
        List<SurveyGroupDto> list = super.findAll(queryDto);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(0, getEmptyGroup());
        return list;
    }

    public SurveyGroupDto getEmptyGroup() {
        Long orgId = TenantContext.getCurrentTenant();
        SurveyGroupDto defaultGroup = new SurveyGroupDto();
        defaultGroup.setOrgId(orgId);
        defaultGroup.setId(0L);
        defaultGroup.setSequence(0);
        defaultGroup.setTitle("项目列表");
        return defaultGroup;
    }

    private Map<Long, CountDto> countSurveyMap(Set<Long> groupIds) {
        if (CollectionUtils.isNotEmpty(groupIds)) {
            Long orgId = TenantContext.getCurrentTenant();
            String ids = groupIds.stream().map(Objects::toString).collect(Collectors.joining(","));
            String sql = String.format("select group_id id ,count(1) count, max(modify_time) modifyTime  from survey where deleted=0 and org_id = %s and group_id in (%s) group by group_id", orgId, ids);
            List<CountDto> counts = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(CountDto.class));
            if (CollectionUtils.isNotEmpty(counts)) {
                return counts.stream().collect(Collectors.toMap(CountDto::getId, Function.identity()));
            }
        }
        return new HashMap<>();
    }

    public List<Long> getMyGroupIds() {
        setCustomStrategy();
        List<SurveyGroup> list = repository.findAll();
        List<Long> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach((i) -> ids.add(i.getId()));
        }
        return ids;
    }

    public List<SurveyGroupDto> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<SurveyGroup> list = repository.findAll((Specification<SurveyGroup>) (root, query, cb) -> root.get("id").in(ids), Sort.by("modifyTime").descending());
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<SurveyGroupDto> dtoList = mapToDto(list);
        afterMapToDto(list, dtoList);
        return dtoList;
    }
}
