package cn.hanyi.survey.service.response;

import cn.hanyi.survey.core.constant.DownloadType;
import cn.hanyi.survey.core.entity.BaseSurvey;
import cn.hanyi.survey.dto.DownloadDto;
import cn.hanyi.survey.dto.DownloadListDto;
import cn.hanyi.survey.dto.DownloadTaskDto;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.download.properties.AttachmentProperties;
import cn.hanyi.survey.workertrigger.ISurveyTaskTrigger;
import lombok.SneakyThrows;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.dto.usertask.UserTaskResponseDownloadDto;
import org.befun.auth.dto.usertask.UserTaskResultDto;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserTaskService;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.MapperService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.FileService;
import org.befun.task.annotation.TaskLock;
import org.befun.task.constant.TaskStatus;
import org.befun.task.dto.TaskProgressDto;
import org.befun.task.entity.TaskProgress;
import org.befun.task.repository.TaskProgressRepository;
import org.befun.task.service.TaskProgressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import javax.transaction.Transactional;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ResponseDownloadService {
    private static final Integer downloadRecordLimit = 5;
    private static final String downloadRecordKey = "download:record";
    private static final long size = 1024 * 1024 * 1024; //G->字节
    public static final String attachmentCapacityKey = "survey:download:attachment:system-capacity";

    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private ISurveyTaskTrigger surveyTaskTrigger;
    @Autowired
    private MapperService mapperService;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private TaskProgressRepository taskProgressRepository;

    @Autowired
    private TaskProgressService taskProgressService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private FileService fileService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private AttachmentProperties attachmentProperties;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private String getKey(Long surveyId) {
        return String.format("%s:%s:%s", downloadRecordKey, TenantContext.getCurrentUserId(), surveyId);
    }

    /**
     * 添加下载任务
     */
    @Transactional
    public TaskProgressDto downloadTask(Long surveyId, DownloadDto dto) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        surveyService.requireSurvey(surveyId);
        DownloadTaskDto downloadTaskDto = new DownloadTaskDto(surveyId, dto);
        TaskProgress progress = null;
        if (dto.getReDownload()) {
            //重新下载只更新
            Long taskProgressId = dto.getTaskProgressId();
            progress = updateTaskProgress(taskProgressId);
            taskProgressService.syncTaskToCache(progress);
        } else {
            progress = userTaskService.createTask(orgId, userId, UserTaskType.responseDownload, 0, downloadTaskDto, surveyId);
        }
        if (dto.getDownloadType() == DownloadType.ATTACHMENT) {
            surveyTaskTrigger.responseDownloadAttachment(orgId, userId, progress.getId(), surveyId, JsonHelper.toJson(dto));
        } else {
            surveyTaskTrigger.responseDownload(orgId, userId, progress.getId(), surveyId, JsonHelper.toJson(dto));
        }
        // 下载任务大于downloadRecordLimit就删掉  产品说的  谁来都不好使
        var taskProgresses = taskProgressRepository.findAll((root, query, builder) -> {
            return builder.and(
                    builder.equal(root.get("type"), UserTaskType.responseDownload.name()),
                    builder.equal(root.get("orgId"), orgId),
                    builder.equal(root.get("relationId"), surveyId));
        }, Sort.by(Sort.Direction.DESC, "createTime"));

        if (taskProgresses.size() > downloadRecordLimit) {
            List<TaskProgress> delList = taskProgresses.subList(downloadRecordLimit, taskProgresses.size());
            delList.forEach(task -> {
                UserTaskResultDto resultDto = JsonHelper.toObject(task.getResult(), UserTaskResultDto.class);
                Optional.ofNullable(resultDto)
                        .flatMap(resDto -> Optional.ofNullable(resDto.getResponseDownload()))
                        .ifPresent(responseDownloadDto -> fileService.delete(responseDownloadDto.getFileUrl()));
            });
            taskProgressRepository.deleteAll(delList);
        }

        // 用来记录弹窗展示的下载任务,不是重新下载才添加下载记录
        if (!dto.getReDownload()) {
            redisTemplate.opsForList().leftPush(getKey(surveyId), String.valueOf(progress.getId()));
        }
        redisTemplate.expire(getKey(surveyId), Duration.ofDays(2));

        return mapperService.map(progress, TaskProgressDto.class);
    }

    /**
     * 重新下载更新任务为默认值
     */
    public TaskProgress updateTaskProgress(Long taskProgressId) {
        TaskProgress taskProgress = taskProgressRepository.findById(taskProgressId).orElseThrow(() -> new BadRequestException("taskProgressId not exist"));
        taskProgress.setSuccessSize(0);
        taskProgress.setFailedSize(0);
        taskProgress.setTotalSize(0);
        taskProgress.setStatus(TaskStatus.INIT);
        taskProgress.setResult(null);
        TaskProgress result = taskProgressRepository.save(taskProgress);
        return result;
    }

    public void deletePop(Long surveyId) {
        redisTemplate.delete(getKey(surveyId));
    }

    public List<DownloadListDto> getDownloadList(Long surveyId, Boolean pop) {
        var userId = TenantContext.getCurrentUserId();

        // 只拉取最近downloadRecordLimit条
        var taskProgresses = taskProgressRepository.findAll((root, query, builder) -> {
            var condition = new ArrayList<Predicate>() {{
                add(builder.equal(root.get("type"), UserTaskType.responseDownload.name()));
                add(builder.equal(root.get("userId"), userId));
                add(builder.equal(root.get("relationId"), surveyId));
            }};

            if (pop != null && pop) {
                // 弹窗展示的下载任务
                var ids = Objects.requireNonNull(redisTemplate.opsForList().range(getKey(surveyId), 0, downloadRecordLimit - 1)).stream().map(Long::valueOf).collect(Collectors.toList());
                ids.add(-1l);
                condition.add(root.get("id").in(ids));
            }

            return builder.and(condition.toArray(new javax.persistence.criteria.Predicate[0]));
        }, PageRequest.of(0, downloadRecordLimit, Sort.by(Sort.Direction.DESC, "createTime"))).getContent();

        var surveyIds = taskProgresses.stream().map(taskProgress -> JsonHelper.toList(taskProgress.getParams(), DownloadTaskDto.class).get(0).getSurveyId()).collect(Collectors.toSet());
        Map<Long, String> surveyTitles = surveyService.getGroupMapByIds(surveyIds, BaseEntity::getId, BaseSurvey::getTitle);

        return taskProgresses.stream().map(taskProgress -> {
            var downloadListDto = new DownloadListDto();
            var downloadTaskDtos = JsonHelper.toList(taskProgress.getParams(), DownloadTaskDto.class);

            downloadListDto.setCreateTime(taskProgress.getCreateTime());
            downloadListDto.setTaskId(taskProgress.getId());
            downloadListDto.setSurveyName(surveyTitles.get(downloadTaskDtos.get(0).getSurveyId()));
            downloadListDto.setFileType(downloadTaskDtos.get(0).getDownloadDto().getDownloadType());
            downloadListDto.setStatus(taskProgress.getStatus());
            downloadListDto.setLastResponseIds(downloadTaskDtos.get(0).getDownloadDto().getResponseIds());

            // 正在进行的条数是没有刷到数据库的，所以要从redis里面拿
            // 完成后的是从数据库中获取
            if (taskProgress.getStatus().isCompleted()) {
                downloadListDto.setTotalCount(taskProgress.getTotalSize());
                downloadListDto.setProcessCount(taskProgress.getSuccessSize());
            } else {
                var taskProgressDto = userTaskService.getCacheProgress(taskProgress.getId());
                if (taskProgressDto != null) {
                    downloadListDto.setProcessCount((int) taskProgressDto.getSuccessSize());
                    downloadListDto.setTotalCount((int) taskProgressDto.getTotalSize());
                }
            }

            var result = JsonHelper.toObject(taskProgress.getResult(), UserTaskResultDto.class);
            if (ObjectUtils.isNotEmpty(result) && result.getResponseDownload() != null) {
                downloadListDto.setResult(result.getResponseDownload());
                downloadListDto.setFileSize(result.getResponseDownload().getFileSize());
            }
            return downloadListDto;
        }).collect(Collectors.toList());
    }

    public Boolean deleteDownloadTask(Long taskId) {
        taskProgressRepository.deleteById(taskId);
        return true;
    }

    /**
     * 设置下载总数
     */
    private void downloadInit(Long progressId, int total) {
        userTaskService.updateTaskTotalSize(progressId, total);
    }

    /**
     * 设置下载进度
     */
    private void downloadProgress(Long progressId, int appendCompletedSize) {
        userTaskService.appendTaskSuccessSize(progressId, appendCompletedSize);
    }

    /**
     * 标记下载完成
     */
    private void downloadCompleted(Long progressId, Long surveyId, String fileName, String fileUrl) {
        userTaskService.successTask(progressId, result -> {
            UserTaskResponseDownloadDto dto = new UserTaskResponseDownloadDto();
            dto.setSurveyId(surveyId);
            dto.setFileName(fileName);
            dto.setFileUrl(fileUrl);
            result.setResponseDownload(dto);
        });
    }

    /**
     * 下载
     */
    public void download(Long progressId, Long surveyId, String data) {
        DownloadDto dto = JsonHelper.toObject(data, DownloadDto.class);
        downloadInit(progressId, 1000);
        downloadProgress(progressId, 1000);
        downloadCompleted(progressId, surveyId, "答卷数据.zip", "http://127.0.0.1/111.csv");
    }

    /**
     * 检测附件下载是否满足要求
     * 如果有限制的话 直接抛出错误 携带信息返回给前端
     */
    @SneakyThrows
    public void checkAttachmentLimit(Long surveyId, DownloadDto dto) {
        AppVersion version = organizationService.parseOrgVersion(TenantContext.getCurrentTenant());
        Integer limit = switch (version) {
            case PROFESSION -> attachmentProperties.getProfession();
            case UPDATE -> attachmentProperties.getUpdate();
            case FREE -> attachmentProperties.getFree();
            case BASE -> attachmentProperties.getBase();
            default -> 0;
        };
        //字节大小
        Long fileSize = computeAttachmentSize(surveyId, dto.getResponseIds());
        if (fileSize <= 0) {
            throw new BadRequestException("问卷暂无有效的填答附件");
        }
        if (fileSize > limit * size) {
            String freeMsg = attachmentProperties.getFreeMsg()
                    .replace("{ATTACHMENT_VERSION}", version.getLabel())
                    .replace("{ATTACHMENT_LIMIT}", limit.toString())
                    .replace("{CURRENT_ATTACHMENT_SIZE}", String.format("%.2f", (double) (fileSize / size)));
            String paidMsg = attachmentProperties.getPaidMsg()
                    .replace("{ATTACHMENT_VERSION}", version.getLabel())
                    .replace("{ATTACHMENT_LIMIT}", limit.toString())
                    .replace("{CURRENT_ATTACHMENT_SIZE}", String.format("%.2f", (double) (fileSize / size)));
            String msg = version == AppVersion.FREE ? freeMsg : paidMsg;
            throw new BusinessException(msg);
        }
        if (checkAttachmentCapacity(fileSize)) {
            throw new BadRequestException("当前系统的下载人数较多，请稍后再试");
        }

    }

    /**
     * 下载容量大小是否大于系统容量
     *
     * @param currentFileSize 单位byte
     */
    @TaskLock(key = "attachment-capacity-lock")
    private boolean checkAttachmentCapacity(long currentFileSize) {
        //系统容量
        long systemCapacity = attachmentProperties.getSystemCapacity() * size;
        //下载中的容量
        String attachmentCapacity = redisTemplate.opsForValue().get(attachmentCapacityKey);
        if (attachmentCapacity == null) {
            if (currentFileSize > systemCapacity) {
                return true;
            }
            redisTemplate.opsForValue().set(attachmentCapacityKey, currentFileSize + "", Duration.ofHours(1));
            return false;
        }
        Long capacity = Long.parseLong(attachmentCapacity) + currentFileSize;
        if (capacity > systemCapacity) {
            return true;
        }
        redisTemplate.opsForValue().increment(attachmentCapacityKey, currentFileSize);
        return false;
    }

    private Long computeAttachmentSize(Long surveyId, List<Long> responseIds) {

        String condition = "";
        StringJoiner joiner = new StringJoiner(",", "(", ")");
        if (CollectionUtils.isNotEmpty(responseIds)) {
            responseIds.forEach(id -> joiner.add(id.toString()));
            condition = "AND sr.id in " + joiner;
        }

        String sql = String.format("SELECT JSON_EXTRACT(src.s_val, '$[*].size') as size\n" +
                "FROM survey_response_cell src \n" +
                "join survey_response sr on sr.id=src.`r_id`\n" +
                "WHERE src.s_id = %s %s and src.type in (17,30) and sr.`status`=1;", surveyId, condition);
        // FIXME: 无法使用mysql直接统计list对象的size
        List<String> sizeList = jdbcTemplate.queryForList(sql, String.class);

        long sum = sizeList.stream().map(x -> {
            List<Long> list = JsonHelper.toList(x, Long.class);
            return list.stream().mapToLong(Long::longValue).sum();
        }).mapToLong(Long::longValue).sum();

        return sum;
    }
}
