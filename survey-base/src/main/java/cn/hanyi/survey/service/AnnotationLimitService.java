package cn.hanyi.survey.service;

import cn.hanyi.survey.core.entity.SurveyChannel;
import org.befun.core.limiter.annotation.LimiterTryConsume;
import org.springframework.stereotype.Service;

import javax.validation.constraints.NotNull;

@Service
public class AnnotationLimitService {

    /**
     * 渠道答题开启了IP限制
     *
     * @param channel
     */
    @LimiterTryConsume(
            key = "'ip.' + #channel.Id + '.'+ #ip",
            condition = "#channel.enableIpLimit",
            max = 1,
            message = "您的IP地址已填答过该问卷"
    )
    public void consumeIp(@NotNull SurveyChannel channel) {
    }
}
