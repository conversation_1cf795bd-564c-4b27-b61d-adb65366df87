package cn.hanyi.survey.service;

import cn.hanyi.survey.client.constant.SurveyLotteryConstant;
import cn.hanyi.survey.client.exception.LotteryException;
import cn.hanyi.survey.client.properties.SendRedPackProperties;
import cn.hanyi.survey.client.utils.RedPackUtils;
import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.entity.SurveyLotteryPrize;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeDto;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeCodeRepository;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeRepository;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.service.OrganizationService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/9/21 15:15:27
 */
@Service
public class SurveyLotteryPrizeService extends CustomEmbeddedService<SurveyLotteryPrize, SurveyLotteryPrizeDto, SurveyLotteryPrizeRepository> {

    @Autowired
    private SurveyLotteryPrizeRepository surveyLotteryPrizeRepository;
    @Autowired
    private SurveyLotteryPrizeCodeRepository surveyLotteryPrizeCodeRepository;

    @Autowired
    private SurveyLotteryPrizeWinnerRepository surveyLotteryPrizeWinnerRepository;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SendRedPackProperties redPackProperties;

    @Autowired
    private OrganizationService organizationService;

    @Override
    protected Object requireParent(long l) {
        return null;
    }

    private void checkMoney(SurveyLotteryPrizeDto dto, SurveyLotteryPrize entity) {
        AppVersion version = organizationService.parseOrgVersion(TenantContext.getCurrentTenant());
        Integer maxMoney = dto.getMaxMoney() != null ? dto.getMaxMoney() : entity.getMaxMoney();
        Integer minMoney = dto.getMinMoney() != null ? dto.getMinMoney() : entity.getMinMoney();
        Integer money = dto.getMoney() != null ? dto.getMoney() : entity.getMoney();
        Integer totalMoney = dto.getTotalMoney() != null ? dto.getTotalMoney() : entity.getTotalMoney();
        Integer number = dto.getNumber() != null ? dto.getNumber() : entity.getNumber();


        switch (dto.getType() != null ? dto.getType() : entity.getType()) {
            case RANDOM_RED_PACK:
                // Use entity values if dto values are null
                if (maxMoney == null || minMoney == null || minMoney < 1 || maxMoney > version.getRedPackMaxMoney()) {
                    throw new BadRequestException(String.format("单个红包金额须在1-%s以内，请重新输入个数或调整金额", version.getRedPackMaxMoney() / 100));
                }
                int averageMoney = totalMoney / number;
                if (averageMoney < minMoney) {
                    throw new BadRequestException("单个红包最小金额小于（总金额/红包个数），请重新输入个数或调整金额");
                }

                if (averageMoney > maxMoney) {
                    throw new BadRequestException("单个红包最大金额超过（总金额/红包个数），请重新输入个数或调整金额");
                }
                break;
            case FIXED_RED_PACK:
                // Use entity values if dto values are null
                if (money == null || money < 1 || money > version.getRedPackMaxMoney()) {
                    throw new BadRequestException(String.format("单个红包金额须在1-%s以内，请重新输入个数或调整金额", version.getRedPackMaxMoney() / 100));
                }
                if (money < minMoney) {
                    throw new BadRequestException("单个红包最小金额小于" + (int) Math.ceil(money / 100.0) + "元，请重新输入个数或调整金额");
                }
                if (money > maxMoney) {
                    throw new BadRequestException("单个红包最大金额超过" + (int) Math.ceil(money / 100.0) + "元，请重新输入个数或调整金额");
                }
                break;
            default:
                break;
        }


    }

    @Override
    public SurveyLotteryPrizeDto updateOneEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, SurveyLotteryPrizeDto change) {
        checkCodeLength(change.getCodes());
        SurveyLotteryPrizeDto surveyLotteryPrizeDto = null;
        try {
            //修改奖品参数加锁
            redisTemplate.opsForValue().set(String.format(SurveyLotteryConstant.UPDATE_PRIZE_LOCK, embeddedId), "1");
            SurveyLotteryPrize prize = surveyLotteryPrizeRepository.findById(embeddedId).orElseThrow(() -> new LotteryException("该奖品不存在"));
            if (change.getType() != null) {
                switch (change.getType()) {
                    case VIRTUAL_PRIZE:
                        virtualPrize(change, prize, embeddedId);
                        break;
                    case PHYSICAL_PRIZE:
                        physicalPrize(prize);
                        break;
                    case RANDOM_RED_PACK:
                        checkMoney(change, prize);
                        randomRedPack(change, entityId);
                        break;
                    case FIXED_RED_PACK:
                        checkMoney(change, prize);
                        fixedRedPack(change, entityId);
                        break;
                }
            }
            surveyLotteryPrizeDto = super.updateOneEmbeddedMany(entityId, embeddedMapperBy, embeddedId, change);
        } catch (Exception e) {
            throw new BadRequestException(e.getMessage());
        } finally {
            redisTemplate.delete(String.format(SurveyLotteryConstant.UPDATE_PRIZE_LOCK, embeddedId));
        }
        return surveyLotteryPrizeDto;
    }

    /**
     * 虚拟奖品
     *
     * @param change
     * @param prize
     * @param prizeId
     */
    public void virtualPrize(SurveyLotteryPrizeDto change, SurveyLotteryPrize prize, Long prizeId) {
        if (change.getCodes() != null) {
            List<String> prizeCode = surveyLotteryPrizeWinnerRepository.findPrizeCodeByPrizeId(prizeId);
            Set<String> codes = change.getCodes();
            //去掉已经被抽中的兑换码
            codes.removeAll(prizeCode);
            if (!codes.isEmpty()) {
                redisTemplate.opsForList().getOperations().delete(String.format(SurveyLotteryConstant.PRIZE_CODES, prizeId));
                redisTemplate.opsForList().rightPushAll(String.format(SurveyLotteryConstant.PRIZE_CODES, prizeId), codes);
            }
        }
        //切换奖品类型时 之前的奖品中奖数量清0
        if (!LotteryPrizeType.VIRTUAL_PRIZE.equals(prize.getType())) {
            prize.setWinnerNum(0);
            surveyLotteryPrizeRepository.save(prize);
        }
    }

    /**
     * 实物奖品
     *
     * @param prize
     */
    public void physicalPrize(SurveyLotteryPrize prize) {
        //切换奖品类型时 之前的奖品中奖数量清0
        if (!LotteryPrizeType.PHYSICAL_PRIZE.equals(prize.getType())) {
            prize.setWinnerNum(0);
            surveyLotteryPrizeRepository.save(prize);
        }
    }

    /**
     * 提前将每个红包保存到redis，抽奖时直接从redis中取
     *
     * @param dto
     * @param lotteryId
     */
    public void saveRedPack2Redis(SurveyLotteryPrizeDto dto, Long lotteryId) {
        //发红包：创建红包渠道，将n个小红包放到redis
        //红包个数
        int number = dto.getNumber();
        //总金额
        int totalMoney = dto.getTotalMoney();
        //最小金额1元
        int minMoney = 100;
        //最大金额200元
        int maxMoney = 20000;
        //随机红包
        if (LotteryPrizeType.RANDOM_RED_PACK.equals(dto.getType())) {
            List<Integer> list = RedPackUtils.RandomRedPack(totalMoney, minMoney, maxMoney, number);
            List<String> values = list.stream().map(x -> x.toString()).collect(Collectors.toList());
            String key = String.format(SurveyLotteryConstant.RED_PACT_KEY, lotteryId);
            redisTemplate.opsForList().leftPop(key);
            redisTemplate.opsForList().leftPushAll(key, values);
        } else if (LotteryPrizeType.FIXED_RED_PACK.equals(dto.getType())) {//固定金额红包

        }

    }

    /**
     * 随机红包
     *
     * @param dto
     * @param lotteryId
     */
    public void randomRedPack(SurveyLotteryPrizeDto dto, Long lotteryId) {
        Assert.notNull(dto.getNumber(), "number is not null");
        Assert.notNull(dto.getTotalMoney(), "totalMoney is not null");
//        checkRedPackMoney(dto.getTotalMoney() / dto.getNumber());
        //发红包：创建红包渠道，将n个小红包放到redis
        //红包个数
        int number = dto.getNumber();
        //总金额
        int totalMoney = dto.getTotalMoney();
        //最小金额1元
        int minMoney = dto.getMinMoney();
        //最大金额
        int maxMoney = dto.getMaxMoney();
        List<Integer> list = RedPackUtils.RandomRedPack(totalMoney, minMoney, maxMoney, number);
        List<String> values = list.stream().map(x -> x.toString()).collect(Collectors.toList());
        String key = String.format(SurveyLotteryConstant.RED_PACT_KEY, lotteryId);
        redisTemplate.delete(key);
        redisTemplate.opsForList().leftPushAll(key, values);
    }

    /**
     * 普通红包
     *
     * @param dto
     * @param lotteryId
     */
    public void fixedRedPack(SurveyLotteryPrizeDto dto, Long lotteryId) {
        Assert.notNull(dto.getNumber(), "number is not null");
        Assert.notNull(dto.getMoney(), "money is not null");
//        checkRedPackMoney(dto.getMoney());
        //红包个数
        int number = dto.getNumber();
        //单个红包金额
        int money = dto.getMoney();
        List<Integer> list = RedPackUtils.fixedRedPack(money, number);
        List<String> values = list.stream().map(x -> x.toString()).collect(Collectors.toList());
        String key = String.format(SurveyLotteryConstant.RED_PACT_KEY, lotteryId);
        redisTemplate.delete(key);
        redisTemplate.opsForList().leftPushAll(key, values);
    }

    /**
     * 单个兑换码长度校验
     *
     * @param codes
     */
    void checkCodeLength(Set<String> codes) {
        if (codes == null) {
            return;
        }
        if (codes.size() > 1000) {
            throw new LotteryException("兑换码个数大于1000");
        }
        for (String code : codes) {
            if (code.length() > 50) {
                throw new LotteryException("单个兑换码长度不能超50");
            }
        }
    }

    /**
     * 校验单个红包金额大小
     *
     * @param money 单个红包金额
     */
    public void checkRedPackMoney(int money) {
        if (money > redPackProperties.getMaxMoney() || money < redPackProperties.getMinMoney()) {
            throw new BadRequestException("红包金额须在1-20以内");
        }
    }

    /**
     * 清楚奖品 默认谢谢参与
     *
     * @param prizeId
     * @return
     */
    public Boolean clearPrizes(Long prizeId) {
        SurveyLotteryPrize prize = surveyLotteryPrizeRepository.getOne(prizeId);
        prize.setPrizeName("谢谢参与");
        prize.setIsPrize(false);
        prize.setImage(null);
        prize.setNumber(null);
        prize.setPercent(null);
        prize.setType(LotteryPrizeType.VIRTUAL_PRIZE);
        prize.setCodes(null);
        prize.setIsAddress(false);
        prize.setAddress("");
        prize.setWinnerNum(0);
        surveyLotteryPrizeRepository.save(prize);
        if (LotteryPrizeType.VIRTUAL_PRIZE.equals(prize.getType())) {
            redisTemplate.delete(String.format(SurveyLotteryConstant.PRIZE_CODES, prize.getId()));
        }
        return true;
    }
}
