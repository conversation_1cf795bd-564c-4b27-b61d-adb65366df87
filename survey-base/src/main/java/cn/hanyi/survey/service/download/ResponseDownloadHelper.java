package cn.hanyi.survey.service.download;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.common.file.storage.MockMultipartFile;
import cn.hanyi.common.file.storage.MockMultipartFileStream;
import cn.hanyi.survey.core.constant.DownloadFromType;
import cn.hanyi.survey.core.constant.DownloadType;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.dto.AttachmentDownloadDto;
import cn.hanyi.survey.core.dto.FileUploadDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.utilis.DownloadUtils;
import cn.hanyi.survey.core.utilis.EasyExcelUtils;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.dto.DownloadDto;
import cn.hanyi.survey.service.ChannelService;
import cn.hanyi.survey.service.ResponseService;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.download.dto.*;
import cn.hanyi.survey.service.download.properties.AttachmentProperties;
import cn.hanyi.survey.service.response.ResponseDownloadService;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.handler.context.RowWriteHandlerContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.service.UserTaskService;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static cn.hanyi.survey.core.constant.question.QuestionType.*;

@Slf4j
@Component
public class ResponseDownloadHelper {

	@Value("${survey.download.batch:500}")
	private int downloadBatch;
	@Autowired
	private SurveyService surveyService;
	@Autowired
	private ChannelService channelService;
	@Autowired
	private ResponseService responseService;
	@Autowired
	private SurveyResponseRepository surveyResponseRepository;
	@Autowired
	private SurveyResponseCellRepository surveyResponseCellRepository;
	@Autowired
	private DownloadResponseParser downloadResponseParser;
	@Autowired
	private DownloadQuestionParser downloadQuestionParser;
	@Value("${hanyi.common.file-storage.old-prefix:http://dev-assets.surveyplus.cn/lite/}")
	private String oldPrefix;
	@Value("${hanyi.common.file-storage.old-base-path:lite/}")
	private String oldBasePath;

	@Autowired(required = false)
	private final List<IDownloadExtHelper<?>> downloadExtHelpers = new ArrayList<>();
	@Autowired
	private FileStorageService fileStorageService;
	@Autowired
	private EntityManager entityManager;
	@Autowired
	private UserTaskService userTaskService;
	@Autowired
	private ExecutorService executorService;

	@Autowired
	private JdbcTemplate jdbcTemplate;

	@Autowired
	private StringRedisTemplate redisTemplate;

	@Autowired
	private AttachmentProperties attachmentProperties;

	public void downloadToResponse(Long surveyId, DownloadDto dto, HttpServletResponse response) {
		List<Long> responseIds = dto.getResponseIds();
		if (dto.getProcessAll()) {
			responseIds = responseService.getSearchResponseIds(surveyId, dto.getQueryDto());
		}
		downloadToResponse(surveyId, dto.getFrom(), dto.getBatchSize(), dto.getDownloadType(), responseIds, response);
	}

	public void downloadToResponse(Long surveyId, DownloadFromType from, Integer batchSize, DownloadType downloadType, List<Long> responseIds, HttpServletResponse response) {
		ResponseDownloadFile zip = download(null, surveyId, from, batchSize, downloadType, responseIds);
		DownloadUtils.responseRequest(response, zip.getFileName(), zip.getBytes());
	}

	/**
	 * 下载答卷模板
	 *
	 * @param surveyId
	 * @param downloadType
	 * @param response
	 * @throws IOException
	 */
	public void downloadToResponseTemplate(Long surveyId, DownloadType downloadType, HttpServletResponse response) throws IOException {
		ResponseDownloadContext context = buildTemplateContext(null, surveyId, downloadType);
		copyColumns(context, List.of(), context.getLabelHeaders(), context.getLabelRowMap().values(), context.getFileLabel().getHeaders(), context.getFileLabel().getRows());
		copyColumns(context, List.of(), context.getCodeHeaders(), context.getCodeRowMap().values(), context.getFileCode().getHeaders(), context.getFileCode().getRows());

		// copy ext data
		downloadExtHelpers.forEach(extHelper -> {
			extHelper.copyColumns(context);
		});

		String fileName = URLEncoder.encode(context.getFileLabel().getFileName(), "UTF-8").replaceAll("\\+", "%20");
		response.setContentType("application/vnd.ms-excel");
		response.setCharacterEncoding("utf-8");
		response.setHeader("Content-Disposition", "attachment; filename=" + fileName);

		ByteArrayOutputStream byteArrayOutputStream = EasyExcelUtils.easyExcel(context.getLabelHeaders(), List.of(), null);
		response.getOutputStream().write(byteArrayOutputStream.toByteArray());
	}

	public FileInfo downloadToUpload(Long taskId, Long surveyId, DownloadDto dto) {
		List<Long> responseIds = dto.getResponseIds();
		if (dto.getProcessAll()) {
			responseIds = responseService.getSearchResponseIds(surveyId, dto.getQueryDto());
		}
		return downloadToUpload(taskId, surveyId, dto.getFrom(), dto.getBatchSize(), dto.getDownloadType(), responseIds);
	}

    private InputStream readFileInputStream(String fileUrl) throws IOException {
        InputStream is = null;
        try {
            is = new ByteArrayInputStream(fileStorageService.download(fileUrl).bytes());
            return is;
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

	public ResponseDownloadFile download(Long taskId, Long surveyId, DownloadDto dto) {
        List<Long> responseIds = dto.getResponseIds();
        if (dto.getProcessAll()) {
            responseIds = responseService.getSearchResponseIds(surveyId, dto.getQueryDto());
        }
        return download(taskId, surveyId, dto.getFrom(), dto.getBatchSize(), dto.getDownloadType(), responseIds);
    }

	public void downloadToResponseFileZip(Long surveyId, DownloadDto downloadDto, HttpServletResponse response) throws IOException {

        List<AttachmentDownloadDto> filePathList = getFilePathList(surveyId, downloadDto.getResponseIds());
        List<ResponseDownloadFile> responseDownloadFileList = new ArrayList<>();
        for (AttachmentDownloadDto dto : filePathList) {
            List<FileUploadDto> fileUploadDtos = JsonHelper.toList(dto.getVal(), FileUploadDto.class);
            for (FileUploadDto fileUploadDto : fileUploadDtos) {
	            String tempName = fileUploadDto.getFileName().substring(0, fileUploadDto.getFileName().lastIndexOf("."));
	            String url = fileUploadDto.getPath().replace(tempName, URLEncoder.encode(tempName, StandardCharsets.UTF_8));
	            byte[] bytes = readFileInputStream(url).readAllBytes();
	            responseDownloadFileList.add(new ResponseDownloadFile(fileUploadDto.getFileName(), bytes));
            }
        }
        Survey survey = surveyService.requireSurvey(surveyId);
        String filename = survey.getTitle() + "-" + System.currentTimeMillis() + ".zip";
        ResponseDownloadFile file = zipFile(filename, responseDownloadFileList);
        DownloadUtils.responseRequest(response, filename, file.getBytes());
    }

    public FileInfo downloadToUpload(Long taskId, Long surveyId, DownloadFromType fromType, Integer batchSize, DownloadType downloadType, List<Long> responseIds) {
        ResponseDownloadFile zip = download(taskId, surveyId, fromType, batchSize, downloadType, responseIds);
        var file = new MockMultipartFile(zip.getFileName(), zip.getFileName(), MediaType.MULTIPART_FORM_DATA_VALUE, zip.getBytes());
        var pre = fileStorageService.of(file);
        pre.setPath(FileService.PRIVATE_PATH);
        pre.setSaveFilename(URLEncoder.encode(zip.getFileName(), StandardCharsets.UTF_8));
        return pre.upload();
    }


	public void delFile(String filename) {
		File file = new File(this.getFilePath(filename));
		if (file.exists()) {
			file.delete();
		}
	}

	public void decrementAttachmentSize(long fileSize) {
		String attachmentCapacityKey = ResponseDownloadService.attachmentCapacityKey;
		String attachmentCapacity = redisTemplate.opsForValue().get(attachmentCapacityKey);
		if (Long.parseLong(attachmentCapacity) < fileSize) {
			redisTemplate.opsForValue().set(attachmentCapacityKey, "0");
		} else {
			redisTemplate.opsForValue().decrement(attachmentCapacityKey, fileSize);
		}
	}

	public Long compressZipFile(Long taskId, Long surveyId, String filename, List<Long> responseIds) {
		File zipFile = new File(this.getFilePath(filename));
		List<AttachmentDownloadDto> attachmentDownloadDtos = this.getFilePathList(surveyId, responseIds);
		int count = attachmentDownloadDtos.stream().map(dto -> JsonHelper.toList(dto.getVal(), Map.class).size()).mapToInt(Integer::intValue).sum();
		log.info("附件数量：{}", count);
		if (taskId != null && taskId > 0) {
			userTaskService.updateTaskTotalSize(taskId, count);
		}
		AtomicInteger successNum = new AtomicInteger(0);
		AtomicInteger failNum = new AtomicInteger(0);
		long fileSize = 0;
		try (FileOutputStream fos = new FileOutputStream(zipFile);
		     ZipArchiveOutputStream zos = new ZipArchiveOutputStream(fos)) {
			for (AttachmentDownloadDto dto : attachmentDownloadDtos) {
				String val = dto.getVal();
				List<FileUploadDto> fileUploadDtos = JsonHelper.toList(val, FileUploadDto.class);
				AtomicInteger seq = new AtomicInteger(1);
				for (FileUploadDto fileUploadDto : fileUploadDtos) {
					InputStream is = null;
					try {
						fileSize += Long.parseLong(fileUploadDto.getSize());
						String name = dto.getSequence() + "-" + dto.getCode() + "-" + seq.getAndIncrement() + "-" + fileUploadDto.getFileName();
						ZipArchiveEntry zipEntry = new ZipArchiveEntry(name);
						zos.putArchiveEntry(zipEntry);
						String tempName = fileUploadDto.getFileName().substring(0, fileUploadDto.getFileName().lastIndexOf("."));
                        String url = fileUploadDto.getPath();
						is = readFileInputStream(url);
						zos.write(is.readAllBytes());
						zos.flush();
						if (taskId != null && taskId > 0) {
							userTaskService.updateTaskSuccessSize(taskId, successNum.incrementAndGet());
						}
					} catch (Exception e) {
						if (taskId != null && taskId > 0) {
							userTaskService.updateTaskFailedSize(taskId, failNum.incrementAndGet());
						}
						e.printStackTrace();
					} finally {
						if (is != null) {
							is.close();
						}
						zos.closeArchiveEntry();
					}
				}
			}
			zos.finish();

		} catch (Exception e) {
			e.printStackTrace();
		}
		return fileSize;
	}

	/**
	 * /data/attachement/
	 *
	 * @return
	 */
	public String getFilePath(String filename) {
		String saveDir = attachmentProperties.getSaveDir();
		if (!saveDir.endsWith("/")) {
			saveDir = saveDir + "/";
		}
		File file = new File(saveDir);
		if (!file.exists()) {
			file.mkdirs();
		}
		return saveDir + filename;
	}

	private List<AttachmentDownloadDto> getFilePathList(Long surveyId, List<Long> responseIds) {
		String condition = "";
        StringJoiner joiner = new StringJoiner(",", "(", ")");
        if (CollectionUtils.isNotEmpty(responseIds)) {
            responseIds.forEach(id -> joiner.add(id.toString()));
            condition = "AND sr.id in " + joiner;
        }
        String sql = String.format("SELECT\n" +
                " sr.sequence sequence,\n" +
                " sq.code code,\n" +
                " src.q_id questionId,\n" +
                " src.r_id responseId,\n" +
                " src.s_val val\n" +
                " FROM " +
                "  survey_question sq\n" +
                "  JOIN survey_response_cell src ON sq.id=src.q_id\n" +
                "  JOIN survey_response sr ON sr.id = src.`r_id` \n" +
                " WHERE " +
                "  src.s_id = %s \n" +
                "  %s  \n" +
                "  AND src.type in(17,30) \n" +
                "  AND sr.`status` = 1;", surveyId, condition);
        log.info("附件下载sql：" + sql);
        List<AttachmentDownloadDto> dtoList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AttachmentDownloadDto.class));
        return dtoList;
    }

    public FileInfo downloadToUploadFileZip(Long taskId, Long surveyId, DownloadDto dto) {
        long fileSize = 0L;
        Survey survey;
        String filename = "";
        FileInfo fileInfo;
        try {
            List<Long> responseIds = dto.getResponseIds();
            if (dto.getProcessAll()) {
                responseIds = responseService.getSearchResponseIds(surveyId, dto.getQueryDto());
            }
            survey = surveyService.requireSurvey(surveyId);
            filename = survey.getTitle() + "-" + System.currentTimeMillis() + ".zip";
            fileSize = this.compressZipFile(taskId, surveyId, filename, responseIds);
            FileInputStream is = new FileInputStream(this.getFilePath(filename));
            var file = new MockMultipartFileStream(filename, filename, MediaType.MULTIPART_FORM_DATA_VALUE, is);
            var pre = fileStorageService.of(file);
            pre.setPath(FileService.PRIVATE_PATH);
            pre.setSaveFilename(URLEncoder.encode(filename, StandardCharsets.UTF_8));
            fileInfo = pre.upload();
            log.info("附件上传成功：{}", JsonHelper.toJson(fileInfo));
        } catch (IOException e) {
            throw new BadRequestException("读取文件失败" + e.getMessage());
        } finally {
            this.delFile(filename);
            decrementAttachmentSize(fileSize);
            log.info("附件删除成功");
        }
        return fileInfo;
    }

    /**
     * 进度计算
     * total 答卷数
     * 数据查询 50%
     * 数据导出 50%
     */
    public ResponseDownloadFile download(Long taskId, Long surveyId, DownloadFromType fromType, Integer batchSize, DownloadType downloadType, List<Long> responseIds) {
        batchSize = batchSize == null ? downloadBatch : batchSize;
        ResponseDownloadContext context = buildContext(taskId, surveyId, downloadType);
        context.setFromType(fromType);
        if (CollectionUtils.isNotEmpty(responseIds)) {
            List<SurveyResponse> list = findResponseByIds(surveyId, responseIds);
            progressStartQuery(context, list.size());
            download0(context, list, false);
            progressUpdateQuery(context, list.size() / 2);
        } else if (batchSize <= 0) {
            List<SurveyResponse> list = findAllResponse(surveyId);
            progressStartQuery(context, list.size());
            download0(context, list, true);
            progressUpdateQuery(context, list.size() / 2);
        } else {
            progressStartQuery(context, countAllResponse(surveyId));
            pageableDownload(context, batchSize);
        }

        ResponseDownloadFile file;
        if (context.getDownloadType() == DownloadType.JSON || context.getDownloadType() == DownloadType.SAV) {
            // write json
            file = writeJson(context);
        } else {
            // write csv|excel
            file = writeExcelOrCsv(context);
        }
        progressEnd(context);
        return file;
    }

    private void pageableDownload(ResponseDownloadContext context, int batchSize) {
        Long minId = 0L;
        boolean hasNext = true;
        List<SurveyResponse> list;
        List<ResponseStatus> status = new ArrayList<>();
        status.add(ResponseStatus.FINAL_SUBMIT);
        if (context.getFromType() != null & !DownloadFromType.Banner.equals(context.getFromType())) {
            status.add(ResponseStatus.INVALID);
        }
        do {
            list = findResponse(context.getSurveyId(), minId, batchSize, status);
            if (CollectionUtils.isNotEmpty(list)) {
                download0(context, list, true);
                minId = list.get(list.size() - 1).getId();
                progressUpdateQuery(context, list.size() / 2);
            } else {
                hasNext = false;
            }
        } while (hasNext);
    }

    /**
     * 查询数据时初始化总数
     */
    private void progressStartQuery(ResponseDownloadContext context, int total) {
        context.setTotal(total);
        log.info("答卷下载：{}，初始化答卷总数：{}", context.getSurveyId(), total);
        Long taskId = context.getTaskId();
        if (taskId != null && taskId > 0) {
            userTaskService.updateTaskTotalSize(taskId, total);
        }
    }

    /**
     * 查询数据时更新进度
     */
    private void progressUpdateQuery(ResponseDownloadContext context, int append) {
        context.appendProgressed(append);
        log.info("答卷下载：{}，总数：{}，进度数：{}", context.getSurveyId(), context.getTotal(), context.getProgressed());
        Long taskId = context.getTaskId();
        if (taskId != null && taskId > 0) {
            userTaskService.updateTaskSuccessSize(taskId, context.getProgressed());
        }
    }

    /**
     * 导出数据时初始化总行数
     */
    private void progressStartWrite(ResponseDownloadContext context) {
        List<ResponseExportFile> list = new ArrayList<>();
        list.add(context.getFileCode());
        list.add(context.getFileLabel());
        list.addAll(context.getExtFileMap().values());
        int totalRows = list.size();
        for (ResponseExportFile file : list) {
            totalRows += file.getRows().size();
        }
        context.setTotalRows(totalRows);
        context.setLogPointStep(totalRows * 1.0 / 50);
        context.setLogPoint(context.getLogPointStep());
        log.info("答卷下载：{}，初始化总行数：{}", context.getSurveyId(), totalRows);
    }

    /**
     * 导出数据时更新进度，每行数据都会调用一次，这里需要减少日志的打印和其他操作
     */
    private void progressUpdateWrite(ResponseDownloadContext context) {
        context.plusProgressedRows();
        if (context.getProgressedRows() >= context.getLogPoint()) {
            context.calcNextLogPointAndProgressed();
            log.info("答卷下载：{}，总数：{}，进度数：{}", context.getSurveyId(), context.getTotal(), context.getProgressed());
            Long taskId = context.getTaskId();
            if (taskId != null && taskId > 0) {
                userTaskService.updateTaskSuccessSize(taskId, context.getProgressed());
            }
        }
    }

    private void progressEnd(ResponseDownloadContext context) {
        log.info("答卷下载：{}，总数：{}，已完成", context.getSurveyId(), context.getTotal());
        Long taskId = context.getTaskId();
        if (taskId != null && taskId > 0) {
            userTaskService.updateTaskSuccessSize(taskId, context.getTotal());
        }
    }

    private ResponseDownloadContext buildContext(Long taskId, Long surveyId, DownloadType downloadType) {
        Survey survey = surveyService.requireSurvey(surveyId);
        List<SurveyChannel> channels = channelService.getChannelList(surveyId);
        ResponseDownloadContext context = new ResponseDownloadContext(taskId, surveyId, downloadType, survey, channels);
        // build file
        context.setFileLabel(new ResponseExportFile(context.getSafeTitle() + "_原始数据" + context.getDownloadType().getSuffix()));
        context.setFileCode(new ResponseExportFile(context.getSafeTitle() + "_编码数据" + context.getDownloadType().getSuffix()));
        // build headers
        downloadResponseParser.buildGroup(context);
        downloadQuestionParser.buildGroup(context);

        // v1.10.3 如果是 json 文件，则只需要答卷数据
        if (context.hasExtFiles()) {
            // build ext file
            downloadExtHelpers.forEach(extHelper -> {
                Optional.ofNullable(extHelper.buildFile(context)).ifPresent(file -> {
                    context.getExtFileMap().put(extHelper.extType(), file);
                });
            });
        }
        return context;
    }

    /**
     * 下载答卷导入模板
     * @param taskId
     * @param surveyId
     * @param downloadType
     * @return
     */
    private ResponseDownloadContext buildTemplateContext(Long taskId, Long surveyId, DownloadType downloadType) {
        Survey survey = surveyService.requireSurvey(surveyId);
        ResponseDownloadContext context = new ResponseDownloadContext(taskId, surveyId, downloadType, survey, null);
        // build file
        context.setFileLabel(new ResponseExportFile(context.getSafeTitle() + "_答卷模板" + context.getDownloadType().getSuffix()));
        context.setFileCode(new ResponseExportFile(context.getSafeTitle() + "_答卷模板" + context.getDownloadType().getSuffix()));
        // build headers
        downloadResponseParser.buildTemplateGroup(context);
        downloadQuestionParser.buildTemplateGroup(context);

        // v1.10.3 如果是 json 文件，则只需要答卷数据
        if (context.hasExtFiles()) {
            // build ext file
            downloadExtHelpers.forEach(extHelper -> {
                Optional.ofNullable(extHelper.buildFile(context)).ifPresent(file -> {
                    context.getExtFileMap().put(extHelper.extType(), file);
                });
            });
        }
        return context;
    }

    public List<SurveyQuestion> buildTemplateSurveyQuestion(ResponseImportContext importContext) {
        Survey survey = surveyService.requireSurvey(importContext.getSurveyId());
        importContext.setSurvey(survey);
        ResponseDownloadContext context = new ResponseDownloadContext(null, importContext.getSurveyId(), null, survey, null);
        downloadQuestionParser.buildTemplateGroup(context);
        return context.getFilterAndSortQuestions();
    }

    private void download0(ResponseDownloadContext context, List<SurveyResponse> responseList, boolean sequential) {
        if (CollectionUtils.isEmpty(responseList)) {
            return;
        }
        List<ResponseStatus> statusList = new ArrayList<>();
        statusList.add(ResponseStatus.FINAL_SUBMIT);
        if (context.getFromType() != null & !DownloadFromType.Banner.equals(context.getFromType())) {
            statusList.add(ResponseStatus.INVALID);
        }
        // 填充答卷基础数据
        responseList.forEach(i -> {
            if (statusList.contains(i.getStatus())) {
                fillResponseValue(context, i);
            }
        });

        // 获得答卷问题数据
        List<SurveyResponseCell> responseCells;
        if (sequential) {
            long min = responseList.get(0).getId();
            long max = responseList.get(responseList.size() - 1).getId();
            responseCells = getCellByRIdRange(context.getSurveyId(), min, max);
        } else {
            List<Long> rIds = responseList.stream().map(BaseEntity::getId).collect(Collectors.toList());
            responseCells = getCellByRIds(context.getSurveyId(), rIds);
        }
        // 填充答卷问题数据
        if (CollectionUtils.isNotEmpty(responseCells)) {
            responseCells.forEach(i -> fillCellValue(context, i));
        }

        // v1.10.3 如果是 json 文件，则只需要答卷数据
        if (context.hasExtFiles()) {
            // download ext data
            downloadExtHelpers.forEach(extHelper -> {
                extHelper.download(context, responseList, false);
            });
        }
        entityManager.clear();
    }

    /**
     * 填充答卷基础数据
     */
    private void fillResponseValue(ResponseDownloadContext context, SurveyResponse response) {
        Object[] labelRow = context.getEmptyRow();
        Object[] codeRow = context.getEmptyRow();
        context.getResponseGroup().getColumns().forEach(c -> {
            labelRow[c.getIndex()] = c.getLabel(response);
            codeRow[c.getIndex()] = c.getCode(response);
        });
        context.getLabelRowMap().put(response.getId(), labelRow);
        context.getCodeRowMap().put(response.getId(), codeRow);
    }

    /**
     * 填充答卷问题数据
     */
    private void fillCellValue(ResponseDownloadContext context, SurveyResponseCell cell) {
        Object[] labelRow = context.getLabelRowMap().get(cell.getResponseId());
        Object[] codeRow = context.getCodeRowMap().get(cell.getResponseId());
        if (labelRow == null || codeRow == null) {
            return;
        }
        DownloadColumnQuestionGroup questionGroup = context.getQuestionGroupMap().get(cell.getQuestionId());
        if (questionGroup != null && questionGroup.getType() == cell.getType()) {
            questionGroup.getColumns().forEach(column -> {
                labelRow[column.getIndex()] = column.getLabel(cell);
                codeRow[column.getIndex()] = column.getCode(cell);
            });
        }
    }



    //
    // {
    //   "columns":[
    //     {
    //         "name":"id",
    //         "label":"主键"
    //     },
    //     {
    //         "name":"status",
    //         "label":"状态",
    //         "variables":[
    //             {"value:1,"label":"有效"},
    //             {"value:2,"label":"无效"}
    //         ]
    //     },
    //   ],
    //   "rows":[
    //     [1,1],
    //     [2,2]
    //   ]
    // },
    //
    //
    private ResponseDownloadFile writeJson(ResponseDownloadContext context) {
        progressStartWrite(context);
        AtomicInteger count = new AtomicInteger(0);
        List<Map<String, Object>> columns = new ArrayList<>();
        List<String> columnValueType = new ArrayList<>();
        List<List<Object>> rows = new ArrayList<>();
        List<Pair<Integer, String>> columnValueFrom = new ArrayList<>(); // null code label
        // 提取 response columns
        context.getResponseGroup().getColumns().forEach(column -> {
            if (!column.needDelete()) {
                Map<String, Object> columnInfo = new HashMap<>();
                columnInfo.put("name", column.getCode());
                columnInfo.put("label", column.getLabel());
                columnInfo.put("index", count.getAndIncrement());
                columnValueType.add(column.isNumberValue() ? "Int64" : "string");
                columns.add(columnInfo);
                columnValueFrom.add(Pair.of(column.getIndex(), "label"));
            }
        });
        // 提取 question columns
        context.getQuestionGroupMap().values().stream()
                .flatMap(i -> i.getColumns().stream())
                .sorted(Comparator.comparingInt(DownloadColumnQuestion::getIndex))
                .forEach(column -> {
                    Map<String, Object> columnInfo = new HashMap<>();
                    columnInfo.put("name", column.getCode());
                    columnInfo.put("label", column.getLabel());
                    columnInfo.put("index", count.getAndIncrement());
                    columns.add(columnInfo);
                    if (column instanceof DownloadColumnQuestion_dynamic_item) {
                        // 动态选项 直接使用 label 值
                        columnValueType.add("string");
                        columnValueFrom.add(Pair.of(column.getIndex(), "label"));
                        return;
                    }else if(column.getCode().endsWith("_text")){
                        // 选项的文本输入，直接使用 label 值
                        columnValueType.add("string");
                        columnValueFrom.add(Pair.of(column.getIndex(), "label"));
                        return;
                    }
                    List<Map<String, Object>> variables = null;
                    if (column.getGroup().getType() == SINGLE_CHOICE
                            || column.getGroup().getType() == COMBOBOX) {
                        // 单选 下拉框 设置列的标签值
                        Map<Integer, Map<String, Object>> variablesMap = new TreeMap<>();
                        column.getGroup().getItemIndexMap().forEach((key, index) -> {
                            String label = column.getGroup().getItemTextMap().getOrDefault(key, "");
                            variablesMap.put(index, Map.of("value", index, "label", label));
                        });
                        variables = new ArrayList<>(variablesMap.values());
                    } else if (column.getGroup().getType() == MATRIX_CHOICE) {
                        //  矩阵单选 设置列的标签值
                        Map<Integer, Map<String, Object>> variablesMap = new TreeMap<>();
                        column.getGroup().getColumnIndexMap().forEach((key, index) -> {
                            String label = column.getGroup().getColumnTextMap().getOrDefault(key, "");
                            variablesMap.put(index, Map.of("value", index, "label", label));
                        });
                        variables = new ArrayList<>(variablesMap.values());
                    }else if (column.getGroup().getType() == MULTIPLE_CHOICES) {
                        // 多选 设置列的标签值 为固定的 未选中 选中
                        variables = new ArrayList<>();
                        variables.add(Map.of("value", 0, "label", "未选中"));
                        variables.add(Map.of("value", 1, "label", "选中"));
                    }
                    if (variables != null) {
                        columnInfo.put("variables", variables);
                        columnValueType.add("Int64");
                        columnValueFrom.add(Pair.of(column.getIndex(), "code"));
                    } else {
                        columnValueType.add(null);
                        columnValueFrom.add(Pair.of(column.getIndex(), "label"));
                    }
                });

        LinkedHashMap<Long, Object[]> labelRows = context.getLabelRowMap();
        LinkedHashMap<Long, Object[]> codeRows = context.getCodeRowMap();
        // 提取 rows data
        labelRows.forEach((id, labelRow) -> {
            // 执行 2 次计数，这里 code 和 label 的数据只取其中一个
            progressUpdateWrite(context);
            progressUpdateWrite(context);
            List<Object> row = new ArrayList<>();
            for (int i = 0; i < columnValueFrom.size(); i++) {
                Pair<Integer, String> pair = columnValueFrom.get(i);
                Object[] rowData = labelRow;
                if (pair.getRight().equals("code")) {
                    rowData = codeRows.get(id);
                }
                Object data = null;
                if (rowData != null && rowData.length > pair.getLeft()) {
                    data = rowData[pair.getLeft()];
                }
                row.add(data);
                // 如果列未设置值类型，则通过数据判断该列的值类型
                if (columnValueType.get(i) == null) {
                    if (data instanceof Integer || data instanceof Long) {
                        columnValueType.set(i, "Int64");
                    } else if (data instanceof Double || data instanceof Float) {
                        columnValueType.set(i, "float");
                    } else {
                        columnValueType.set(i, "string");
                    }
                }
            }
            rows.add(row);
        });
        IntStream.range(0, columns.size()).forEach(i -> {
            String type = columnValueType.get(i);
            // 如果此列无数据，则设置列类型为 string
            columns.get(i).put("type", type != null ? type : "string");
        });
        String fileName = context.getSafeTitle() + "-" + System.currentTimeMillis() + context.getDownloadType().getSuffix();
        Map<String, Object> data = Map.of("columns", columns, "rows", rows);
        return new ResponseDownloadFile(fileName, data);
    }

    private ResponseDownloadFile writeExcelOrCsv(ResponseDownloadContext context) {
        try {
            // delete empty column | copy
            List<Integer> deleteColumns = context.getResponseGroup().deleteColumns();
            copyColumns(context, deleteColumns, context.getLabelHeaders(), context.getLabelRowMap().values(), context.getFileLabel().getHeaders(), context.getFileLabel().getRows());
            copyColumns(context, deleteColumns, context.getCodeHeaders(), context.getCodeRowMap().values(), context.getFileCode().getHeaders(), context.getFileCode().getRows());

            // copy ext data
            downloadExtHelpers.forEach(extHelper -> {
                extHelper.copyColumns(context);
            });
            context.clearTemp();
            progressStartWrite(context);

            WriteHandler handler = new RowWriteHandler() {
                @Override
                public void afterRowCreate(RowWriteHandlerContext c) {
                    progressUpdateWrite(context);
                }
            };
            List<ResponseDownloadFile> files = new ArrayList<>();
            ExcelTypeEnum fileType = context.getDownloadType().getExcelType();
            writeFile(files, context.getFileLabel(), handler, fileType);
            writeFile(files, context.getFileCode(), handler, fileType);

            for (IDownloadExtHelper<?> helper : downloadExtHelpers) {
                if (context.getExtFileMap().containsKey(helper.extType())) {
                    helper.writeFile(context, files, handler, fileType);
                }
            }

            if (!context.getAdditionalFiles().isEmpty()) {
                context.getAdditionalFiles().values().forEach(l -> {
                    Optional.ofNullable(l).ifPresent(i -> i.forEach(f -> writeAdditionalFile(files, f)));
                });
            }
            return zipFile(genFileName(context.getSurveyId(), context.getSafeTitle()), files);

        } catch (IOException e) {
            log.error("生成文件失败", e);
        }
        return null;
    }
    /**
     * 复制列，并删除无数据的列（deleteIfEmpty=true）
     */
    private void copyColumns(ResponseDownloadContext context, List<Integer> deleteColumns,
                             List<String> originHeaders, Collection<Object[]> originRows,
                             List<List<String>> formatHeaders, List<List<Object>> formatRows) {
        IntStream.range(0, context.getColumnSize()).filter(j -> !deleteColumns.contains(j)).forEach(i -> {
            String header = originHeaders.get(i);
            formatHeaders.add(List.of(header));
        });
        originRows.forEach(originRow -> {
            List<Object> row = new ArrayList<>();
            IntStream.range(0, context.getColumnSize()).filter(j -> !deleteColumns.contains(j)).forEach(i -> {
                row.add(originRow[i]);
            });
            formatRows.add(row);
        });
    }

    public static void writeFile(List<ResponseDownloadFile> files, ResponseExportFile file, WriteHandler handler, ExcelTypeEnum type) throws IOException {
        ByteArrayOutputStream bos = new ByteArrayOutputStream();

        if (type == ExcelTypeEnum.CSV) {
            // CSV文件增加bom头
            byte[] bom = getBomByte("UTF-8");
            bos.write(bom);
        }

        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(bos).excelType(type).useDefaultStyle(false);
        if (handler != null) {
            excelWriterBuilder.registerWriteHandler(handler);
        }

        excelWriterBuilder.head(file.getHeaders()).sheet("导出").doWrite(file.getRows());
        bos.flush();
        files.add(new ResponseDownloadFile(file.getFileName(), bos.toByteArray()));
        file.clear();

    }

    /**
     * 增加bom头
     * @param encode
     * @return
     */
    private static byte[] getBomByte(String encode) {
        switch (encode) {
            case "UTF-8":
                return new byte[] { (byte) 0xEF, (byte) 0xBB, (byte) 0xBF };
            case "UTF-16BE":
                return new byte[] { (byte) 0xFE, (byte) 0xFF };
            case "UTF-16LE":
                return new byte[] { (byte) 0xFF, (byte) 0xFE };
            case "UTF-32BE":
                return new byte[] { (byte) 0x00, (byte) 0x00, (byte) 0xFE, (byte) 0xFF };
            case "UTF-32LE":
                return new byte[] { (byte) 0x00, (byte) 0x00, (byte) 0xFF, (byte) 0xFE };
        }
        return null;
    }

    public void writeAdditionalFile(List<ResponseDownloadFile> files, ResponseAdditionalFile file) {
        files.add(new ResponseDownloadFile(file.getFullFileName(), fileStorageService.download(file.getFileUrl()).bytes()));
    }

    public ResponseDownloadFile zipFile(String fileName, List<ResponseDownloadFile> files) throws IOException {
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream();
             ZipOutputStream zos = new ZipOutputStream(bos)) {
            byte[] bytes = new byte[2048];

            for (ResponseDownloadFile file : files) {
                try (InputStream fis = new ByteArrayInputStream(file.getBytes()); BufferedInputStream bis = new BufferedInputStream(fis)) {
                    zos.putNextEntry(new ZipEntry(file.getFileName()));
                    int bytesRead;
                    while ((bytesRead = bis.read(bytes)) != -1) {
                        zos.write(bytes, 0, bytesRead);
                    }
                    zos.closeEntry();
                    file.setBytes(new byte[0]);
                }
            }
            zos.close();
            byte[] zipBytes = bos.toByteArray();
            return new ResponseDownloadFile(fileName, zipBytes);
        }
    }

    private List<SurveyResponse> findResponseByIds(Long surveyId, List<Long> responseIds) {
        return surveyResponseRepository.findBySurveyIdAndIdIn(surveyId, responseIds);
    }

    private int countAllResponse(Long surveyId) {
        return (int) surveyResponseRepository.countBySurveyIdAndStatus(surveyId, ResponseStatus.FINAL_SUBMIT);
    }

    private List<SurveyResponse> findAllResponse(Long surveyId) {
        return surveyResponseRepository.findBySurveyIdAndStatusOrderByIdAsc(surveyId, ResponseStatus.FINAL_SUBMIT);
    }

    private List<SurveyResponse> findResponse(Long surveyId, Long minId, int limit, List<ResponseStatus> statusList) {
        return surveyResponseRepository.findBySurveyIdAndStatusInAndIdGreaterThan(surveyId, statusList, minId, PageRequest.of(0, limit, Sort.by("id")));
    }

    private List<SurveyResponseCell> getCellByRIdRange(Long surveyId, long minRId, long maxRId) {
        return surveyResponseCellRepository.findBySurveyIdAndResponseIdBetween(surveyId, minRId, maxRId);
    }

    private List<SurveyResponseCell> getCellByRIds(Long surveyId, List<Long> rIds) {
        return surveyResponseCellRepository.findAllBySurveyIdAndResponseIdIn(surveyId, rIds);
    }

    public String genFileName(Long surveyId, String title) {
        return String.format("%s-%s.zip", RegularExpressionUtils.safeTitle(title), System.currentTimeMillis());
    }

    /**
     * 下载附件
     */
    public void downloadAttachment() {
        String url = "https://dev-assets.surveyplus.cn/cem/lite/Chroma_1709083349697.jpg";
        AtomicInteger count = new AtomicInteger(0);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        final ZipOutputStream[] zipOut = {null};
        try {
            zipOut[0] = new ZipOutputStream(new FileOutputStream("/Users/<USER>/Downloads/output.zip"));
            for (int i = 0; i < 5; i++) {
                CompletableFuture<Void> feature = CompletableFuture.runAsync(() -> {
                    synchronized (zipOut[0]) {
                        fileStorageService.download(url).inputStream(ins -> {
                            try {
                                ZipEntry zipEntry = new ZipEntry("file" + count.getAndIncrement() + ".jpg");
                                zipOut[0].putNextEntry(zipEntry);
                                byte[] buffer = new byte[1024];
                                int len;
                                while ((len = ins.read(buffer)) > 0) {
                                    zipOut[0].write(buffer, 0, len);
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        });
                    }
                }, executorService);
                futures.add(feature);
            }
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (zipOut[0] != null) {
                try {
                    zipOut[0].close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
