package cn.hanyi.survey.service;

import cn.hanyi.survey.client.exception.LotteryException;
import cn.hanyi.survey.client.service.luckyDraw.WechatLuckyDrawService;
import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.constant.lottery.SurveyLotteryStatus;
import cn.hanyi.survey.core.dto.lottery.HarvestAddressParam;
import cn.hanyi.survey.core.dto.lottery.SendRedPackParam;
import cn.hanyi.survey.core.entity.SurveyLotteryPrize;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinner;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinnerDto;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeRepository;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.exception.BaseException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/10/17 16:56:59
 */
@Service
@Slf4j
public class SurveyLotteryPrizeWinnerService extends BaseService<SurveyLotteryPrizeWinner, SurveyLotteryPrizeWinnerDto, SurveyLotteryPrizeWinnerRepository> {

    @Autowired
    private SurveyLotteryPrizeWinnerRepository surveyLotteryPrizeWinnerRepository;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyLotteryPrizeRepository surveyLotteryPrizeRepository;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private WechatLuckyDrawService wechatLuckyDrawService;

    /**
     * 添加中奖收获地址信息
     *
     * @param data 收获地址信息
     * @return
     */
    public Boolean savePrizeWinnerInfo(HarvestAddressParam data) {
        Assert.notNull(data.getId(), "id is not null");
        SurveyLotteryPrizeWinner prizeWinner = surveyLotteryPrizeWinnerRepository.findById(data.getId()).orElseThrow(() -> new LotteryException("id不存在"));
        prizeWinner.setId(data.getId());
        prizeWinner.setName(data.getName());
        prizeWinner.setPhone(data.getPhone());
        prizeWinner.setProvince(data.getProvince());
        prizeWinner.setCity(data.getCity());
        prizeWinner.setDistrict(data.getDistrict());
        prizeWinner.setAddress(data.getAddress());
        surveyLotteryPrizeWinnerRepository.save(prizeWinner);
        return true;
    }

    /**
     * 修改奖品发放状态
     *
     * @param status 发放状态
     * @return
     */
    public SurveyLotteryPrizeWinner updatePrizeWinnerSendStatus(Long sid, Long prizeWinnerId, PrizeSendStatus status) {
        surveyRepository.findById(sid).orElseThrow(() -> new LotteryException("抱歉，您访问的问卷不存在"));
        SurveyLotteryPrizeWinner prizeWinner = surveyLotteryPrizeWinnerRepository.findById(prizeWinnerId).orElseThrow();
        if (!prizeWinner.getStatus().equals(PrizeSendStatus.NOT_SEND)) {
            throw new LotteryException("只有待发放可以切换状态");
        }

        prizeWinner.setStatus(status);
        prizeWinner.setSendId(TenantContext.getCurrentUserId());
        surveyLotteryPrizeWinnerRepository.save(prizeWinner);

        SurveyLotteryPrize prize = surveyLotteryPrizeRepository.findById(prizeWinner.getPrizeId()).orElseThrow(() -> new LotteryException("奖品不存在"));
        if (List.of(LotteryPrizeType.RANDOM_RED_PACK, LotteryPrizeType.FIXED_RED_PACK).contains(prize.getType())
                && PrizeSendStatus.IS_SENT.equals(status)
                && prize.getLottery().getIsVerify()
        ) {
            if (SurveyLotteryStatus.CLOSED.equals(prize.getLottery().getStatus())) {
                throw new LotteryException("奖励已经关闭");
            }
            wechatLuckyDrawService.SendRedPack(new SendRedPackParam(prizeWinner.getId(), prizeWinner.getOpenid()));
        } else {
            prizeWinner.setSendTime(new Date());
        }

        return prizeWinner;
    }

    /**
     * 删除中奖信息
     *
     * @param sid
     * @param prizeWinnerId
     * @return
     */
    public Boolean deletePrizeWinner(Long sid, Long prizeWinnerId) {
        surveyRepository.findById(sid).orElseThrow(() -> new LotteryException("抱歉，您访问的问卷不存在"));
        SurveyLotteryPrizeWinner prizeWinner = surveyLotteryPrizeWinnerRepository.findById(prizeWinnerId).orElseThrow(() -> new BaseException());
        surveyLotteryPrizeWinnerRepository.delete(prizeWinner);
        return true;
    }
}






















