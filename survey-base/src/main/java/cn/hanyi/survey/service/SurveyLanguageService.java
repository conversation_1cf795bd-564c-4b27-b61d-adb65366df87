package cn.hanyi.survey.service;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyLanguage;
import cn.hanyi.survey.core.entity.SurveyLanguageDto;
import cn.hanyi.survey.core.repository.SurveyLanguageRepository;
import cn.hanyi.survey.dto.SurveyLanguageTranslateRequestDto;
import cn.hanyi.survey.workertrigger.SurveyTaskTrigger;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.UserTaskService;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.MapperService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;


@Service
@Slf4j
public class SurveyLanguageService {

    @Autowired
    private SurveyLanguageRepository surveyLanguageRepository;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private SurveyTaskTrigger surveyTaskTrigger;

    public SurveyLanguageDto findBySurveyAndLanguage(Long sid, String type) {
        Survey survey = new Survey();
        survey.setId(sid);
        Optional<SurveyLanguage> language = surveyLanguageRepository.findBySurveyAndLanguage(survey, type);
        return language.isEmpty() ? null : mapperService.map(language.get(), SurveyLanguageDto.class);
    }

    public SurveyLanguage findById(Long languageId) {
        Optional<SurveyLanguage> language = surveyLanguageRepository.findById(languageId);
        return language.isEmpty() ? null : language.get();
    }

    public Boolean translateAsync(Long sid, SurveyLanguageTranslateRequestDto requestDto) {
        SurveyLanguageDto languageDto = findBySurveyAndLanguage(sid, requestDto.getLanguage());
        Long languageId;
        try {
            if (languageDto == null) {
                SurveyLanguage language = new SurveyLanguage();
                language.setLanguage(requestDto.getLanguage());
                surveyLanguageRepository.save(language);
                languageId = language.getId();
            } else {
                languageId = languageDto.getId();
            }
            requestDto.setLanguageId(languageId);
            requestDto.setOrgId(TenantContext.getCurrentTenant());
            requestDto.setUserId(TenantContext.getCurrentUserId());
            TaskProgress progress = userTaskService.createTask(TenantContext.getCurrentTenant(),
                    TenantContext.getCurrentUserId(), UserTaskType.translateSurvey, 0, null, languageId);
            surveyTaskTrigger.translateSurvey(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), sid, JsonHelper.toJson(requestDto));
            log.info("translate survey: {}, task progress: {}", sid, progress.getId());
            return Boolean.TRUE;
        } catch (Exception ex) {
            log.error("failed to trigger translate task");
            ex.printStackTrace();
            return Boolean.FALSE;
        }
    }
}
