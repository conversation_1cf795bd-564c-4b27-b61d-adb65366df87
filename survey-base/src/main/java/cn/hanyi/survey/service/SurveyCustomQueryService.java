package cn.hanyi.survey.service;

import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.dto.SurveySimpleListDto;
import cn.hanyi.survey.dto.survey.SurveyRelationDto;
import cn.hanyi.survey.dto.survey.SurveySearchDto;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.UserService;
import org.befun.core.constant.EntityScopeStrategyTypes;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedResourceService;
import org.befun.core.service.MapperService;
import org.befun.core.service.ResourceCorporationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SurveyCustomQueryService extends BaseMixedResourceService<
        Survey, SurveySimpleListDto, SurveyRepository,
        SurveyGroup, SurveyGroupDto, SurveyGroupService> {

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    @Lazy
    private SurveyService surveyService;

    @Autowired
    private SurveyGroupService surveyGroupService;

    @Autowired
    private UserService userService;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private ResourceCorporationService resourceCorporationService;

    @Autowired
    private OrganizationConfigService organizationConfigService;

    public List<SurveyRelationDto> surveyRelation(String ids) {
        if (StringUtils.isEmpty(ids)) {
            return List.of();
        }
        List<SurveyRelationDto> dto = new ArrayList<>();
        Arrays.stream(ids.split(",")).forEach(i -> {
            if (NumberUtils.isDigits(i)) {
                Long surveyId = Long.parseLong(i);
                int relation = 1; // 1 问卷已删除 2 无权限 3 有权限
                Optional<Survey> survey = surveyRepository.findById(surveyId);
                boolean exists = survey.isPresent();
                String title = survey.map(BaseSurvey::getTitle).orElse(null);
                if (exists) {
                    exists = !surveyRepository.findAll((Specification<Survey>) (root, query, cb) -> cb.equal(root.get("id"), surveyId)).isEmpty();
                    if (exists) {
                        relation = 3;
                    } else {
                        relation = 2;
                    }
                }
                dto.add(new SurveyRelationDto(surveyId, relation, title));
            }
        });
        return dto;
    }


    @Override
    public void afterMapToDto(List<Survey> surveyList, List<SurveySimpleListDto> surveyDtoList) {
        Set<Long> updateUserIds = new HashSet<>();
        Set<Long> groupIds = new HashSet<>();
        Set<Long> surveyIds = new HashSet<>();

        surveyList.forEach(survey -> {
            Optional.ofNullable(survey.getEditorId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(survey.getUserId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(survey.getGroupId()).ifPresent(groupIds::add);
            Optional.ofNullable(survey.getId()).ifPresent(surveyIds::add);
        });

        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
        Map<Long, SurveyGroupDto> groupMap = surveyGroupService.getGroupMapByIds(new ArrayList<>(groupIds), SurveyGroup::getId);
        Map<Long, Integer> numRespMap = responseService.getNumResponse(surveyList);

        List<SurveySimpleListDto> missingCreator = new ArrayList<>();

        surveyDtoList.forEach(surveySimpleDto -> {
            Survey survey = surveySimpleDto.getEntity();
            Integer numOfResponse = numRespMap.getOrDefault(surveySimpleDto.getId(), 0);
            surveySimpleDto.setNumOfResponses(numOfResponse == null ? 0 : numOfResponse.intValue());
            Optional.ofNullable(survey.getEditorId()).ifPresent(i -> surveySimpleDto.setEditorUser(userMap.get(i)));
            Optional.ofNullable(survey.getUserId()).ifPresent(i -> surveySimpleDto.setCreator(userMap.get(i)));
            if (surveySimpleDto.getCreator() == null) {
                missingCreator.add(surveySimpleDto);
            }
            Optional.ofNullable(survey.getGroupId()).ifPresent(i -> surveySimpleDto.setGroup(groupMap.get(i)));
        });
        if (!missingCreator.isEmpty()) {
            SimpleUser superAdmin = userService.getAdminUser(TenantContext.getCurrentTenant());
            if (superAdmin != null) {
                missingCreator.forEach(i -> i.setCreator(superAdmin));
                List<Long> resetIds = missingCreator.stream().map(SurveySimpleListDto::getId).collect(Collectors.toList());
                surveyService.resetSurveyOwner(TenantContext.getCurrentTenant(), resetIds, superAdmin.getId());
            }
        }
    }

    public Page<SurveySimpleListDto> findPageByGroup(SurveySearchDto dto) {
        //如果根据 groupId搜索 则修改类型为all v 1.10.4 目录下面的问卷只能是目录所有人的
//        if (dto.getGroupId() != null && dto.getGroupId() > 0) {
//            dto.setType("all");
//        }
        Page<SurveySimpleListDto> surveySimpleListDtos;
        if (dto.isAudit()) {
            surveySimpleListDtos = findByVerify(dto);
        } else {
            surveySimpleListDtos = findAll(dto);
        }
        List<SurveySimpleListDto> list = surveySimpleListDtos.getContent();
        Map<Integer, List<SurveySimpleListDto>> s = list.stream().collect(Collectors.groupingBy(SurveySimpleListDto::getItemType));
        Optional.ofNullable(s.get(1)).ifPresent(l -> resourceCorporationService.fillResourcePermissionInfo(l, ResourcePermissionType.SURVEY.name(), SurveySimpleListDto::getId));
        Optional.ofNullable(s.get(2)).ifPresent(l -> resourceCorporationService.fillResourcePermissionInfo(l, ResourcePermissionType.SURVEY_GROUP.name(), ss -> ss.getGroup().getId()));
        return surveySimpleListDtos;
    }

    /**
     * 我的审核
     */
    private Page<SurveySimpleListDto> findByVerify(SurveySearchDto dto) {

        final OrgConfigDto orDefaultConfig = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyVerify);
        if (!orDefaultConfig.getSurveyVerify()) {
            throw new BadRequestException("管理员已取消问卷审核流程");
        }
        // 构造查询条件
        ResourceEntityQueryDto<SurveyDto> params = dto.transform();
        if (params.getSorts() == null) {
            params.setSorts(Sort.by(Sort.Direction.DESC, "modifyTime"));
        }
        if (StringUtils.isNotEmpty(dto.getQ())) {
            params.addCriteria(new ResourceQueryCriteria("title", dto.getQ(), QueryOperator.LIKE));
        }
        dto.buildStatusCriteria().ifPresent(params::addCriteria);
        // 非管理员，则设置为 邀请协作 查询策略
        EntityScopeStrategyTypes strategyType = EntityScopeStrategyTypes.getInstance("SURVEY", "surveyVerifyFilter");
        TenantContext.addCustomEntityScopeStrategy(Survey.class, strategyType);
        Page<SurveyDto> page = surveyService.findAll(params);
        TenantContext.clearCustomEntityScopeStrategy(Survey.class);
        return page.map(i -> mapperService.map(i, SurveySimpleListDto.class));
    }


    private List<SurveySimpleListDto> surveyListToSurveySimpleDtoList(List<Survey> surveyList) {
        List<SurveySimpleListDto> surveySimpleListDto = new ArrayList<>();
        Set<Long> updateUserIds = new HashSet<>();
        Set<Long> groupIds = new HashSet<>();
        Set<Long> surveyIds = new HashSet<>();

        surveyList.forEach(survey -> {
            Optional.ofNullable(survey.getEditorId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(survey.getUserId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(survey.getGroupId()).ifPresent(groupIds::add);
            Optional.ofNullable(survey.getId()).ifPresent(surveyIds::add);
        });

        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
        Map<Long, SurveyGroupDto> groupMap = surveyGroupService.getGroupMapByIds(new ArrayList<>(groupIds), SurveyGroup::getId);
        Map<Long, Integer> numRespMap = responseService.getNumResponse(surveyList);

        surveyList.forEach(survey -> {
            SurveySimpleListDto surveySimpleDto = mapperService.map(survey, SurveySimpleListDto.class);

            Integer numOfResponse = numRespMap.getOrDefault(survey.getId(), 0);
            surveySimpleDto.setNumOfResponses(numOfResponse == null ? 0 : numOfResponse.intValue());
            Optional.ofNullable(survey.getEditorId()).ifPresent(i -> surveySimpleDto.setEditorUser(userMap.get(i)));
            Optional.ofNullable(survey.getUserId()).ifPresent(i -> surveySimpleDto.setCreator(userMap.get(i)));
            Optional.ofNullable(survey.getGroupId()).ifPresent(i -> surveySimpleDto.setGroup(groupMap.get(i)));
            surveySimpleListDto.add(surveySimpleDto);
        });
        resourceCorporationService.fillResourcePermissionInfo(surveySimpleListDto);
        return surveySimpleListDto;
    }
}
