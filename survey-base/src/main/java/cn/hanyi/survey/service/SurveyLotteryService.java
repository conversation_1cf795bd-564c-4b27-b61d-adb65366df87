package cn.hanyi.survey.service;

import cn.hanyi.survey.client.constant.SurveyLotteryConstant;
import cn.hanyi.survey.client.exception.LotteryException;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.constant.lottery.SurveyLotteryStatus;
import cn.hanyi.survey.core.dto.lottery.OrderParam;
import cn.hanyi.survey.core.dto.lottery.SimpleSurveyLotteryResult;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyLottery;
import cn.hanyi.survey.core.entity.SurveyLotteryDto;
import cn.hanyi.survey.core.entity.SurveyLotteryPrize;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import cn.hanyi.survey.core.repository.SurveyLotteryRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.pay.constant.OrderStatus;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.dto.order.OrderStatusDto;
import org.befun.auth.pay.dto.order.RedPacketOrderRefundRequestDto;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2022/9/21 14:54:40
 */
@Service
@Slf4j
public class SurveyLotteryService extends BaseService<SurveyLottery, SurveyLotteryDto, SurveyLotteryRepository> {

    @Autowired
    private SurveyLotteryRepository surveyLotteryRepository;

    @Autowired
    private UserService userService;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private OrganizationOrderService organizationOrderService;

    @Autowired
    private OrganizationRechargeService rechargeService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;
    @Autowired
    private SurveyLotteryPrizeWinnerRepository surveyLotteryPrizeWinnerRepository;


    public Survey requireSurvey(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = surveyRepository.findById(surveyId);
        Survey survey = surveyOptional.orElse(null);
        if (survey == null) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
        }
        return survey;
    }


    /**
     * 返回指定问卷的抽奖活动
     *
     * @param sid
     * @return
     */
    public Page<SurveyLotteryDto> findAllBySid(Long sid, int page, int limit) {

        PageRequest pageRequest = PageRequest.of(page - 1, limit, Sort.by("createTime").descending());
        Page<SurveyLottery> lotteryList = surveyLotteryRepository.findBySid(sid, pageRequest);

        List<SurveyLotteryDto> lotteryDtos = new ArrayList<>();
        lotteryList.stream().forEach(x -> {
            SurveyLotteryDto dto = mapToDto(x);
            dto.setTotalNumber(x.getTotalNumber());
            dto.setTotalWinnerNum(x.getTotalWinnerNum());
            SimpleUser simpleUser = x.getCreator() == null ? null : userService.getSimple(x.getCreator()).orElse(null);
            dto.setUser(simpleUser);
            lotteryDtos.add(dto);
        });
        return new PageImpl<>(lotteryDtos, pageRequest, lotteryList.getTotalElements());
    }

    /**
     * 新增抽奖渠道
     *
     * @param data
     * @return
     */
    public SurveyLotteryDto insert(SurveyLotteryDto data) {

        int count = surveyLotteryRepository.countBySidAndAndLotteryType(data.getSid(), data.getLotteryType()) + 1;
        data.setLotteryName(data.getLotteryType().getText() + count);

        Survey survey = this.requireSurvey(data.getSid());

        SurveyLotteryDto dto = create(data);
        return dto;
    }

    /**
     * 获取问卷下的所有抽奖活动名称
     *
     * @param sid
     * @return
     */
    public List<SimpleSurveyLotteryResult> findSimpleLottery(Long sid) {
        return surveyLotteryRepository.findAllBySid(sid);
    }

    @Override
    public SurveyLotteryDto findOne(long id) {
        SurveyLotteryDto dto = super.findOne(id);
        Survey survey = surveyRepository.getOne(dto.getSid());
        dto.setSurveyName(survey.getTitle());
        return dto;
    }

    /**
     * 修改抽奖活动
     *
     * @param id
     * @param change
     * @return
     */
    @Transactional
    public SurveyLotteryDto updateOne(long id, SurveyLotteryDto change) {
        SurveyLottery surveyLottery = surveyLotteryRepository.findById(id).orElseThrow(() -> new LotteryException("活动不存在"));
        if (change.getStatus() != null) {
            //活动被关闭后，不能再修改状态
            checkStatus(surveyLottery.getStatus());
            //如果是微信渠道，启用时判断是否支付
            if (change.getStatus() == SurveyLotteryStatus.OPENING && surveyLottery.getLotteryType() == LotteryType.WECHAT) {
                checkPay(surveyLottery);
            }
            //关闭状态回退红包余额
            if (change.getStatus() == SurveyLotteryStatus.CLOSED) {
                rollbackMoney(surveyLottery);
                surveyEventTrigger.lotteryClose(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), surveyLottery.getSid(), id, surveyLottery.getLotteryType().name());
            }
        }
        SurveyLotteryDto dto = super.updateOne(id, change);
        return dto;
    }

    /**
     * 检查微信渠道是否支付
     *
     * @param surveyLottery
     */
    private void checkPay(SurveyLottery surveyLottery) {
        if (surveyLottery.getOrderId() == null) {
            throw new BadRequestException("请先进入渠道内完成红包支付");
        }
        List<SurveyLotteryPrize> prizes = surveyLottery.getPrizes();
        int totalMoney = 0;
        if (!prizes.isEmpty()) {
            totalMoney = prizes.get(0).getTotalMoney();
        }
        OrderStatusDto dto = organizationOrderService.orderStatus(surveyLottery.getOrderId(), surveyLottery.getId(), OrderType.order_red_packet, totalMoney);
        if (dto == null || dto.getStatus() != OrderStatus.success) {
            throw new LotteryException();
        }
    }

    @Transactional
    public Boolean deleteOne(long id) {
        SurveyLottery surveyLottery = surveyLotteryRepository.findById(id).orElseThrow(() -> new LotteryException("活动不存在"));
        if (surveyLottery.getStatus() != SurveyLotteryStatus.CLOSED) {
            //删除红包回退余额,已经关闭的渠道不再回退
            rollbackMoney(surveyLottery);
            surveyEventTrigger.lotteryClose(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), surveyLottery.getSid(), id, surveyLottery.getLotteryType().name());
        }
        return super.deleteOne(id);
    }

    /**
     * 关闭和删除微信红包活动返回剩余余额
     *
     * @param surveyLottery
     */
    public void rollbackMoney(SurveyLottery surveyLottery) {
        if (surveyLottery.getLotteryType() == LotteryType.WECHAT) {
            // 删除退回剩余红包金额
            List<SurveyLotteryPrize> prizes = surveyLottery.getPrizes();
            if (!prizes.isEmpty() && prizes.get(0).getTotalMoney() != null && prizes.get(0).getWinnerMoney() != null) {
                int amount = getTotalMoney(surveyLottery.getId());
                log.info("红包返还余额{}元", amount / 100);
                if (amount > 0 && surveyLottery.getOrderId() != null) {
                    Survey survey = this.requireSurvey(surveyLottery.getSid());
                    RedPacketOrderRefundRequestDto refund = new RedPacketOrderRefundRequestDto(surveyLottery.getOrderId(),
                            surveyLottery.getId(), amount, surveyLottery.getId().toString(), "红包返还:" + survey.getTitle());
                    boolean refundResult = organizationOrderService.refundOrder(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), OrderType.order_red_packet, refund);
                    log.info("红包回退结果：{}", refundResult);
                    if (refundResult) {
                        String redPack = String.format(SurveyLotteryConstant.RED_PACT_KEY, surveyLottery.getId());
                        redisTemplate.delete(redPack);
                        log.info("红包返还成功");
                    }
                }
            }
        }
    }

    /**
     * 红包剩余总金额
     *
     * @param lotteryId
     * @return
     */
    public int getTotalMoney(Long lotteryId) {
        String redPack = String.format(SurveyLotteryConstant.RED_PACT_KEY, lotteryId);
        Long size = redisTemplate.opsForList().size(redPack);
        int totalMoney = 0;
        for (int i = 0; i < size; i++) {
            //String money = redisTemplate.opsForList().leftPop(redPack);
            String money = redisTemplate.opsForList().index(redPack, i);
            if (money == null) {
                return totalMoney;
            }
            totalMoney += Integer.parseInt(money);
        }
        return totalMoney + getUnScanMoney(lotteryId) + getInvalid(lotteryId);
    }

    public int getUnScanMoney(Long lotteryId) {
        AtomicInteger totalMoney = new AtomicInteger(0);
        surveyLotteryPrizeWinnerRepository.findAllByLotteryIdAndStatus(lotteryId, PrizeSendStatus.NOT_SEND.ordinal()).forEach(winner -> {
            totalMoney.addAndGet(winner.getAmount());
            surveyLotteryPrizeWinnerRepository.save(winner);
        });
        log.info("红包未扫码领取金额：{}", totalMoney.get() / 100);
        return totalMoney.get();
    }

    public int getInvalid(Long lotteryId) {
        AtomicInteger totalMoney = new AtomicInteger(0);
        surveyLotteryPrizeWinnerRepository.findAllByLotteryIdAndStatus(lotteryId, PrizeSendStatus.INVALID.ordinal()).forEach(winner -> {
            totalMoney.addAndGet(winner.getAmount());
        });
        log.info("红包作废金额：{}", totalMoney.get() / 100);
        return totalMoney.get();
    }


    public void checkStatus(SurveyLotteryStatus status) {
        if (SurveyLotteryStatus.CLOSED.equals(status)) {
            throw new LotteryException("活动被关闭后，不能再修改");
        }
    }

    public SurveyLotteryDto update(Long lotteryId, OrderParam order) {
        SurveyLottery surveyLottery = surveyLotteryRepository.findById(lotteryId).orElseThrow(() -> new LotteryException("entity is not exist"));
        List<SurveyLotteryPrize> prizes = surveyLottery.getPrizes();
        int totalMoney = 0;
        if (!prizes.isEmpty()) {
            totalMoney = prizes.get(0).getTotalMoney();
        }
        OrderStatusDto dto = organizationOrderService.orderStatus(order.getOrderId(), lotteryId, OrderType.order_red_packet, totalMoney);
        if (dto == null || dto.getStatus() != OrderStatus.success) {
            throw new LotteryException();
        }
        surveyLottery.setIsPay(true);
        surveyLottery.setStatus(SurveyLotteryStatus.OPENING);
        surveyLottery.setOrderId(order.getOrderId());
        surveyLotteryRepository.save(surveyLottery);
        return mapToDto(surveyLottery);
    }
}
















