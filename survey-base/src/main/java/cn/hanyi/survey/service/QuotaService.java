package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.QuotaChannelType;
import cn.hanyi.survey.core.constant.QuotaType;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.survey.SurveyQuotaStatus;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.quota.SurveyQuotaBatchOptionRequestDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuota;
import cn.hanyi.survey.core.entity.SurveyQuotaDto;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyQuotaRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.dto.SurveyProcessDto;
import cn.hanyi.survey.service.quota.QuotaAllMatchLimit;
import cn.hanyi.survey.service.quota.bean.QuotaInit;
import cn.hanyi.survey.service.quota.bean.QuotaStatItem;
import cn.hanyi.survey.workertrigger.dto.TaskQuotaSyncDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.UserTaskService;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.query.PageResult;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskStatus;
import org.befun.task.dto.TaskProgressDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class QuotaService extends CustomEmbeddedService<SurveyQuota, SurveyQuotaDto, SurveyQuotaRepository> {

    @Autowired
    private QuotaAllMatchLimit quotaAllMatchLimit;
    @Autowired
    private SurveyQuotaRepository quotaRepository;
    @Autowired
    private SurveyService surveyService;
    @Autowired
    private ResponseService responseService;
    @Autowired
    private SurveyResponseRepository responseRepository;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${befun.task.queue.adminx-sync-quota-key:}")
    private String adminxSyncQuotaKey;

    @Override
    protected Object requireParent(long l) {
        return surveyService.require(l);
    }

    @Override
    public Page<SurveyQuotaDto> findAllEmbeddedMany(Long rootId, String rootFieldNameInEmbedded,
                                                    ResourceEntityQueryDto<SurveyQuotaDto> queryDto) {
        List<SurveyQuotaDto> quotaDtos = new ArrayList<>();
        List<SurveyQuota> quotaList = new ArrayList<>();
        Pageable pageable = PageRequest.of(queryDto.getPage() - 1, queryDto.getLimit());
        Page<SurveyQuota> quotas = quotaRepository.findQuotaList(rootId, pageable);
        quotas.forEach(q -> {
            // 单题配额相同group_code算作一条数据
            if (q.getType() == QuotaType.SINGLE_QUSTION_QUOTA) {
                entityManager.clear();
                List<SurveyQuota> groupQuotaList = quotaRepository
                        .findAllBySurveyAndGroupCodeAndTypeAndChannelType(q.getSurvey(),
                                q.getGroupCode(), QuotaType.SINGLE_QUSTION_QUOTA,
                                QuotaChannelType.XM_PLUS);
                quotaDtos.addAll(mapToDto(groupQuotaList));
                quotaList.addAll(groupQuotaList);
            } else {
                quotaDtos.add(mapToDto(q));
                quotaList.add(q);
            }
        });
        afterMapToDto(quotaList, quotaDtos);
        return new PageResult<SurveyQuotaDto>(quotaDtos, pageable, quotas.getTotalElements());
    }

    @Override
    public SurveyQuotaDto createEmbeddedMany(Long rootId, EmbeddedFieldContext fieldContext, SurveyQuotaDto data) {
        data.setChannelType(QuotaChannelType.XM_PLUS);
        return super.createEmbeddedMany(rootId, fieldContext, data);
    }

    @Transactional
    public ResourceListResponseDto<SurveyQuotaDto> batchCreate(Long rootId, SurveyQuotaBatchOptionRequestDto requestDto) {
        Survey survey = surveyService.requireSurvey(rootId);
        if (survey != null) {
            List<SurveyQuota> quotaList = requestDto.getQuotas().stream().map(q -> {
                SurveyQuota quota = mapperService.map(q, SurveyQuota.class);
                quota.setChannelType(QuotaChannelType.XM_PLUS);
                quota.setSurvey(survey);
                return quota;
            }).collect(Collectors.toList());
            repository.saveAll(quotaList);
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
            survey.setEnableQuota(true);
            survey.setStatus(SurveyStatus.STOPPED);
            surveyService.save(survey);
            //TODO 新增配额表达式需要重新统计所有的配额
            unsetQuotaExpressionHash(survey);
            return new ResourceListResponseDto<>(mapToDto(quotaList));
        } else {
            throw new BadRequestException();
        }
    }

    @Transactional
    public ResourceResponseDto<Boolean> batchDelete(Long rootId, SurveyQuotaBatchOptionRequestDto requestDto) {
        requestDto.getQuotas().forEach(q -> repository.delete(mapperService.map(q, SurveyQuota.class)));
        return new ResourceResponseDto<>(true);
    }

    @Transactional
    public ResourceListResponseDto<SurveyQuotaDto> batchUpdate(Long rootId, SurveyQuotaBatchOptionRequestDto requestDto) {
        Survey survey = surveyService.requireSurvey(rootId);
        if (survey != null) {
            List<SurveyQuota> quotaList = repository
                    .findAllBySurveyAndGroupCodeAndTypeAndChannelType(survey,
                            requestDto.getGroupCode(), QuotaType.SINGLE_QUSTION_QUOTA,
                            QuotaChannelType.XM_PLUS);
            // 需要删除的配额
            List<Long> existIds = requestDto.getQuotas().stream().map(SurveyQuotaDto::getId).collect(
                    Collectors.toList());
            quotaList.forEach(q -> {
                if (!existIds.contains(q.getId())) {
                    repository.delete(q);
                }
            });
            // 需要更新的配额
            requestDto.getQuotas().forEach(q -> {
                updateOneEmbeddedMany(rootId, "survey", q.getId(), q);
            });
            return new ResourceListResponseDto<>(requestDto.getQuotas());
        } else {
            throw new BadRequestException();
        }
    }

    /**
     * 开启配额
     * 如果该问卷下没有答题数据,就不用跑配额
     * 如果有答题数据，就需要异步跑配额
     */
    public void enable(Survey survey) {
        survey.setEnableQuota(true);
        survey.setStatus(SurveyStatus.STOPPED);
        //如果配额不为空 启用问卷的时候 需要重新计算配额
        if (!survey.getQuotas().isEmpty()) {
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
        }
        surveyService.save(survey);
    }

    /**
     * 关闭配额
     */
    public void disable(Survey survey) {
        survey.setEnableQuota(false);
        surveyService.save(survey);
    }

    /**
     * 旧的答卷重新使用配额
     * 如果命中了配额，直接使用，不用校验容量
     */
    public boolean useQuotaByOldResponse(Survey survey, Long responseId) {
        return useQuotaByOldResponse(survey, responseService.get(responseId));
    }

    /**
     * 旧的答卷重新使用配额
     * 如果命中了配额，直接使用，不用校验容量
     */
    public boolean useQuotaByOldResponse(Survey survey, SurveyResponse response) {
        if (survey == null || response == null) {
            return false;
        }
        Long surveyId = survey.getId();
        Long responseId = response.getId();
        log.info("useQuotaByOldResponse survey: {} response: {}", surveyId, responseId);
        if (!surveyService.checkSurveyQuota(survey)) {
            return true;
        }
        List<SurveyQuota> useQuotas = new ArrayList<>();
        applyQuotaByResponse(survey, response, useQuotas::add, useQuotas::add);
        List<Long> matchQuoteIds = new ArrayList<>();
        responseService.quotaTriggerResponse(useQuotas, responseId).forEach(quota -> matchQuoteIds.add(quota.getId()));
        if (matchQuoteIds.isEmpty()) {
            return true;
        }
        log.debug("useQuotaByOldResponse survey: {} response: {} quotas: {}", surveyId, response.getId(), JsonHelper.toJson(matchQuoteIds));
        return quotaAllMatchLimit.useQuotaIgnoreCapacity(surveyId, responseId, matchQuoteIds);
    }

    /**
     * 1份答卷 要么计算 adminx 配额 要么计算 cem 配额
     */
    private void applyQuotaByResponse(Survey survey, SurveyResponse response, Consumer<SurveyQuota> applyCem, Consumer<SurveyQuota> applyAdminx) {
        if (response.getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) {
            if (survey.getEnableAdminxQuota()) {
                survey.getQuotas().forEach(quota -> {
                    if (quota.getChannelType() == QuotaChannelType.SURVEY_PLUS) {
                        // 是 adminx 答卷, 并且开启了 adminx 配额，并且是 adminx 配额
                        applyAdminx.accept(quota);
                    }
                });
            }
        } else {
            if (survey.getEnableQuota()) {
                survey.getQuotas().forEach(quota -> {
                    if (quota.getChannelType() == QuotaChannelType.XM_PLUS) {
                        // 是 cem 答卷, 并且开启了 cem 配额，并且是 cem 配额
                        applyCem.accept(quota);
                    }
                });
            }
        }
    }

    /**
     * 批量删除答卷后需要回退配额进度
     */
    @Async
    public void rollBackQuotaBatch(@NotEmpty Long surveyId, @NotEmpty List<Long> respondentIds) {
        Survey survey = surveyService.requireSurvey(surveyId);
        respondentIds.forEach(id -> rollBackQuota(survey, responseService.get(id)));
    }

    /**
     * 批量删除答卷后需要回退配额进度
     */
    @Async
    public void rollBackQuotaBatch2(@NotEmpty Long surveyId, @NotEmpty List<SurveyResponse> surveyResponses) {
        Survey survey = surveyService.requireSurvey(surveyId);
        surveyResponses.forEach(response -> rollBackQuota(survey, response));
    }

    /**
     * 删除答卷后需要回退配额进度
     */
    public boolean rollBackQuota(@NotEmpty Long surveyId, Long responseId) {
        return rollBackQuota(surveyService.get(surveyId), responseService.get(responseId));
    }

    public boolean rollBackQuota(Survey survey, SurveyResponse response) {
        if (survey == null || response == null) {
            return false;
        }
        Long surveyId = survey.getId();
        Long responseId = response.getId();
        log.info("rollBackQuota survey: {} response: {}", surveyId, responseId);
        if (!surveyService.checkSurveyQuota(survey)) {
            return true;
        }
        List<Long> supportQuotaIds = new ArrayList<>();
        applyQuotaByResponse(survey, response,
                quota -> supportQuotaIds.add(quota.getId()),
                quota -> supportQuotaIds.add(quota.getId()));
        if (!supportQuotaIds.isEmpty()) {
            quotaAllMatchLimit.deleteResponse(surveyId, responseId, supportQuotaIds);
        }
        return true;
    }


    @Override
    public void afterMapToDto(List<SurveyQuota> entity, List<SurveyQuotaDto> dto) {
        if (CollectionUtils.isNotEmpty(entity)) {
            Survey survey = entity.get(0).getSurvey();
            if (survey != null) {
                List<QuotaStatItem> quotaStats = quotaAllMatchLimit.getQuotaStat(survey.getId());
                dto.forEach(quota -> {
                    quotaStats.forEach(quotaStat -> {
                        if (quota.getId() == quotaStat.quotaId)
                            quota.setCurrent(quotaStat.use);
                    });
                });
            }
        }
    }

    @Override
    protected void afterExecuteMethod(ResourceMethod method, SurveyQuota entity, SurveyQuotaDto dto) {
        if (method == ResourceMethod.CREATE) {
            Survey survey = entity.getSurvey();
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
            survey.setEnableQuota(true);
            survey.setStatus(SurveyStatus.STOPPED);
            surveyService.save(survey);
            //TODO 新增配额表达式需要重新统计所有的配额
            unsetQuotaExpressionHash(survey);
        }
    }

    @Override
    public SurveyQuotaDto updateOneEmbeddedMany(Long surveyId, String embeddedMapperBy, Long eid, SurveyQuotaDto params) {
        Optional<SurveyQuota> surveyQuotaOptional = quotaRepository.findById(eid);
        if (surveyQuotaOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        SurveyQuota surveyQuota = surveyQuotaOptional.get();

        //对比表达式 如果不一样 需要更新配额计算状态
        String oldExpression = Optional.ofNullable(surveyQuota.getExpression()).orElse("");
        String newExpression = Optional.ofNullable(params.getExpression()).orElse("");
        if (!oldExpression.equals(newExpression)) {
            //获取问卷信息
            Survey survey = surveyService.requireSurvey(surveyId);
            //问卷配额状态 修改为计算中
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
            survey.setEnableQuota(true);
            surveyService.save(survey);
            //TODO 修改了配额表达式需要重新统计所有的配额
            unsetQuotaExpressionHash(survey);
        }

        crudService.mapToEntity(params, surveyQuota);

        //更新配额数量需要 更新quota:stats:surveyId hash的统计数据
        if (params.getMax() != null && params.getMax() > 0) {
            surveyQuota.setMax(params.getMax());
            quotaAllMatchLimit.updateQuotaMax(surveyId, eid, params.getMax());
        }

        //获取配额使用的数据
        quotaRepository.save(surveyQuota);
        SurveyQuotaDto dto = mapToDto(surveyQuota);
        dto.setCurrent(quotaAllMatchLimit.getOneQuotaUseStat(surveyId, eid));
        return dto;
    }

    /**
     * 重置配额表达式的hash 重新发布问卷的时候 会重新统计配额
     */
    public void unsetQuotaExpressionHash(@NotNull Survey survey) {
        repository.updateExpressionHashBySurvey(survey);
    }

    @Transactional
    public SurveyProcessDto calculateQuota(Long surveyId) {
        Survey survey = surveyService.require(surveyId);
        SurveyStatus.checkToCollecting(survey.getStatus());
        return calculateQuota(survey, false, true);
    }

    /**
     * 定时从 Redis 消息队列消费数据 同步社区配额
     * 每隔 2 秒执行一次
     */
    @Scheduled(fixedRate = 2000)
    public void consumeFromQueueToSyncAdminxQuota() {
        // 从队列右侧获取消息（非阻塞式）
        String message = stringRedisTemplate.opsForList().rightPop(adminxSyncQuotaKey);

        if (message != null) {
            TaskQuotaSyncDto quotaSyncDto = JsonHelper.toObject(message,TaskQuotaSyncDto.class);
            if (quotaSyncDto != null) {
                log.info("surveyId:{} start to sync quota", quotaSyncDto.getSurveyId());
                Survey survey = surveyService.require(quotaSyncDto.getSurveyId());
                SurveyStatus.checkToCollecting(survey.getStatus());
                calculateQuota(survey, false, true);
            }
        }
    }

    @Transactional
    public SurveyProcessDto calculateQuota(Survey survey, Boolean cemPublish, Boolean adminxPublish) {
        boolean cemQuota = survey.getEnableQuota() != null && survey.getEnableQuota() && cemPublish;
        boolean adminxQuota = survey.getEnableAdminxQuota() != null && survey.getEnableAdminxQuota() && adminxPublish;
        // 没有开启配额
        if (!cemQuota && !adminxQuota) {
            log.debug("survey {} calculateQuota skip, quota is disabled", survey.getId());
            return null;
        }
        if (CollectionUtils.isEmpty(survey.getQuotas())) {
            log.debug("survey {} calculateQuota skip, quota is empty", survey.getId());
            return null;
        }
        // 配额计算完毕
        if (cemPublish && survey.getQuotaStatus() == SurveyQuotaStatus.CALCULATE_COMPLETE) {
            log.debug("survey {} calculateQuota skip, quota is complete", survey.getId());
            return null;
        }
        Long orgId = survey.getOrgId();
        if ((cemPublish && survey.getQuotaStatus() == SurveyQuotaStatus.CALCULATE_INIT) || (adminxPublish && survey.getAdminxQuotaStatus() == SurveyQuotaStatus.CALCULATE_INIT)) {
            if (calculateQuotaStart(orgId, cemQuota, adminxQuota, survey)) {
                return null;
            }
        }
        return calculateQuotaProgress(orgId, survey);
    }

    /**
     * @return true 不需要计算 false 开始计算
     */
    private boolean calculateQuotaStart(long orgId, boolean cemQuota, boolean adminxQuota, Survey survey) {
        log.info("survey {} calculateQuota start", survey.getId());
        List<SurveyQuota> quotas = survey.getQuotas();
        List<QuotaInit> allQuotas = new ArrayList<>();
        List<Long> syncCemQuotas = new ArrayList<>();
        List<Long> syncAdminxQuotas = new ArrayList<>();
        quotas.forEach(quota -> {
            allQuotas.add(new QuotaInit(quota.getId(), quota.getMax()));
            //社区配额全部重新计算
            if (quota.getChannelType() == QuotaChannelType.SURVEY_PLUS) {
                syncAdminxQuotas.add(quota.getId());
            }
            if ((quota.getExpressionHash() == null || !DigestUtils.sha256Hex(quota.getExpression()).equals(quota.getExpressionHash())) && quota.getChannelType() == QuotaChannelType.XM_PLUS) {
                syncCemQuotas.add(quota.getId());
            }
        });

        long countCemResponse = 0;
        long countAdminxResponse = 0;
        if (cemQuota && !syncCemQuotas.isEmpty()) {
            countCemResponse = responseRepository.countBySurveyIdAndStatusAndCollectorMethodNot(survey.getId(), ResponseStatus.FINAL_SUBMIT, SurveyCollectorMethod.SURVEY_PLUS);
            log.info("survey {} calculateQuota cem quotas {}, responses {}", survey.getId(), JsonHelper.toJson(syncCemQuotas), countCemResponse);
        }
        if (adminxQuota && !syncAdminxQuotas.isEmpty()) {
            countAdminxResponse = responseRepository.countBySurveyIdAndStatusInAndCollectorMethod(survey.getId(), List.of(ResponseStatus.FINAL_SUBMIT,ResponseStatus.WAIT_AUDIT), SurveyCollectorMethod.SURVEY_PLUS);
            log.info("survey {} calculateQuota adminx quotas {}, responses {}", survey.getId(), JsonHelper.toJson(syncAdminxQuotas), countCemResponse);
        }
        boolean success = quotaAllMatchLimit.syncQuotaStat(orgId, survey.getId(), allQuotas,
                syncCemQuotas, countCemResponse,
                syncAdminxQuotas, countAdminxResponse);
        if (success) {
            log.info("survey {} calculateQuota skip, all quotas are latest", survey.getId());
            return true;
        } else {
            if (cemQuota) {
                survey.setQuotaStatus(SurveyQuotaStatus.CALCULATING);
                log.info("cemQuota survey {} calculateQuota calculating", survey.getId());
            }
            if (adminxQuota) {
                survey.setAdminxQuotaStatus(SurveyQuotaStatus.CALCULATING);
                log.info("adminxQuota survey {} calculateQuota calculating", survey.getId());
            }
            surveyService.save(survey);
            return false;
        }
    }

    private SurveyProcessDto calculateQuotaProgress(long orgId, Survey survey) {
        SurveyProcessDto processDto = new SurveyProcessDto();
        processDto.setStatus(survey.getStatus());
        TaskProgressDto taskProgressDto = quotaAllMatchLimit.syncProgress(orgId, survey.getId());
        if (taskProgressDto != null && List.of(TaskStatus.INIT, TaskStatus.RUNNING, TaskStatus.SUCCESS).contains(taskProgressDto.getStatus())) {
            processDto.setCompleted(taskProgressDto.getStatus() == TaskStatus.SUCCESS);
            processDto.setProcess(Double.valueOf(taskProgressDto.getCompletedSize() * 100.0 / taskProgressDto.getTotalSize()).intValue());
            processDto.setTotal((int) taskProgressDto.getTotalSize());
            processDto.setSuccess((int) taskProgressDto.getCompletedSize());
        } else {
            processDto.setCompleted(false);
            processDto.setProcess(0);
            processDto.setTotal(0);
            processDto.setSuccess(0);
            if (taskProgressDto == null || taskProgressDto.getStatus() == TaskStatus.FAILED) {
                quotaAllMatchLimit.syncCompleted(survey.getId(), false);
                survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_INIT);
                surveyService.save(survey);
                log.info("syncQuotaFailure {}", survey.getId());
            }
        }
        return processDto;
    }

    /**
     * worker 调用了此方法
     */
    @Transactional
    public void syncQuotaComplete(long surveyId, boolean success, boolean cemQuota) {
        Survey survey = surveyService.requireSurvey(surveyId);
        survey.getQuotas().forEach(quota -> {
            String sha256 = DigestUtils.sha256Hex(quota.getExpression());
            if (quota.getExpressionHash() == null || !sha256.equals(quota.getExpressionHash())) {
                quota.setExpressionHash(sha256);
                quotaRepository.save(quota);
            }
        });
        quotaAllMatchLimit.syncCompleted(surveyId, success);
        if (!cemQuota) {
            survey.setAdminxQuotaStatus(SurveyQuotaStatus.CALCULATE_COMPLETE);
        } else {
            survey.setQuotaStatus(SurveyQuotaStatus.CALCULATE_COMPLETE);
        }

        surveyService.save(survey);
        log.info("syncQuotaComplete {}", surveyId);
    }

    /**
     * worker 调用了此方法
     */
    @Transactional
    public void syncCemQuotaByPage(long surveyId, List<Long> syncQuotas, int page, int size, Consumer<SurveyResponse> consumer) {
        syncQuotaByPage("syncCemQuotaByPage", surveyId, syncQuotas, page, size, consumer, pageable ->
                responseRepository.findBySurveyIdAndStatusAndCollectorMethodNot(
                        surveyId,
                        ResponseStatus.FINAL_SUBMIT,
                        SurveyCollectorMethod.SURVEY_PLUS, pageable));
    }

    /**
     * worker 调用了此方法
     */
    @Transactional
    public void syncAdminxQuotaByPage(long surveyId, List<Long> syncQuotas, int page, int size, Consumer<SurveyResponse> consumer) {
        syncQuotaByPage("syncAdminxQuotaByPage", surveyId, syncQuotas, page, size, consumer, pageable ->
                responseRepository.findBySurveyIdAndStatusInAndCollectorMethod(
                        surveyId,
                        List.of(ResponseStatus.FINAL_SUBMIT,ResponseStatus.WAIT_AUDIT),
                        SurveyCollectorMethod.SURVEY_PLUS, pageable));
    }

    private void syncQuotaByPage(String tag, long surveyId, List<Long> syncQuotas, int page, int size,
                                 Consumer<SurveyResponse> consumer,
                                 Function<Pageable, List<SurveyResponse>> getResponses) {
        Pageable pageable = PageRequest.of(page, size, Sort.by("id"));
        List<SurveyResponse> responses = getResponses.apply(pageable);
        if (CollectionUtils.isEmpty(responses)) {
            log.info("{} skip, page = {}, size = {}, responses is empty", tag, page, size);
            return;
        }
        List<SurveyQuota> quotas = quotaRepository.findAllByIdIn(syncQuotas);
        if (CollectionUtils.isEmpty(quotas)) {
            log.info("{} skip, page = {}, size = {}, quotas is empty", tag, page, size);
            return;
        }
        Survey survey = surveyService.get(surveyId);
        long start = System.currentTimeMillis();
        log.info("{} start, page = {}, size = {}", tag, page, size);
        try {
            Map<Long, List<Long>> quotaIdsMap = new HashMap<>();
            responses.forEach(response -> {
                try {
//                    List<SurveyQuota> triggerQuotas = quotas.stream().filter(i -> RandomUtils.nextBoolean()).collect(Collectors.toList());
                    List<SurveyQuota> triggerQuotas = responseService.quotaTriggerResponse(survey.getQuestions(), quotas, response);
                    log.debug("{} survey: {} response: {} trigger quotas: {}", tag, surveyId, response.getId(), JsonHelper.toJson(triggerQuotas));
                    triggerQuotas.forEach(quota -> {
                        quotaIdsMap.computeIfAbsent(quota.getId(), i -> new ArrayList<>()).add(response.getId());
                    });
                } catch (Throwable e) {
                    log.error("{} error, response {}", tag, response.getId());
                } finally {
                    consumer.accept(response);
                }
            });
            quotaAllMatchLimit.syncBatchAddQuota(surveyId, quotaIdsMap);
        } finally {
            log.info("{} end, page = {}, size = {}, cost = {}", tag, page, size, System.currentTimeMillis() - start);
        }
    }
}
