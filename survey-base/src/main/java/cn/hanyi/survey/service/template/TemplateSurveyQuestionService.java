package cn.hanyi.survey.service.template;

import cn.hanyi.survey.core.constant.Template.TemplateType;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyDto;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.template.TemplateGroup;
import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestion;
import cn.hanyi.survey.core.entity.template.TemplateSurveyQuestionDto;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.core.repository.TemplateSurveyQuestionRepository;
import cn.hanyi.survey.core.utilis.SurveyUtils;
import cn.hanyi.survey.dto.template.QuestionsConvertTemplateDto;
import cn.hanyi.survey.dto.template.TemplateConvertQuestionDto;
import cn.hanyi.survey.dto.template.TemplateSurveyQueryDto;
import cn.hanyi.survey.service.QuestionService;
import cn.hanyi.survey.service.SurveyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class TemplateSurveyQuestionService extends BaseService<TemplateSurveyQuestion, TemplateSurveyQuestionDto, TemplateSurveyQuestionRepository> {

    @Autowired
    SurveyQuestionRepository surveyQuestionRepository;

    @Autowired
    SurveyService surveyService;

    @Autowired
    QuestionService questionService;

    @Autowired
    TemplateSurveyQuestionRepository templateSurveyQuestionRepository;

    @Autowired
    TemplateGroupService templateGroupService;

    @Autowired
    TemplateMapService templateMapService;

    @Autowired
    UserService userService;


    @Override
    public <S extends ResourceCustomQueryDto> Page<TemplateSurveyQuestionDto> findAll(S query) {
        TemplateSurveyQueryDto dto = (TemplateSurveyQueryDto) query;
        Long searchGroupId = dto.getGroupId() == null || dto.getGroupId() < 0 ? null : dto.getGroupId();
        ResourceEntityQueryDto<TemplateSurveyQuestionDto> params = dto.transform();

        if (StringUtils.isNotEmpty(dto.getQ())) {
            params.getQueryCriteriaList().add(new ResourceQueryCriteria("title", dto.getQ(), QueryOperator.LIKE));
        }
        if (searchGroupId != null) {
            params.getQueryCriteriaList().add(new ResourceQueryCriteria("group", searchGroupId));
        }
        params.getQueryCriteriaList().add(new ResourceQueryCriteria("survey", null, QueryOperator.IS_NULL));
        params.getQueryCriteriaList().add(new ResourceQueryCriteria("orgId", TenantContext.getCurrentTenant()));
        return findAll(params);
    }

    public TemplateSurveyQuestion requireTemplateQuestion(Long questionId) {
        Optional<TemplateSurveyQuestion> questionOptional = templateSurveyQuestionRepository.findById(questionId);
        if (questionOptional.isEmpty()) {
            throw new EntityNotFoundException();
        }
        return questionOptional.get();
    }

    public SurveyDto convertToSurveyQuestion(TemplateConvertQuestionDto questionsConvertDto) {
        Survey survey = surveyService.requireSurvey(questionsConvertDto.getSurveyId());

        // List<SurveyQuestion> questions = new ArrayList<>();
        questionsConvertDto.getQuestions().forEach(questionIdSequence -> {
            try {
                TemplateSurveyQuestion templateSurveyQuestion = requireTemplateQuestion(questionIdSequence.getQuestionId());
                SurveyQuestion question = templateMapService.mapToQuestion(templateSurveyQuestion, false);
                Optional.ofNullable(questionIdSequence.getSequence()).ifPresent(question::setSequence);
                Optional.ofNullable(questionIdSequence.getCode()).ifPresent(question::setCode);
                Optional.ofNullable(questionIdSequence.getName()).ifPresent(question::setName);
                Optional.ofNullable(questionIdSequence.getTitle()).ifPresent(question::setTitle);
                question.setGroupCode(questionIdSequence.getGroupCode());

                SurveyUtils.insertQuestion(question, survey::getQuestions, q -> q.setSurvey(survey));
                //questions.add(question);
                surveyQuestionRepository.save(question);
            } catch (Exception e) {
                log.error("模板题目转题型失败", e);
            }
        });
        //只返回从模板里选择的问题
        // survey.getQuestions().clear();
        //survey.setQuestions(questions);
        return surveyService.mapToDto(survey);
    }

    public boolean updateTemplate(long id, Long groupId, String templateName) {
        TemplateSurveyQuestion entity = require(id);
        Optional.ofNullable(groupId).ifPresent(i -> {
            TemplateGroup group = null;
            if (groupId <= 0) {
                group = templateGroupService.getDefaultGroup(TemplateType.QUESTION, groupId);
            } else {
                group = templateGroupService.require(groupId);
                templateGroupService.checkIsCurrentOrg(group);
            }
            entity.setGroup(group);
        });
        Optional.ofNullable(templateName).ifPresent(entity::setTitle);
        templateSurveyQuestionRepository.save(entity);
        return true;
    }

    public List<TemplateSurveyQuestionDto> convertToTemplate(QuestionsConvertTemplateDto questionsConvertTemplateDto) {
        TemplateGroup group = null;
        // -1 0 不存在数据库 默认保存到未分组
        if (questionsConvertTemplateDto.getGroupId() > 0) {
            group = templateGroupService.require(questionsConvertTemplateDto.getGroupId());
        } else {
            group = templateGroupService.getEmptyGroup(TemplateType.QUESTION);
        }
        List<TemplateSurveyQuestion> templateSurveyQuestions = new ArrayList<>();
        TemplateGroup finalGroup = group;

        questionsConvertTemplateDto.getQuestionIds().forEach(questionId -> {
            try {
                SurveyQuestion question = questionService.requireQuestion(questionId);
                TemplateSurveyQuestion templateSurveyQuestion = new TemplateSurveyQuestion();
                templateMapService.mapToTemplate(question, templateSurveyQuestion, false);
                templateSurveyQuestion.setGroup(finalGroup);

                templateSurveyQuestions.add(templateSurveyQuestionRepository.save(templateSurveyQuestion));
            } catch (Exception e) {
                log.error("题目转模板失败", e);
            }
        });

        return mapToDto(templateSurveyQuestions);
    }

    @Override
    public void afterMapToDto(List<TemplateSurveyQuestion> entity, List<TemplateSurveyQuestionDto> dto) {
        Set<Long> updateUserIds = new HashSet<>();
        dto.forEach(question -> {
            if (question.getGroup() == null) {
                question.setGroup(templateGroupService.mapToDto(templateGroupService.getEmptyGroup(TemplateType.SURVEY)));
            }
            Optional.ofNullable(question.getUserId()).ifPresent(updateUserIds::add);
        });
        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
        dto.forEach(survey -> {
            Optional.ofNullable(survey.getUserId()).ifPresent(editorUser -> survey.setCreator(userMap.get(editorUser)));
        });
    }
}
