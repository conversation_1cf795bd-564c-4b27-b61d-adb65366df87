package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.dto.SurveyCrossAnalysisQuestionDto;
import cn.hanyi.survey.core.dto.SurveyCrossAnalysisQuestionsDto;
import cn.hanyi.survey.core.dto.SurveyCrossAnalysisRequestDto;
import cn.hanyi.survey.core.dto.SurveyCrossAnalysisSimpleDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.projection.SimpleResponse2;
import cn.hanyi.survey.core.repository.SurveyCrossAnalysisRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.exception.BusinessException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.core.utils.ListHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Slf4j
@Service
public class SurveyCrossAnalysisService extends CustomEmbeddedService<SurveyCrossAnalysis, SurveyCrossAnalysisDto, SurveyCrossAnalysisRepository> {
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;
    @Autowired
    private QuestionService questionService;
    @Autowired
    private AnalyticService analyticService;

    @Transactional
    public List<SurveyCrossAnalysisDto> batchAdd(Long surveyId, SurveyCrossAnalysisRequestDto data) {
        if (CollectionUtils.isEmpty(data.getSelectRows()) || data.getSelectRows().size() > 2) {
            throw new BusinessException("行不能为空，并且不能超过2行");
        }
        if (CollectionUtils.isEmpty(data.getSelectColumns())) {
            throw new BusinessException("列不能为空");
        }
        List<SurveyCrossAnalysis> list = data.getSelectColumns().stream().map(c -> {
            SurveyCrossAnalysis entity = new SurveyCrossAnalysis();
            entity.setSid(surveyId);
            entity.setSelectRows(data.getSelectRows());
            entity.setSelectColumns(List.of(c));
            return entity;
        }).collect(Collectors.toList());
        repository.saveAll(list);
        return mapToDto(list);
    }

    @Transactional
    public SurveyCrossAnalysisDto update(Long surveyId, Long crossAnalysisId, SurveyCrossAnalysisRequestDto data) {
        SurveyCrossAnalysis entity = repository.findById(crossAnalysisId).orElse(null);
        if (entity == null || !surveyId.equals(entity.getSid())) {
            throw new EntityNotFoundException(SurveyCrossAnalysis.class);
        }
        if (CollectionUtils.isEmpty(data.getSelectRows()) || data.getSelectRows().size() > 2) {
            throw new BusinessException("行不能为空，并且不能超过2行");
        }
        if (CollectionUtils.isEmpty(data.getSelectColumns()) || data.getSelectColumns().size() != 1) {
            throw new BusinessException("列不能为空，并且只能是1列");
        }
        entity.setSelectRows(data.getSelectRows());
        entity.setSelectColumns(data.getSelectColumns());
        repository.save(entity);
        return mapToDto(entity);
    }

    @Override
    public void afterMapToDto(SurveyCrossAnalysis entity, SurveyCrossAnalysisDto dto) {
        // 1 row1 2 row2 3 column
        analyticService.checkVersionLimit();
        List<Long> qIds = new ArrayList<>();
        Map<Integer, SurveyCrossAnalysisSimpleDto> simpleQuestionMap = parseSimpleQuestionMap(dto);
        Map<Integer, SurveyQuestion> questionMap = loadQuestionMap(dto.getSid(), qIds, simpleQuestionMap);

        AtomicInteger index = new AtomicInteger(0);
        Question row1 = Question.create(true, index, questionMap.get(1), simpleQuestionMap.get(1));
        Question row2 = Question.create(false, index, questionMap.get(2), simpleQuestionMap.get(2));
        Question column = Question.create(true, index, questionMap.get(3), simpleQuestionMap.get(3));
        Map<Long, Boolean[]> responseMap = loadResponseMap(dto.getSid(), qIds, index.get(), row1, row2, column);

        buildTable(dto, row1, row2, column, responseMap);
    }

    private Map<Integer, SurveyCrossAnalysisSimpleDto> parseSimpleQuestionMap(SurveyCrossAnalysisDto dto) {
        Map<Integer, SurveyCrossAnalysisSimpleDto> map = new HashMap<>();
        if (CollectionUtils.isEmpty(dto.getSelectRows()) || dto.getSelectRows().size() > 2) {
            throw new BusinessException("行不能为空，并且不能超过2行");
        } else {
            map.put(1, dto.getSelectRows().get(0));
            if (dto.getSelectRows().size() == 2) {
                map.put(2, dto.getSelectRows().get(1));
            }
        }
        if (CollectionUtils.isEmpty(dto.getSelectColumns()) || dto.getSelectColumns().size() != 1) {
            throw new BusinessException("列不能为空，并且只能是1列");
        } else {
            map.put(3, dto.getSelectColumns().get(0));
        }
        return map;
    }

    private Map<Integer, SurveyQuestion> loadQuestionMap(Long surveyId, List<Long> qIds, Map<Integer, SurveyCrossAnalysisSimpleDto> simpleQuestionMap) {
        List<String> questionNames = simpleQuestionMap.values().stream().map(SurveyCrossAnalysisSimpleDto::getName).collect(Collectors.toList());
        Survey survey = new Survey();
        survey.setId(surveyId);
        List<SurveyQuestion> questions = surveyQuestionRepository.findBySurveyAndNameIn(survey, questionNames);
        if (CollectionUtils.isEmpty(questions)) {
            throw new BusinessException("问题或者测量项已被删除");
        }
        questionService.replaceDynamicItems(questions);
        Map<Integer, SurveyQuestion> map = new HashMap<>();
        IntStream.range(1, 4).forEach(index -> {
            Optional.ofNullable(simpleQuestionMap.get(index)).ifPresent(s -> {
                questions.stream().filter(i -> s.getName().equals(i.getName())).findFirst().ifPresentOrElse(j -> {
                    map.put(index, j);
                    qIds.add(j.getId());
                }, () -> {
                    throw new BusinessException("问题或者测量项已被删除");
                });
            });
        });
        return map;
    }

    private Map<Long, Boolean[]> loadResponseMap(Long surveyId, List<Long> qIds, int length, Question row1, Question row2, Question column) {
        Map<Long, Boolean[]> map = new HashMap<>();
        List<Question> questions = ListHelper.arrayList(row1, row2, column);
        pageableLoadResponse(surveyId, 5000, list -> {
            list.forEach(i -> map.put(i.getId(), new Boolean[length]));
            long min = list.get(0).getId();
            long max = list.get(list.size() - 1).getId();
            List<SurveyResponseCell> responseCells = getCellByRIdRange(surveyId, qIds, min, max);
            if (CollectionUtils.isNotEmpty(responseCells)) {
                responseCells.forEach(cell -> {
                    Boolean[] rowValues = map.get(cell.getResponseId());
                    if (rowValues != null) {
                        questions.forEach(q -> {
                            if (q.matchQuestion(cell)) {
                                List<Integer> index = q.getMatchedItemIndex(cell);
                                if (CollectionUtils.isNotEmpty(index)) {
                                    index.forEach(i -> {
                                        if (i < rowValues.length) {
                                            rowValues[i] = true;
                                        }
                                    });
                                }
                            }
                        });
                    }
                });
            }
        });
        return map;
    }

    private void pageableLoadResponse(Long surveyId, int batchSize, Consumer<List<SimpleResponse2>> consumer) {
        Long minId = 0L;
        boolean hasNext = true;
        List<SimpleResponse2> list;
        do {
            entityManager.clear();
            list = findResponse(surveyId, minId, batchSize);
            if (CollectionUtils.isNotEmpty(list)) {
                consumer.accept(list);
                minId = list.get(list.size() - 1).getId();
            } else {
                hasNext = false;
            }
        } while (hasNext);
    }

    private List<SimpleResponse2> findResponse(Long surveyId, Long minId, int limit) {
        return surveyResponseRepository.findSimpleBySurveyIdAndStatusAndIdGreaterThan(surveyId, ResponseStatus.FINAL_SUBMIT, minId, PageRequest.of(0, limit, Sort.by("id")));
    }

    private List<SurveyResponseCell> getCellByRIdRange(Long surveyId, List<Long> qIds, long minRId, long maxRId) {
        return surveyResponseCellRepository.findBySurveyIdAndQuestionIdInAndResponseIdBetween(surveyId, qIds, minRId, maxRId);
    }

    private void buildTable(SurveyCrossAnalysisDto dto, Question row1, Question row2, Question column, Map<Long, Boolean[]> responseMap) {
        List<Row> rows = new ArrayList<>();
        row1.items.forEach(question1Item -> {
            if (row2 == null) {
                rows.add(new Row(question1Item, null, column));
            } else {
                row2.items.forEach(question2Item -> rows.add(new Row(question1Item, question2Item, column)));
            }
        });
        responseMap.values().forEach(response -> {
            rows.forEach(r -> r.fillValues(response));
        });

        Map<String, Object> columnTotal = new HashMap<>();
        List<String> columnValues = columnValues(row1, row2, column, columnTotal);
        List<Map<String, Object>> rowValues = rows.stream().map(r -> r.rowValues(columnTotal)).collect(Collectors.toList());
        if (row2 == null) {
            rowValues.add(columnTotal);
        }
        dto.setQuestions(new SurveyCrossAnalysisQuestionsDto(
                row1.getQuestion(),
                row2 == null ? null : row2.getQuestion(),
                column.getQuestion()
        ));
        dto.setColumns(columnValues);
        dto.setRows(rowValues);
    }

    private List<String> columnValues(Question row1, Question row2, Question column, Map<String, Object> columnTotal) {
        List<String> columns = new ArrayList<>();
        columns.add(row1.getQuestion().getFullTitle());
        columnTotal.put(row1.getQuestion().getFullTitle(), "总计");
        if (row2 != null) {
            columns.add(row2.getQuestion().getFullTitle());
        }
        column.items.forEach(i -> {
            columns.add(i.key);
            columnTotal.put(i.key, 0);
        });
        columns.add("总计");
        columnTotal.put("总计", 0);
        return columns;
    }

    @Getter
    @Setter
    public static abstract class Question {
        private SurveyCrossAnalysisQuestionDto question;
        private List<QuestionItem> items;
        private Map<String, QuestionItem> itemMap = new LinkedHashMap<>();

        public static Question create(boolean notNull, AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            if ((question == null || simple == null)) {
                if (notNull) {
                    throw new BusinessException("问题或者测量项已被删除");
                } else {
                    return null;
                }
            }
            Question q;
            switch (question.getType()) {
                case SINGLE_CHOICE -> q = new Question_SINGLE_CHOICE();
                case COMBOBOX -> q = new Question_COMBOBOX();
                case MULTIPLE_CHOICES -> q = new Question_MULTIPLE_CHOICES();
                case SCORE -> q = new Question_SCORE();
                case MATRIX_SCORE -> q = new Question_MATRIX_SCORE();
                case MATRIX_CHOICE -> q = new Question_MATRIX_CHOICE();
                default -> throw new BusinessException("不支持的问题类型");
            }
            q.create(index, question, simple);
            return q;
        }

        public Question create(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            this.question = SurveyCrossAnalysisQuestionDto.mapFromQuestion(question, simple);
            if (this.question == null) {
                throw new BusinessException("问题或者测量项已被删除");
            }
            if (!isValidQuestion()) {
                throw new BusinessException("问题或者测量项已被删除");
            }
            this.items = parseItems(index, question, simple);
            this.items.forEach(item -> itemMap.put(item.value, item));
            return this;
        }

        public boolean matchQuestion(SurveyResponseCell cell) {
            return cell != null && cell.getQuestionId() != null && cell.getQuestionId().equals(getQuestion().getId());
        }

        public abstract List<QuestionItem> parseItems(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple);

        public abstract List<Integer> getMatchedItemIndex(SurveyResponseCell cell);

        public boolean isValidQuestion() {
            return true;
        }
    }

    public static class Question_SINGLE_CHOICE extends Question {

        @Override
        public List<QuestionItem> parseItems(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            return question.getItems().stream().map(i -> new QuestionItem(index, getQuestion(), i)).collect(Collectors.toList());
        }

        @Override
        public List<Integer> getMatchedItemIndex(SurveyResponseCell cell) {
            List<Integer> matchedIndex = new ArrayList<>();
            if (StringUtils.isNotEmpty(cell.getStrValue())) {
                String[] as = cell.getStrValue().split(";");
                for (String s : as) {
                    QuestionItem item = getItemMap().get(s);
                    if (item != null) {
                        matchedIndex.add(item.index);
                        break;
                    }
                }
            }
            return matchedIndex;
        }
    }

    public static class Question_COMBOBOX extends Question {

        @Override
        public List<QuestionItem> parseItems(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            return question.getItems().stream().map(i -> new QuestionItem(index, getQuestion(), i)).collect(Collectors.toList());
        }

        @Override
        public List<Integer> getMatchedItemIndex(SurveyResponseCell cell) {
            List<Integer> matchedIndex = new ArrayList<>();
            if (StringUtils.isNotEmpty(cell.getStrValue())) {
                String[] as = cell.getStrValue().split(";");
                for (String s : as) {
                    QuestionItem item = getItemMap().get(s);
                    if (item != null) {
                        matchedIndex.add(item.index);
                        break;
                    }
                }
            }
            return matchedIndex;
        }
    }

    public static class Question_MULTIPLE_CHOICES extends Question {

        @Override
        public List<QuestionItem> parseItems(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            return question.getItems().stream().map(i -> new QuestionItem(index, getQuestion(), i)).collect(Collectors.toList());
        }

        @Override
        public List<Integer> getMatchedItemIndex(SurveyResponseCell cell) {
            List<Integer> matchedIndex = new ArrayList<>();
            if (StringUtils.isNotEmpty(cell.getStrValue())) {
                String[] as = cell.getStrValue().split(";");
                for (String s : as) {
                    QuestionItem item = getItemMap().get(s);
                    if (item != null) {
                        matchedIndex.add(item.index);
                    }
                }
            }
            return matchedIndex;
        }
    }

    public static class Question_SCORE extends Question {

        @Override
        public List<QuestionItem> parseItems(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            List<QuestionItem> items = new ArrayList<>();
            if (question.getIsNps() != null && question.getIsNps()) {
                IntStream.range(0, 11).forEach(i -> {
                    items.add(new QuestionItem(index, getQuestion(), i, i + "分"));
                });
            } else {
                IntStream.range(question.getMin(), question.getMax() + 1).forEach(i -> {
                    items.add(new QuestionItem(index, getQuestion(), i, i + "分"));
                });
            }
            if (question.getInapplicable() != null && question.getInapplicable()) {
                items.add(new QuestionItem(index, getQuestion(), -1, question.getInapplicableLabel()));
            }
            return items;
        }

        @Override
        public List<Integer> getMatchedItemIndex(SurveyResponseCell cell) {
            List<Integer> matchedIndex = new ArrayList<>();
            Integer intValue = cell.getIntValue();
            if (intValue != null) {
                QuestionItem item = getItemMap().get(intValue.toString());
                if (item != null) {
                    matchedIndex.add(item.index);
                }
            }
            return matchedIndex;
        }
    }


    public static class Question_MATRIX_SCORE extends Question {

        @Override
        public List<QuestionItem> parseItems(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            List<QuestionItem> items = new ArrayList<>();
            IntStream.range(question.getMin(), question.getMax() + 1).forEach(i -> {
                items.add(new QuestionItem(index, getQuestion(), i, i + "分"));
            });
            if (question.getInapplicable() != null && question.getInapplicable()) {
                items.add(new QuestionItem(index, getQuestion(), -1, question.getInapplicableLabel()));
            }
            return items;
        }

        @Override
        public boolean isValidQuestion() {
            return StringUtils.isNotEmpty(getQuestion().getMatrixValue());
        }

        @Override
        public List<Integer> getMatchedItemIndex(SurveyResponseCell cell) {
            List<Integer> matchedIndex = new ArrayList<>();
            Map<String, Object> jsonValue = cell.getJsonValue();
            if (jsonValue != null) {
                Object value = jsonValue.get(getQuestion().getMatrixValue());
                if (value != null) {
                    QuestionItem item = getItemMap().get(value.toString());
                    if (item != null) {
                        matchedIndex.add(item.index);
                    }
                }
            }
            return matchedIndex;
        }
    }

    public static class Question_MATRIX_CHOICE extends Question {

        @Override
        public List<QuestionItem> parseItems(AtomicInteger index, SurveyQuestion question, SurveyCrossAnalysisSimpleDto simple) {
            return question.getColumns().stream().map(i -> new QuestionItem(index, getQuestion(), i)).collect(Collectors.toList());
        }

        @Override
        public boolean isValidQuestion() {
            return StringUtils.isNotEmpty(getQuestion().getMatrixValue());
        }

        @Override
        public List<Integer> getMatchedItemIndex(SurveyResponseCell cell) {
            List<Integer> matchedIndex = new ArrayList<>();
            Map<String, Object> jsonValue = cell.getJsonValue();
            if (jsonValue != null) {
                Object value = jsonValue.get(getQuestion().getMatrixValue());
                if (value != null) {
                    QuestionItem item = getItemMap().get(value.toString());
                    if (item != null) {
                        matchedIndex.add(item.index);
                    }
                }
            }
            return matchedIndex;
        }
    }

    @Getter
    @Setter
    public static class QuestionItem {
        private SurveyCrossAnalysisQuestionDto question;
        private String text;
        private String value;
        private String key;
        private int index;
        private int count;

        public QuestionItem(AtomicInteger index, SurveyCrossAnalysisQuestionDto question, SurveyQuestionItem item) {
            this.question = question;
            this.text = RegularExpressionUtils.replaceHtml(item.getText());
            this.value = item.getValue();
            this.key = "key:" + this.value + "-" + this.text;
            this.index = index.getAndIncrement();
        }

        public QuestionItem(AtomicInteger index, SurveyCrossAnalysisQuestionDto question, SurveyQuestionColumn item) {
            this.question = question;
            this.text = RegularExpressionUtils.replaceHtml(item.getText());
            this.value = item.getValue();
            this.key = "key:" + this.value + "-" + this.text;
            this.index = index.getAndIncrement();
        }

        public QuestionItem(AtomicInteger index, SurveyCrossAnalysisQuestionDto question, int score, String text) {
            this.question = question;
            this.text = text;
            this.value = String.valueOf(score);
            this.key = "key:" + this.value + "-" + this.text;
            this.index = index.getAndIncrement();
        }
    }


    @Getter
    @Setter
    public static class Row {
        private QuestionItem row1;
        private QuestionItem row2;
        private Map<QuestionItem, AtomicInteger> columnValueMap;

        public Row(QuestionItem row1, QuestionItem row2, Question column) {
            this.row1 = row1;
            this.row2 = row2;
            this.columnValueMap = new HashMap<>();
            column.items.forEach(i -> columnValueMap.put(i, new AtomicInteger(0)));
        }

        public void fillValues(Boolean[] values) {
            boolean selectRow1 = Optional.ofNullable(values[row1.index]).orElse(false);
            boolean selectRow2 = Optional.ofNullable(row2).map(i -> values[i.index]).orElse(false);
            if (selectRow1 && (row2 == null || selectRow2)) {
                columnValueMap.forEach((k, v) -> {
                    boolean selectColumn = Optional.ofNullable(values[k.index]).orElse(false);
                    if (selectColumn) {
                        v.incrementAndGet();
                    }
                });
            }
        }

        public Map<String, Object> rowValues(Map<String, Object> columnTotal) {
            Map<String, Object> map = new HashMap<>();
            map.put(row1.question.getFullTitle(), row1.text);
            if (row2 != null) {
                map.put(row2.question.getFullTitle(), row2.text);
            }
            AtomicInteger rowCount = new AtomicInteger(0);
            columnValueMap.forEach((k, v) -> {
                map.put(k.key, v.get());
                rowCount.addAndGet(v.get());
                columnTotal.put(k.key, Optional.ofNullable(columnTotal.get(k.key))
                        .map(Object::toString)
                        .filter(NumberUtils::isDigits)
                        .map(Integer::parseInt)
                        .orElse(0) + v.get());
            });
            map.put("总计", rowCount.get());
            columnTotal.put("总计", Optional.ofNullable(columnTotal.get("总计"))
                    .map(Object::toString)
                    .filter(NumberUtils::isDigits)
                    .map(Integer::parseInt)
                    .orElse(0) + rowCount.get());
            return map;
        }
    }

}
















