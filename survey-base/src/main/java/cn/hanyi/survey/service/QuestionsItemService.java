package cn.hanyi.survey.service;

import cn.hanyi.cem.core.constant.QuestionItemType;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyQuestionItemDto;
import cn.hanyi.survey.core.repository.SurveyQuestionItemRepository;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class QuestionsItemService extends CustomDeepEmbeddedService<SurveyQuestionItem, SurveyQuestionItemDto, SurveyQuestionItemRepository> {

    @Autowired
    private QuestionService questionService;

    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Override
    protected Object requireParent(long l) {
        return questionService.require(l);
    }


    @Override
    public Boolean deleteOneDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, Long deepId) {
        surveyEventTrigger.questionItemDelete(entityId, embeddedId, deepId, QuestionItemType.ITEM.name());
        return super.deleteOneDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, deepId);
    }

    @Override
    public void delete(SurveyQuestionItem entity) {
        var question = entity.getQuestion();
        surveyEventTrigger.questionItemDelete(question.getSurvey().getId(), question.getId(), entity.getId(), QuestionItemType.ITEM.name());
        super.delete(entity);
    }

    /**
     * 批量插入问题选项
     *
     * @param question
     * @param items
     * @return
     */
    public List<SurveyQuestionItemDto> insertQuestionItems(SurveyQuestion question, List<SurveyQuestionItemDto> items) {
        List<SurveyQuestionItem> is = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(items)) {
            items.forEach(i -> {
                SurveyQuestionItem ie = new SurveyQuestionItem();
                crudService.mapToEntity(i, ie);
                ie.setQuestion(question);
                is.add(ie);
            });
            repository.saveAll(is);
        }
        return mapToDto(is);
    }

    @Transactional
    public void deleteAll(SurveyQuestion question) {

        var items = question.getItems();

        if (CollectionUtils.isNotEmpty(items)) {
            items.forEach(i -> {
                surveyEventTrigger.questionItemDelete(question.getSurvey().getId(), question.getId(), i.getId(), QuestionItemType.ITEM.name());
            });
        }
        question.getItems().clear();
        repository.deleteAllByQuestion(question);
    }

    @Transactional
    public void deleteByIdIn(List<Long> itemIds) {
        if (CollectionUtils.isNotEmpty(itemIds)) {
            var question = require(itemIds.get(0)).getQuestion();
            itemIds.forEach(i -> {
                surveyEventTrigger.questionItemDelete(question.getSurvey().getId(), question.getId(), i, QuestionItemType.ITEM.name());
            });
        }

        repository.deleteByIdIn(itemIds);
    }


    public SurveyQuestionItem update(Long id, Long mid, Long did, Map<String, Object> data) {
        SurveyQuestionItemDto dto = mapperService.map(data, SurveyQuestionItemDto.class);

        SurveyQuestionItemDto surveyQuestionItemDto = updateOneDeepEmbeddedMany(id, "survey", mid, "question", did, dto);
        SurveyQuestionItem surveyQuestionItem = surveyQuestionItemDto.getEntity();

        // 删除逻辑跳转是把visibleIf设置为null
        if (data.containsKey("visibleIf")) {
            surveyQuestionItem.setVisibleIf(data.get("visibleIf") == null ? null : data.get("visibleIf").toString());
        }

        return repository.save(surveyQuestionItem);
    }

    @Override
    public List<SurveyQuestionItemDto> batchUpdateDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, ResourceBatchUpdateRequestDto<SurveyQuestionItemDto> batchChangeDto) {
        // groupCode
        List<Long> nullGroupCodeIds = batchChangeDto.getChanges().stream().filter(i -> i.getData().getGroupCode() == null).map(x -> x.getData().getId()).collect(Collectors.toList());
        List<SurveyQuestionItemDto> surveyQuestionItemDtos = super.batchUpdateDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, batchChangeDto);
        List<SurveyQuestionItem> groupCodeEntities = surveyQuestionItemDtos.stream().filter(i -> nullGroupCodeIds.contains(i.getId())).map(i -> {
            i.getEntity().setGroupCode(null);
            return i.getEntity();
        }).collect(Collectors.toList());
        repository.saveAll(groupCodeEntities);
        List<SurveyQuestionItem> byQuestion = repository.findByQuestionIdOrderBySequenceAsc(embeddedId);
        return mapToDto(byQuestion);
    }
}
