package cn.hanyi.survey.service;

import cn.hanyi.cem.core.dto.task.TaskNotifyInboxDto;
import cn.hanyi.cem.core.producer.TaskProducerHelper;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.constant.survey.SurveyVerifyStatus;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyVerifyRepository;
import cn.hanyi.survey.dto.verifyRecord.VerifySurveyStatusResultDto;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.UserService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.extension.constant.InboxMessageType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SurveyVerifyService extends CustomEmbeddedService<SurveyVerify, SurveyVerifyDto, SurveyVerifyRepository> {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyVerifyRepository verifyRepository;

    @Autowired
    private UserRepository userepository;

    @Autowired
    private UserService userService;
    @Autowired
    private SurveyVerifyRecordService verifyRecordService;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    @Autowired
    private SurveyContentAuditService surveyContentAuditService;
    @Autowired
    private TaskProducerHelper taskProducerHelper;
    @Value("${survey.cem-domain:}")
    private String cemDomain;

    @Override
    protected Object requireParent(long l) {
        return null;
    }

    /**
     * 查询问卷的审核人
     * <p>
     * sid 问卷id
     *
     * @return
     */
    public List<SimpleUser> findVerifyUserList(Long sid) {
        Survey survey = surveyService.requireSurvey(sid);
        List<SurveyVerify> verifyUserList = verifyRepository.findBySurveyIdAndVerifyVersion(sid, survey.getVerifyVersion());
        List<Long> uids = verifyUserList.stream().map(x -> x.getUserId()).collect(Collectors.toList());
        List<User> users = userepository.findAllById(uids);
        List<SimpleUser> userList = new ArrayList<>();
        users.stream().forEach(x -> {
            userList.add(SimpleUser.fromUser(x));
        });
        return userList;
    }

    /**
     * 返回问卷状态和登录用户是否有审核权限
     *
     * @param surveyId
     * @return
     */
    public VerifySurveyStatusResultDto getSurveyStatusAndVerify(Long surveyId) {
        Optional<Survey> survey = surveyRepository.findById(surveyId);
        if (!survey.isPresent()) {
            throw new BadRequestException("问卷数据已删除");
        }
        SurveyStatus status = survey.get().getStatus();
        VerifySurveyStatusResultDto dto = new VerifySurveyStatusResultDto();
        dto.setStatus(status);
        dto.setVerify(false);
        if (survey.isPresent()) {
            Long userid = TenantContext.getCurrentUserId();
            SurveyVerify surveyVerify = verifyRepository.findBySurveyIdAndUserIdAndVerifyVersion(surveyId, userid, survey.get().getVerifyVersion());
            if (surveyVerify != null) {
                dto.setVerify(true);
            }
            return dto;
        }
        return dto;
    }

    /**
     * 审核列表中单个问卷详情
     *
     * @param sid
     * @return
     */
    public SurveyDto getOneVerifySurvey(Long sid) {
        Long userid = TenantContext.getCurrentUserId();
        Survey survey = surveyRepository.getOneVerifySurvey(sid, userid);
        return surveyService.mapToDto(survey);
    }

    /**
     * 判断是否打开后台问卷审核
     */
    public void isOpenVerify() {
        Boolean isOpenVerify = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyVerify).getSurveyVerify();
        if (!isOpenVerify) {
            throw new BadRequestException("管理员已取消问卷审核流程");
        }
    }


    /**
     * 问卷提交审核
     *
     * @param sid
     * @return
     */
    @Transactional
    public SurveyDto commitVerify(Long sid, List<Long> userIds) {
        if (userIds == null || userIds.isEmpty()) {
            throw new BadRequestException();
        }
        //问卷前置判断
        isOpenVerify();
        Survey survey = surveyService.requireWithFilter(sid);

        SurveyStatus.checkToAuditing(survey.getStatus());
        surveyContentAuditService.checkContentAudit(survey, false);

        //每次提交问卷版本号加一
        Integer version = survey.getVerifyVersion() + 1;
        survey.setVerifyVersion(version);
        survey.setStatus(SurveyStatus.AUDITING);
        surveyRepository.save(survey);

        //保存审核人
        List<SurveyVerify> surveyVerifies = new ArrayList<>();
        for (Long userId : userIds) {
            SurveyVerify surveyVerify = new SurveyVerify();
            surveyVerify.setSurveyId(sid);
            surveyVerify.setUserId(userId);
            surveyVerify.setVerifyVersion(version);
            surveyVerifies.add(surveyVerify);
        }
        saveAll(surveyVerifies);

        //保存提交审核记录 只在提交时记录版本
        saveVerifyRecord(sid, SurveyVerifyStatus.AUDITING, version);
        //站内信通知给审核者
        inboxNotice(survey, SurveyVerifyStatus.AUDITING, userIds);
        return surveyService.mapToDto(survey);
    }

    /**
     * 问卷审核撤销问卷状态为停用
     *
     * @param sid
     * @return
     */
    @Transactional
    public SurveyDto withdrawSurvey(Long sid) {
        //前置判断
        isOpenVerify();
        Survey survey = surveyService.requireWithFilter(sid);

        SurveyStatus.checkToStopped(survey.getStatus());

        //修改问卷状态
        survey.setStatus(SurveyStatus.STOPPED);
        surveyRepository.save(survey);

        //保存撤销记录
        saveVerifyRecord(sid, SurveyVerifyStatus.WITHDRAW, null);
        // 撤销通知给审核者
        List<SimpleUser> verifyUserList = findVerifyUserList(sid);
        inboxNotice(survey, SurveyVerifyStatus.WITHDRAW, verifyUserList.stream().map(SimpleUser::getId).collect(Collectors.toList()));
        return surveyService.mapToDto(survey);
    }

    /**
     * 立即审核：包括通过和驳回
     *
     * @param sid
     * @return
     */
    @Transactional
    public SurveyDto verifyNow(Long sid, String status, String comment) {
        //前置判断
        if (!"approval".equals(status) && !"reject".equals(status)) {
            throw new BadRequestException();
        }
        isOpenVerify();

        Survey survey = surveyService.requireSurvey(sid);

        if ("approval".equals(status)) {
            SurveyStatus.checkToApproval(survey.getStatus());
        } else {
            SurveyStatus.checkToRejected(survey.getStatus());
        }

        SurveyVerify surveyVerify = repository.findBySurveyIdAndUserIdAndVerifyVersion(survey.getId(), TenantContext.getCurrentUserId(), survey.getVerifyVersion());
        SurveyVerifyRecord surveyVerifyRecord = verifyRecordService.findBySurveyIdAndVerifyVersionAndOperation(sid, surveyVerify.getVerifyVersion(), SurveyVerifyStatus.AUDITING);
        SurveyVerifyStatus notifyStatus = null;
        if ("approval".equals(status)) {
            //保存审核人
            surveyVerify.setStatus(SurveyVerifyStatus.APPROVAL);
            save(surveyVerify);
            //审核记录
            saveVerifyRecord(sid, SurveyVerifyStatus.APPROVAL, comment);
            survey.setStatus(SurveyStatus.APPROVAL);
            notifyStatus = SurveyVerifyStatus.APPROVAL;
        } else {
            //保存审核人
            surveyVerify.setStatus(SurveyVerifyStatus.REJECTED);
            save(surveyVerify);
            //审核记录
            saveVerifyRecord(sid, SurveyVerifyStatus.REJECTED, comment);
            survey.setStatus(SurveyStatus.REJECTED);
            notifyStatus = SurveyVerifyStatus.REJECTED;
        }
        // 通知给发起人
        SurveyVerifyStatus finalNotifyStatus = notifyStatus;
        Optional.ofNullable(surveyVerifyRecord).ifPresent(n -> {
            inboxNotice(survey, finalNotifyStatus, new ArrayList<Long>() {{
                add(n.getUserId());
            }});
        });
        surveyRepository.save(survey);
        return surveyService.mapToDto(survey);
    }

    /**
     * 保存审核记录
     *
     * @param sid
     * @param status
     * @return
     */
    private SurveyVerifyRecord saveVerifyRecord(Long sid, SurveyVerifyStatus status, String comment) {
        SurveyVerifyRecord surveyVerifyRecord = SurveyVerifyRecord.builder()
                .surveyId(sid)
                .userId(TenantContext.getCurrentUserId())
                .operation(status)
                .comment(comment)
                .build();
        verifyRecordService.save(surveyVerifyRecord);
        return surveyVerifyRecord;
    }

    /**
     * 保存审核记录
     *
     * @param sid
     * @param status
     * @param version
     * @return
     */
    private SurveyVerifyRecord saveVerifyRecord(Long sid, SurveyVerifyStatus status, int version) {
        SurveyVerifyRecord surveyVerifyRecord = SurveyVerifyRecord.builder()
                .surveyId(sid)
                .userId(TenantContext.getCurrentUserId())
                .operation(status)
                .verifyVersion(version)
                .build();
        verifyRecordService.save(surveyVerifyRecord);
        return surveyVerifyRecord;
    }

    /**
     * 提交审核，撤销，通过，驳回站内信通知
     *
     * @param survey
     * @param status
     */
    private void inboxNotice(Survey survey, SurveyVerifyStatus status, List<Long> toUserIds) {
        SimpleUser user = userService.requireCurrentSimple();

        var inbox = new TaskNotifyInboxDto();
        inbox.setType(InboxMessageType.VERIFY.name());

        inbox.setTitle(String.format("%s%s了《%s》的审核", user.getTruename(), status.getText(), survey.getTitle()));
        inbox.setUserIds(new HashSet<Long>() {{
            addAll(toUserIds);
        }});
        inbox.setOrgId(TenantContext.getCurrentTenant());
        inbox.setTargetUrl(String.format("%stouchs/surveyEditor?id=%s&type=audit", cemDomain, survey.getId()));
        taskProducerHelper.notifyInbox(inbox, TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), survey.getId(), null);
    }

}






















