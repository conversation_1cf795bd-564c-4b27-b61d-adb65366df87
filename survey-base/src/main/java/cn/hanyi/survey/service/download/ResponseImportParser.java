package cn.hanyi.survey.service.download;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.service.QuestionResponseHelper;
import cn.hanyi.survey.client.service.TrackingService;
import cn.hanyi.survey.core.constant.survey.ResponseImportType;
import cn.hanyi.survey.core.dto.SurveyTrackingDataDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.channel.SendFileListener;
import cn.hanyi.survey.service.download.dto.ResponseImportContext;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import cn.hanyi.survey.workertrigger.ISurveyTaskTrigger;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.UserTaskService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.JsonHelper;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotNull;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ResponseImportParser {

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private ResponseDownloadHelper responseDownloadHelper;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private ISurveyTaskTrigger surveyTaskTrigger;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    protected ISurveyEventTrigger surveyEventTrigger;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private QuestionResponseHelper questionResponseHelper;


    @Value("${survey.response.import-file-max:5}")
    private Long importFileMax;

    @Value("${survey.response.import-size:10000}")
    private Integer importSize;

    public ResponseImportContext uploadResponse(Long surveyId, ResponseImportType responseImportType, MultipartFile file) {
        if (file.getSize() > (importFileMax * 1024 * 1024)) {
            throw new BadRequestException("文件必须少于5M");
        }
        FileInfo fileInfo = fileStorageService.of(file)
                .setObjectId("0")
                .upload();

        ResponseImportContext context = new ResponseImportContext(surveyId, fileInfo.getUrl(), false);

        try {
            parseExcelFile(context);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return context;
    }

    /**
     * 答卷导入task
     * @param context
     * @return
     */
    public Boolean importResponseTask(ResponseImportContext context) {
        if (StringUtils.isEmpty(context.getUrl())) {
            throw new BadRequestException("上传的答卷文件不存在");
        }
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();

        if (context.getSubmit()) {
            if (context.getAsync()) {//异步
                TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.responseImport, 0, null, context.getSurveyId());
                surveyTaskTrigger.responseImport(orgId, userId, progress.getId(), context.getSurveyId(), context.getUrl());
            } else {//同步
                importResponseConsumer(orgId, userId , context.getSurveyId(), context.getUrl());
            }
        } else {
            fileStorageService.delete(context.getUrl());
        }
        return true;
    }

    /**
     * cem-worker task消费responseImport 逻辑处理
     *
     * @param orgId
     * @param userId
     * @param surveyId
     * @param url
     */
    public void importResponseConsumer(Long orgId, Long userId, Long surveyId, String url) {
        ResponseImportContext context = new ResponseImportContext(surveyId, url, true);
        try {
            if (orgId != null && orgId > 0) context.setOrgId(orgId);
            if (userId != null && userId > 0) context.setUserId(userId);
            //解析上传的文件
            parseExcelFile(context);
            if (context.getResponseNum().get() > 0) {
                updateResponseFinishNum(context);
            }
            log.info("导入问卷:{} 成功", surveyId);
        } catch (Exception e) {
            log.info("导入问卷:{} 失败", surveyId);
            e.printStackTrace();
        }
        //删除上传的文件
        fileStorageService.delete(context.getUrl());
    }

    private void updateResponseFinishNum(ResponseImportContext context) {
        Survey survey = surveyService.requireSurvey(context.getSurveyId());
        if (survey.getResponseFinishNum() != null) {
            surveyService.updateResponseNum(context.getSurveyId(), context.getResponseNum().get());
        }
    }

    /**
     * 解析excel上传的答卷
     *
     * @param context
     */
    @Transactional
    public void parseExcelFile(ResponseImportContext context) {
        if (StringUtils.isNotEmpty(context.getUrl())) {
            //加载题目
            context.setQuestionList(responseDownloadHelper.buildTemplateSurveyQuestion(context));

            try (InputStream is = new ByteArrayInputStream(fileStorageService.download(context.getUrl()).bytes())) {
                List<Map<Integer, String>> headersList = new ArrayList<>();
                EasyExcel.read(is, new SendFileListener(headersList, dataList -> {

                    if (dataList.size() >= importSize) {
                        context.getErrorList().add("文件必须是少于5M的Excel文件，且最多不超过10000行");
                    }

                    context.setCount(dataList.size());
                    for (Map<Integer, String> header: headersList) {
                        context.getHeaders().putAll(header);
                    }
                    //校验header
                    validHeader(context);
                    if (!context.getErrorList().isEmpty()) return;
                    for (Map<Integer, String> data : dataList) {
                        parseField(context, data);
                    }
                }, importSize)).sheet().doRead();

            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            //如果有数据错误 去重 并且删除文件
            if (!context.getErrorList().isEmpty()) {
                context.setErrorList(context.getErrorList().stream().distinct().collect(Collectors.toList()));
                context.setResponseList(new ArrayList<>());
                fileStorageService.delete(context.getUrl());
            }

        }
    }

    public void validHeader(ResponseImportContext context) {
        if (context.getHeaders().isEmpty())
            throw new BadRequestException("上传文件列名字段错误，请检查！");
        if (!"提交时间".equals(context.getHeaders().get(0)))
            context.getErrorList().add("上传数据第一列必须为'提交时间'");
        if (!"开始时间".equals(context.getHeaders().get(1)))
            context.getErrorList().add("上传数据第二列必须为'开始时间'");
        if (!"ip".equals(context.getHeaders().get(2)))
            context.getErrorList().add("上传数据第三列必须为'ip'");
        if (!"答题时长(秒)".equals(context.getHeaders().get(3)))
            context.getErrorList().add("上传数据第四列必须为'答题时长(秒)'");
        if (!"外部客户ID".equals(context.getHeaders().get(4)))
            context.getErrorList().add("上传数据第五列必须为'外部客户ID'");
        if (!"外部组织编号".equals(context.getHeaders().get(5)))
            context.getErrorList().add("上传数据第六列必须为'外部组织编号'");
        if (!"外部参数".equals(context.getHeaders().get(6)))
            context.getErrorList().add("上传数据第七列必须为'外部参数'");
    }

    public void parseField(ResponseImportContext context, Map<Integer, String> data) {

        if (context.getQuestionList().isEmpty()) return;

        if (StringUtils.isEmpty(data.get(0)) || StringUtils.isEmpty(data.get(1))) {
            context.getErrorList().add("开始时间和提交时间为必填字段");
        }
        if (StringUtils.isNotEmpty(data.get(3)) && !StringUtils.isNumeric(data.get(3))) {
            context.getErrorList().add("答题时长必须为数字");
        }

        Date finishTime = StringUtils.isEmpty(data.get(0)) ? null : DateUtil.parse(data.get(0));
        Date startTime = StringUtils.isEmpty(data.get(1)) ? null : DateUtil.parse(data.get(1));
        String ip = data.get(2);
        Integer durationSeconds = StringUtils.isEmpty(data.get(3)) ? null : Integer.parseInt(data.get(3));
        String externalUserId = data.get(4);
        String departmentCode = data.get(5);
        Map<String,Object> parameters = StringUtils.isEmpty(data.get(6)) ? new HashMap<>() : JsonHelper.toMap(data.get(6));

        //如果答题时长为null或者0 用提交时间 - 开始时间计算
        if (durationSeconds == null || durationSeconds ==0) {
            durationSeconds = Math.toIntExact((finishTime.getTime() - startTime.getTime()) / 1000);
            if (durationSeconds < 0) durationSeconds = 0;
        }

        //重置 dataIndex 默认从第7列开始
        context.setDataIndex(7);
        if (context.getSubmit() || (!context.getSubmit() && context.getResponseList().size() <100)) {
            context.getResponseList().add(data);
        }

        List<SurveyResponseCell> cells = new ArrayList<>();
        SurveyResponse newResponse = new SurveyResponse();

        if (context.getSubmit()) {
            //构建答卷基础信息
            SurveyResponse response = new SurveyResponse(context.getSurveyId(), context.getOrgId(), startTime, finishTime, ip, durationSeconds, externalUserId, departmentCode, parameters);
            //解析ip
            if (StringUtils.isNotEmpty(ip)) {
                SurveyTrackingDataDto trackingDataDto = trackingService.parseIpAddress(ip);
                response.setCountry(trackingDataDto.getCountry());
                response.setProvince(trackingDataDto.getProvince());
                response.setCity(trackingDataDto.getCity());
            }
            //答卷编号
            response.setSequence(getSurveyResponseSequence(context.getSurveyId()));
            newResponse = surveyResponseRepository.save(response);
            context.plusResponseNum();
        }

        for (SurveyQuestion question : context.getQuestionList()) {
            SurveyResponseCell cell = new SurveyResponseCell(context.getSurveyId(), question, newResponse);
            setCellValue(context, question, cell, data, cells);
        }

        if (context.getSubmit()) {
            surveyResponseCellRepository.saveAll(cells);
            surveyEventTrigger.responseImport(context.getOrgId(), context.getSurveyId(), newResponse.getId(), true);
        }
    }

    private void setCellValue(ResponseImportContext context, SurveyQuestion question, SurveyResponseCell cell, Map<Integer, String> data, List<SurveyResponseCell> cells) {
        Integer iValue = null;
        String sValue = null;
        String tags = null;
        String comment = null;
        Integer cellScore = null;
        int index = context.getDataIndex();
        int finalIndex = index;
        //过滤标题中的副文本标签
        question.setTitle(RegularExpressionUtils.replaceHtml(question.getTitle()));
        switch(question.getType()) {
            case SCORE:
                if (!context.getSubmit() && data.get(finalIndex) != null && !StringUtils.isNumeric(data.get(finalIndex))) {
                    context.getErrorList().add(question.getCode() + "存在数值以外的数据");
                } else {
                    iValue = data.get(finalIndex) == null ? null : Integer.parseInt(data.get(finalIndex));
                    if (iValue != null) {
                        if ((iValue > 10 || iValue < 0))
                            context.getErrorList().add(question.getCode() + "中存在0-10以外的数值");
                        cell.setIntValue(iValue);
                        cells.add(cell);
                    }
                }
                index++;
                break;
            case SINGLE_CHOICE:
                if (StringUtils.isNotEmpty(data.get(finalIndex))) {
                    Optional<SurveyQuestionItem> itemValue = question.getItems().stream().filter(item -> RegularExpressionUtils.replaceHtml(item.getText().trim()).equals(data.get(finalIndex).trim())).findFirst();
                    if (itemValue.isPresent()) {
                        sValue = itemValue.get().getValue();
                        if (itemValue.get().getEnableTextInput() && StringUtils.isNotEmpty(data.get(finalIndex+1))) {
                            Map<String,String> commentValue = new HashMap<>();
                            commentValue.put(sValue,data.get(finalIndex+1));
                            comment = JsonHelper.toJson(commentValue);
                        }
                    } else {
                        context.getErrorList().add(question.getCode() + "中出现'" + data.get(finalIndex) + "'不符合题目中已有的选项");
                    }
                    cell.setStrValue(sValue);
                    cell.setCommentValue(comment);
                    cells.add(cell);
                }
                //如果单选开启了文本输入
                if (!question.getItems().stream().filter(item -> item.getEnableTextInput()).collect(Collectors.toList()).isEmpty()) index++;
                index++;
                break;
            case MULTIPLE_CHOICES:
                Map<String,String> textInputMap = new HashMap<>();
                sValue = data.get(finalIndex);
                index++;
                if (StringUtils.isNotEmpty(data.get(finalIndex))) {
                    for (SurveyQuestionItem item : question.getItems()) {
                        //多选题下载的答卷 第一列是合并的数据  例：选项1,选项2  数据库里面存储的是 TmnWE;TR6Bi 所以需要替换文本和","
                        sValue = sValue.replace(item.getText(), item.getValue()).replace("┋",";").replace("|",";").replace(",",";");
                        index++;
                        if (item.getEnableTextInput()) {//该选项开启了文本输入
                            //如果导入了该选项的文本输入 多选题每个选项占一列 开启了文本输入的选项也会占一列
                            if (StringUtils.isNotEmpty(data.get(index))) {
                                textInputMap.put(item.getValue(),data.get(index));
                            }
                            index++;
                        }
                    }
                    //检查所有值是否都在itemList中
                    // 将values字符串拆分为数组
                    String[] valueArray = sValue.split(";");
                    boolean allValuesPresent = Arrays.stream(valueArray).allMatch(value ->
                            question.getItems().stream().anyMatch(item -> (item.getValue().trim()).equals(value.trim())));
                    if (!allValuesPresent)
                        context.getErrorList().add(question.getCode() + "中出现'" + data.get(finalIndex) + "'不符合题目中已有的选项");
                    cell.setStrValue(sValue);
                    if (!textInputMap.isEmpty()) cell.setCommentValue(JsonHelper.toJson(textInputMap));
                    cells.add(cell);
                } else {
                    List<String> valueArray = new ArrayList<>();
                    for (SurveyQuestionItem item : question.getItems()) {
                        //多选题每个选项占一列 校验每个选项的数据
                        if (StringUtils.isNotEmpty(data.get(index)) && !RegularExpressionUtils.replaceHtml(data.get(index).trim()).equals(item.getText().trim())) {
                            context.getErrorList().add(question.getCode() + "中出现'" + data.get(index) + "'不符合题目中已有的选项");
                        } else if (StringUtils.isNotEmpty(data.get(index))) {
                            valueArray.add(item.getValue());
                        }
                        index++;
                        if (item.getEnableTextInput()) {//该选项开启了文本输入
                            //如果导入了该选项的文本输入 多选题每个选项占一列 开启了文本输入的选项也会占一列
                            if (StringUtils.isNotEmpty(data.get(index))) {
                                textInputMap.put(item.getValue(),data.get(index));
                            }
                            index++;
                        }
                    }
                    if (!valueArray.isEmpty()) cell.setStrValue(String.join(";",valueArray));
                    if (!textInputMap.isEmpty()) cell.setCommentValue(JsonHelper.toJson(textInputMap));
                    cells.add(cell);
                }
                break;
            case EVALUATION:
                if (StringUtils.isNotEmpty(data.get(finalIndex))) {
                    Optional<SurveyQuestionItem> itemValue = question.getItems().stream().filter(item -> RegularExpressionUtils.replaceHtml(item.getText().trim()).equals(data.get(finalIndex).trim())).findFirst();
                    if (itemValue.isPresent()) {
                        sValue = itemValue.get().getValue();
                    } else {
                        context.getErrorList().add(question.getCode() + "评价中出现'" + data.get(finalIndex) + "'不符合题目中已有的选项");
                    }
                }
                //标签
                index++;
                tags = data.get(index);

                //如果开启了写评价
                if (question.getHasOther()) {
                    index++;
                    comment = data.get(index);
                }
                cell.setStrValue(sValue);
                cell.setCommentValue(comment);
                cell.setTags(tags);
                if (StringUtils.isNotEmpty(sValue)) cells.add(cell);
                index++;
                break;
            case SCORE_EVALUATION:
                if (data.get(finalIndex) != null && !StringUtils.isNumeric(data.get(finalIndex))) {
                    context.getErrorList().add(question.getCode() + "中存在数值以外的数据");
                } else {
                    cellScore = data.get(finalIndex) == null ? null : Integer.parseInt(data.get(finalIndex));
                }
                index++;
                if (StringUtils.isNotEmpty(data.get(finalIndex + 1))) {
                    Optional<SurveyQuestionItem> itemValue = question.getItems().stream().filter(item -> RegularExpressionUtils.replaceHtml(item.getText().trim()).equals(data.get(finalIndex + 1).trim())).findFirst();
                    if (itemValue.isPresent()) {
                        sValue = itemValue.get().getValue();
                    } else {
                        context.getErrorList().add(question.getCode() + "评价中出现'" + data.get(index) + "'不符合题目中已有的选项");
                    }
                }
                //标签
                index++;
                tags = data.get(index);

                //如果开启了写评价
                if (question.getHasOther()) {
                    index++;
                    comment = data.get(index);
                }
                cell.setStrValue(sValue);
                cell.setCommentValue(comment);
                cell.setTags(tags);
                cell.setCellScore(cellScore);
                if (StringUtils.isNotEmpty(sValue)) cells.add(cell);
                index++;
                break;
            case NPS:
                if (!context.getSubmit() && data.get(finalIndex) != null && !StringUtils.isNumeric(data.get(finalIndex))) {
                    context.getErrorList().add(question.getCode() + "存在数值以外的数据");
                } else {
                    iValue = data.get(finalIndex) == null ? null : Integer.parseInt(data.get(finalIndex));
                    if (iValue != null) {
                        if ((iValue > 10 || iValue < 0))
                            context.getErrorList().add(question.getCode() + "中存在0-10以外的数值");
                        cell.setIntValue(iValue);
                        ClientQuestion clientQuestion = new ClientQuestion();
                        clientQuestion.setItems(question.getItems());
                        clientQuestion.setType(question.getType());
                        questionResponseHelper.castNpsSValue(cell, clientQuestion, iValue);
                    } else {
                        cell.setIntValue(null);
                    }
                }

                //标签
                if (question.getShowTags()) {
                    index++;
                    tags = data.get(index);
                    cell.setTags(tags);
                }

                //如果开启了写评价
                if (question.getHasOther()) {
                    index++;
                    comment = data.get(index);
                    cell.setCommentValue(comment);
                }
                cells.add(cell);
                index++;
                break;
            case TEXT:
                if (StringUtils.isNotEmpty(data.get(finalIndex))) {
                    cell.setStrValue(data.get(finalIndex));
                    cells.add(cell);
                }
                index++;
                break;
            case COMBOBOX:
                if (StringUtils.isNotEmpty(data.get(finalIndex))) {
                    Optional<SurveyQuestionItem> itemValue = question.getItems().stream().filter(item -> RegularExpressionUtils.replaceHtml(item.getText().trim()).equals(data.get(finalIndex).trim())).findFirst();
                    if (itemValue.isPresent()) {
                        sValue = itemValue.get().getValue();
                        cell.setStrValue(sValue);
                        cells.add(cell);
                    } else {
                        context.getErrorList().add(question.getCode() + "中出现'" + data.get(finalIndex) + "'不符合题目中已有的选项");
                    }
                }
                index++;
                break;
            default:
        }
        context.setDataIndex(index);
    }

    /**
     * 获取答卷sequence
     * @param surveyId
     * @return
     */
    private Long getSurveyResponseSequence(@NotNull long surveyId) {
        String key = String.format("survey_response_sequence:%d", surveyId);
        return stringRedisTemplate.opsForValue().increment(key, 1);
    }
}
