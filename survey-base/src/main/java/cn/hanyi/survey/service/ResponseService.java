package cn.hanyi.survey.service;


import cn.hanyi.survey.SurveyResponseActionReplyDto;
import cn.hanyi.survey.client.cache.LimitSurveyCacheHelper;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.core.constant.EntityType;
import cn.hanyi.survey.core.constant.EntityUpdateStatus;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.SurveyResponseFinishTimeOnly;
import cn.hanyi.survey.core.dto.message.EntityChangeDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.projection.SimpleResponse3;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.service.expression.ExpressionService;
import cn.hanyi.survey.core.utilis.AESUtils;
import cn.hanyi.survey.dto.RollBackResponseMessageDto;
import cn.hanyi.survey.dto.SurveyResponseDetailDto;
import cn.hanyi.survey.dto.SurveyResponseIdsDto;
import cn.hanyi.survey.dto.SurveyResponseQueryDto;
import cn.hanyi.survey.dto.open.ResponseSharedDetailDto;
import cn.hanyi.survey.dto.survey.NumberOfResponseDto;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import java.sql.PreparedStatement;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ResponseService extends CustomEmbeddedService<SurveyResponse, SurveyResponseDetailDto, SurveyResponseRepository> {

    private static final String SURVEY_RESPONSE_SEQUENCE_KEY = "survey_response_sequence:%s";//问卷答卷序号 redis key

    private static ExecutorService executorService = Executors.newFixedThreadPool(10);

    @Autowired
    private SurveyResponseRepository repository;

    @Autowired
    @Lazy
    private SurveyService surveyService;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    @Autowired
    @Lazy
    private QuotaService quotaService;

    @Autowired
    private SurveyResponseCellRepository cellRepository;

    @Autowired
    @Lazy
    private ChannelService channelService;


    @Autowired
    private ExpressionService expressionService;

    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Autowired
    private ResponseSharedService responseSharedService;

    @Autowired
    private LimitSurveyCacheHelper limitSurveyCacheHelper;
    @Autowired
    private AnalyticService analyticService;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    /**
     * 下载时 过滤答卷状态
     */
    private static List<ResponseStatus> downloadStatusList = List.of(
            ResponseStatus.FINAL_SUBMIT
    );


    /**
     * 获取答卷-不存在抛错
     *
     * @param responseId
     * @return
     */
    public SurveyResponse requireResponse(@NotNull Long responseId) {
        Optional<SurveyResponse> response = repository.findById(responseId);
        if (response.isEmpty()) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_RESPONSE_DELETE);
        }
        return response.get();
    }


    /**
     * 命中配额条件
     *
     * @param quotas
     * @param responseId
     * @return
     */
    public List<SurveyQuota> quotaTriggerResponse(List<SurveyQuota> quotas, Long responseId) {
        List<SurveyQuota> quotaList = new ArrayList<>();

        quotas.forEach(quota -> {
            Boolean triggerResult = expressionService.triggerExpression(responseId, quota.getExpression(), null, true);
            log.debug("quota: {} triggerResult: {}", quota.getId(), triggerResult);
            if (triggerResult) {
                quotaList.add(quota);
            }
        });

        return quotaList;
    }

    /**
     * 命中配额条件
     *
     * @param questions
     * @param quotas
     * @param response
     * @return
     */
    public List<SurveyQuota> quotaTriggerResponse(List<SurveyQuestion> questions, List<SurveyQuota> quotas, SurveyResponse response) {
        List<SurveyQuota> quotaList = new ArrayList<>();

        quotas.forEach(quota -> {
            Boolean triggerResult = expressionService.triggerExpressionNew(questions, response, quota.getExpression(), null, false);
            log.debug("quota: {} triggerResult: {}", quota.getId(), triggerResult);
            if (triggerResult) {
                quotaList.add(quota);
            }
        });

        return quotaList;
    }

    /**
     * 异步删除问卷某个渠道答题数据
     *
     * @param surveyId
     */
    @Transactional
    public List<Long> deleteSurveyResponse(@NotNull long surveyId, @NotNull long channelId) {
        List<SurveyResponse> surveyResponseList = repository.findAllBySurveyIdAndChannelId(surveyId, channelId);
        if (surveyResponseList.isEmpty()) return List.of();

        List<Long> responseIds = surveyResponseList.stream().map(q -> q.getId()).collect(Collectors.toList());

        //删除SurveyResponse的数据
        repository.deleteAllByIdIn(responseIds);
        //删除SurveyResponseCells的数据
        cellRepository.deleteAllBySurveyIdAndResponseIdIn(surveyId, responseIds);

        surveyEventTrigger.responseDeleteByChannel(surveyId, channelId);
        return responseIds;
    }


    @Override
    public void afterMapToDto(List<SurveyResponse> entity, List<SurveyResponseDetailDto> dto) {
        analyticService.checkVersionLimit();
        List<Long> channelIds = new ArrayList<>();
        Optional.ofNullable(dto).ifPresent(i -> i.forEach(j -> {
            Optional.ofNullable(j.getChannelId()).ifPresent(channelIds::add);
        }));
        if (!channelIds.isEmpty()) {
            Map<Long, SurveyChannelDto> channelMap = channelService.getGroupMapByIds(channelIds, SurveyChannel::getId);
            if (MapUtils.isNotEmpty(channelMap)) {
                dto.forEach(j -> {
                    Optional.ofNullable(channelMap.get(j.getChannelId())).ifPresent(c -> {
                        j.setChannelName(c.getName());
                    });
                });
            }
        }
    }

    @Override
    public void afterMapToDto(SurveyResponse entity, SurveyResponseDetailDto dto) {
        analyticService.checkVersionLimit();
        Map<String, Object> cellData = buildSubmitCellData(entity.getSurveyId(), entity.getId());
        dto.setCellData(cellData);
    }

    /**
     * 批量删除问卷
     *
     * @param responseIdsDto
     * @return
     */
    @Transactional
    public ResourceResponseDto deleteBatch(Long surveyId, SurveyResponseIdsDto responseIdsDto) {
        Survey survey = surveyService.requireSurvey(surveyId);
        responseIdsDto.appendIds(getSearchResponseIds(surveyId, responseIdsDto.getQueryDto()));
        List<SurveyResponse> surveyResponses = this.getByIds(responseIdsDto.getIds());
        //删除SurveyResponse的数据
        repository.deleteAllByIdIn(responseIdsDto.getIds());
        //删除SurveyResponseCells的数据
        cellRepository.deleteAllBySurveyIdAndResponseIdIn(survey.getId(), responseIdsDto.getIds());
        //回退配额进度
        if (!survey.getQuotas().isEmpty())
            quotaService.rollBackQuotaBatch2(survey.getId(), surveyResponses);

        //只处理有效答卷
        long count = surveyResponses.stream().filter(x -> x.getStatus().equals(ResponseStatus.FINAL_SUBMIT)).count();
        if (count > 0) {
            this.updateResponseNum(survey, (int) -count);
        }

        responseIdsDto.getIds().forEach(id -> {
            EntityChangeDto entityChangeDto = new EntityChangeDto();
            entityChangeDto.setEntityId(id);
            entityChangeDto.setSurveyId(survey.getId());
            entityChangeDto.setEntityType(EntityType.RESPONSE);
            entityChangeDto.setStatus(EntityUpdateStatus.DELETED);
//            messageService.notifySurveyChange(entityChangeDto);
            surveyEventTrigger.responseDelete(surveyId, id);
        });
        return new ResourceResponseDto();
    }


    /**
     * 批量修改答卷状态
     *
     * @param responseIdsDto
     * @return
     */
    @Transactional
    public ResourceResponseDto updateBatch(Long surveyId, SurveyResponseIdsDto responseIdsDto) {
        Survey survey = surveyService.requireSurvey(surveyId);
        responseIdsDto.appendIds(getSearchResponseIds(surveyId, responseIdsDto.getQueryDto()));
        EntityUpdateStatus entityUpdateStatus;

        switch (responseIdsDto.getStatus()) {
            case FINAL_SUBMIT:
                entityUpdateStatus = EntityUpdateStatus.CREATED;
                this.updateResponseNum(survey, responseIdsDto.getIds().size());
                break;
            case INVALID:
                entityUpdateStatus = EntityUpdateStatus.INVALID;
                this.updateResponseNum(survey, -responseIdsDto.getIds().size());
                break;
            default:
                throw new BadRequestException("答卷填答状态无效");
        }
        repository.updateStatusByIdIn(responseIdsDto.getStatus(), responseIdsDto.getIds());

        //答卷作废 回退配额进度
        if (responseIdsDto.getStatus() == ResponseStatus.INVALID && !survey.getQuotas().isEmpty()) {
            responseIdsDto.getIds().forEach(id -> {
                quotaService.rollBackQuota(surveyId, id);
            });
        }

        //答卷还原 重新统计配额
        if (responseIdsDto.getStatus() == ResponseStatus.FINAL_SUBMIT && !survey.getQuotas().isEmpty()) {
            responseIdsDto.getIds().forEach(id -> {
                quotaService.useQuotaByOldResponse(survey, id);
            });
        }

        responseIdsDto.getIds().forEach(id -> {
            EntityChangeDto entityChangeDto = new EntityChangeDto();
            entityChangeDto.setEntityId(id);
            entityChangeDto.setSurveyId(survey.getId());
            entityChangeDto.setEntityType(EntityType.RESPONSE);
            entityChangeDto.setStatus(entityUpdateStatus);
//            messageService.notifySurveyChange(entityChangeDto);
            if (responseIdsDto.getStatus() == ResponseStatus.FINAL_SUBMIT) {
                surveyEventTrigger.responseRecover(surveyId, id);
            } else {
                surveyEventTrigger.responseInvalid(surveyId, id);
            }
        });

        return new ResourceResponseDto();
    }

    private void updateResponseNum(Survey survey, int add) {
        if (survey.getResponseFinishNum() == null) {
            long count = repository.countBySurveyIdAndStatus(survey.getId(), ResponseStatus.FINAL_SUBMIT);
            survey.setResponseFinishNum((int) count);
            surveyService.save(survey);
        }
        surveyService.updateResponseNum(survey.getId(), add);
    }


    /**
     * 根据问卷和答题状态获取答卷数据
     *
     * @param survey
     * @param status
     * @return
     */
    public Long numOfResponse(Survey survey, ResponseStatus status) {
        return repository.countBySurveyIdAndStatus(survey.getId(), status);
    }

    /**
     * 统计渠道答卷回收数据
     *
     * @param surveyId
     * @param channelId
     * @return
     */
    public Long getNumResponseByChannel(Long surveyId, Long channelId) {
        return repository.countBySurveyIdAndChannelIdAndStatus(surveyId, channelId, ResponseStatus.FINAL_SUBMIT);
    }

    /**
     * 获取指定client_id的答题数据
     *
     * @return
     */
    public Optional<SurveyResponse> clientIdData(Survey survey, String clientId) {
        return repository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(survey.getId(), clientId);
    }

    /**
     * 获取问卷下的所有答题数据
     *
     * @param survey
     * @return
     */
    public Optional<List<SurveyResponse>> getBySurvey(Survey survey) {
        return repository.findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndStatusInOrderByCreateTimeAsc(survey.getId(), downloadStatusList);
    }

    /**
     * 获取问卷下的所有答题数据
     *
     * @param survey
     * @return
     */
    public Optional<List<SurveyResponse>> getBySurvey(Survey survey, Pageable pageable) {
        return repository.findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndStatusInOrderByCreateTimeAsc(survey.getId(), downloadStatusList, pageable);
    }

    /**
     * 获取问卷下的所有答题数量
     *
     * @param survey
     * @return
     */
    public int countResponseBySurvey(Survey survey) {
        return repository.countDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndStatusInOrderByCreateTimeAsc(survey.getId(), downloadStatusList);
    }


    /**
     * 批量获取答卷下的答题数据
     *
     * @param survey
     * @param surveyResponseList
     * @return
     */
    public List<SurveyResponseCell> getCells(Survey survey, List<SurveyResponse> surveyResponseList) {
        List<Long> ids = surveyResponseList.stream().map(BaseEntity::getId).collect(Collectors.toList());
        return cellRepository.findAllBySurveyIdAndResponseIdIn(survey.getId(), ids);
    }

    /**
     * 清除答卷
     *
     * @param survey
     */
    @Transactional
    public void cleanResponse(Survey survey) {
//        cellRepository.deleteAllBySurveyId(survey.getId());
        repository.deleteBySurveyId(survey.getId());
        survey.setResponseFinishNum(0);
        surveyService.save(survey);
        //清空渠道数据
        List<SurveyChannel> channels = channelService.getChannelList(survey.getId());
        channels.forEach(c -> channelService.wipeData(survey.getId(), c.getId()));
        surveyEventTrigger.responseDeleteBySurvey(survey.getId());
    }

    /**
     * 构建答题时提交的数据
     *
     * @return
     */
    public Map<String, Object> buildSubmitCellData(Long surveyId, Long responseId) {
        LinkedHashMap<String, Object> cellData = new LinkedHashMap<>();
        Map<Long, SurveyQuestion> questionIdMap = surveyService.getSurvey(surveyId).getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        if (questionIdMap == null) return cellData;
        cellRepository.findAllBySurveyIdAndResponseId(surveyId, responseId).forEach(
                surveyResponseCell -> {
                    SurveyQuestion question = questionIdMap.get(surveyResponseCell.getQuestionId());
                    if (question == null) return;
                    String name = question.getName();
                    Object value = surveyResponseCell.getValue();
                    Object otherValue = surveyResponseCell.getCommentValue();
                    String tags = surveyResponseCell.getTags();
                    cellData.put(name, value);
                    if (otherValue != null) {
                        cellData.put(String.format("%s-Comment", name), otherValue);
                    }
                    if (tags != null) {
                        cellData.put(String.format("%s-Tags", name), Arrays.asList(tags.split(",")));
                    }
                }
        );
        return cellData;
    }

    /**
     * 构建答题时获得的分数
     *
     * @return
     */
    public Map<String, Integer> buildSubmitCellScore(Long surveyId, Long responseId) {
        LinkedHashMap<String, Integer> cellData = new LinkedHashMap<>();
        Map<Long, SurveyQuestion> questionIdMap = surveyService.getSurvey(surveyId).getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        cellRepository.findAllBySurveyIdAndResponseIdAndCellScoreIsNotNull(surveyId, responseId).forEach(
                surveyResponseCell -> {
                    SurveyQuestion question = questionIdMap.get(surveyResponseCell.getQuestionId());
                    if (question == null) return;
                    String name = question.getName();
                    Integer value = surveyResponseCell.getCellScore();
                    cellData.put(name, value);
                }
        );
        return cellData;
    }


    /**
     * 根据答卷id获取答卷
     *
     * @param ids
     * @return
     */
    public List<SurveyResponse> getByIds(List<Long> ids) {
        return repository.findAllByIdIn(ids);
    }

    /**
     * 统计某一部门下的答题数
     *
     * @param survey
     * @param departmentId
     * @return
     */
    public Long countByDepartmentId(Survey survey, Long departmentId) {
        return repository.countBySurveyIdAndDepartmentIdAndStatus(survey.getId(), departmentId, ResponseStatus.FINAL_SUBMIT);
    }

    /**
     * 使用apiKey加密
     *
     * @return
     */
    public String encryptParams(String apiKey, String content) {
        try {
            return AESUtils.encrypt(apiKey, content);
        } catch (Exception e) {
            log.error("apiKey:{} 加密失败: {} with: {}", apiKey, content, e.getMessage());
        }
        return content;
    }

    /**
     * 使用apiKey解密
     *
     * @return
     */
    public String decryptParams(String apiKey, String content) {
        try {
            return AESUtils.decrypt(apiKey, content);
        } catch (Exception e) {
            log.error("apiKey:{} 解密失败: {} with: {}", apiKey, content, e.getMessage());
        }
        return content;
    }

    /**
     * 根据答卷状态获取答卷
     *
     * @param survey
     * @param status
     * @param pageable
     * @return
     */
    public List<SurveyResponse> getByStatus(Survey survey, ResponseStatus status, Pageable pageable) {
        return repository.findBySurveyIdAndStatus(survey.getId(), status, pageable);
    }

    /**
     * 获取指定状态的最近答题时间
     *
     * @param survey
     * @param status
     * @return
     */
    public SurveyResponseFinishTimeOnly LatestResponseTime(Survey survey, ResponseStatus status) {
        return repository.findTopBySurveyIdAndStatusOrderByFinishTimeDesc(survey.getId(), status);
    }


    /**
     * 根据答卷responseId获取详情
     *
     * @param surveyId
     * @param responseId
     * @return
     */
    public SurveyResponseDetailDto getSurveyResponseDetail(Long surveyId, Long responseId) {
        Survey survey = surveyService.requireSurvey(surveyId);
        SurveyResponse response = requireResponse(responseId);
        Map<String, Object> cellData = buildSubmitCellData(surveyId, responseId);
        //获取得分情况
        Map<String, Integer> cellScore = buildSubmitCellScore(surveyId, responseId);

        ResponseSharedDetailDto responseSharedDetail = responseSharedService.getResponseSharedDetail(surveyId, responseId, true);

        SurveyResponseDetailDto surveyResponseDetailDto = mapToDto(response);
        surveyResponseDetailDto.setCellData(cellData);
        surveyResponseDetailDto.setCellScore(cellScore);
        surveyResponseDetailDto.setSharedToken(responseSharedDetail);


        //如果有渠道号 返回渠道信息
        if (surveyResponseDetailDto.getChannelId() != null && surveyResponseDetailDto.getChannelId() > 0) {
            SurveyChannel surveyChannel = channelService.requireChannel(surveyResponseDetailDto.getChannelId());
            surveyResponseDetailDto.setChannelName(surveyChannel.getName());
        }

        surveyResponseDetailDto.setSurvey(surveyService.mapToDto(survey));
        return surveyResponseDetailDto;
    }


    public Map<Long, Integer> getNumResponse(List<Survey> surveyList) {
        this.calcResponse(surveyList);
        Set<Long> surveyIds = surveyList.stream().map(x -> x.getId()).collect(Collectors.toSet());
        List<Map<String, Object>> numberOfResponseDtoList = repository.numRespSurveyIdsAndStatus(surveyIds);

        return numberOfResponseDtoList.stream().map(
                        m -> new NumberOfResponseDto(
                                Long.valueOf(String.valueOf(m.get("surveyId"))),
                                Integer.valueOf(String.valueOf(m.get("total")))))
                .collect(Collectors.toMap(NumberOfResponseDto::getSurveyId, NumberOfResponseDto::getTotal));
    }


    public void calcResponse(List<Survey> surveyList) {

        String lockKey = String.format("survey:response:sync:%s:%s", TenantContext.getCurrentTenant(), DateHelper.formatDate(new Date())); //org_id,date
        String key = stringRedisTemplate.opsForValue().get(lockKey);
        if (StringUtils.isEmpty(key)) {
            this.asyncResponseFinishNum();
            //设置key
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime midnight = now.toLocalDate().plusDays(1).atStartOfDay();
            long secondsUntilMidnight = ChronoUnit.SECONDS.between(now, midnight);
            stringRedisTemplate.opsForValue().set(lockKey, "1", secondsUntilMidnight, TimeUnit.SECONDS);
        }
        List<Long> surveyIds = surveyList.stream().filter(x -> x.getResponseFinishNum() == null).map(x -> x.getId()).collect(Collectors.toList());
        //存在null才去同步
        if (surveyIds.size() > 0) {
            String ids = surveyIds.stream().map(Objects::toString).collect(Collectors.joining(","));
            String allSurvey = String.format("select id surveyId, response_finish_num total from survey where org_id = %s and id in (%s)", TenantContext.getCurrentTenant(), ids);
            List<Map<String, Object>> numberOfResponseDtoList = jdbcTemplate.queryForList(allSurvey);

            List<Map<String, Object>> res = numberOfResponseDtoList.stream().filter(num -> num.get("total") == null).collect(Collectors.toList());
            this.syncResponseFinishNum(res);
        }
    }

    /**
     * 同步null的数据
     * {
     * key:sid
     * value:response_finish_num
     * }
     *
     * @param numberOfResponseDtoList
     */
    private void syncResponseFinishNum(List<Map<String, Object>> numberOfResponseDtoList) {
        CountDownLatch latch = new CountDownLatch(numberOfResponseDtoList.size());
        for (int i = 0; i < numberOfResponseDtoList.size(); i++) {
            Map<String, Object> map = numberOfResponseDtoList.get(i);
            executorService.execute(() -> {
                try {
                    Long sid = Long.parseLong(String.valueOf(map.get("surveyId")));
                    updateResponseFinishNum(sid);
                } finally {
                    latch.countDown();
                }
            });
        }
        try {
            latch.await(60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

    }

    /**
     * 重新异步更新问卷的完成人数
     */
    public void asyncResponseFinishNum() {
        Long orgId = TenantContext.getCurrentTenant();
        new Thread(() -> {
            String allSurvey = "select id surveyId, response_finish_num total from  survey  where org_id=" + orgId;
            List<Map<String, Object>> numberOfResponseDtoList = jdbcTemplate.queryForList(allSurvey);
            for (int i = 0; i < numberOfResponseDtoList.size(); i++) {
                Map<String, Object> map = numberOfResponseDtoList.get(i);
                executorService.execute(() -> {
                    Long sid = Long.parseLong(String.valueOf(map.get("surveyId")));
                    updateResponseFinishNum(sid);
                });
            }
        }).start();

    }

    /**
     * 修改问卷答卷数量 responseFinishNum
     *
     * @param sid
     */
    public void updateResponseFinishNum(Long sid) {
        long responseNum = repository.countBySurveyIdAndStatusForceIndex(sid, ResponseStatus.FINAL_SUBMIT.ordinal());
        String sql = String.format("update survey set response_finish_num = %s where id = %s", responseNum, sid);
        jdbcTemplate.update(sql);
    }

    /**
     * 根据clientId获取答卷状态
     *
     * @param survey
     * @param clientId
     * @return
     */
    public Boolean getResponseStatusByClientId(Survey survey, Object clientId) {
        if (clientId == null) return false;

        Optional<SurveyResponse> surveyResponseOptional = clientIdData(survey, clientId.toString());

        if (surveyResponseOptional.isPresent()) {

            SurveyResponse surveyResponse = surveyResponseOptional.get();

            if (surveyResponse.getIsCompleted() || surveyResponse.getStatus() != ResponseStatus.INIT) {
                return true;
            }
        }
        return false;
    }

    /**
     * * 查看答卷状态 false可以弹出问卷  true表示问卷免打扰
     *
     * @param survey
     * @param channel
     * @param isComplete
     * @param ip
     * @return
     */
    public Boolean checkResponseStatus(Survey survey, SurveyChannel channel, Boolean isComplete, String ip) {

        //设备限制
        if (isComplete) return true;

        if (survey.getStatus() != SurveyStatus.COLLECTING || channel.getStatus() != ChannelStatus.RECOVERY)
            return true;
        //ip限制
        if (survey.getEnableIpLimit()) {
            LocalDateTime lastTime = limitSurveyCacheHelper.lastTimeByIp(survey.getId(), ip);
            if (lastTime == null) {
                return false;
            }
            if (lastTime.isAfter(survey.getIpLimit().limitTime())) {
                return true;
            }
        }

        //回收数量限制
        if (survey.getEnableResponseLimit()) {
            Long count = numOfResponse(survey, ResponseStatus.FINAL_SUBMIT);
            if (count >= survey.getResponseAmount()) return true;
        }

        //调查期限内
        if (survey.getEnableDateLimit()) {
            try {
                IBeforeLimiter.checkDateRange(survey.getStartTime(), survey.getEndTime());
            } catch (Exception e) {
                return true;
            }
        }
        return false;
    }

    /**
     * 消费kafka消息 回退审核失败的答卷配额
     *
     * @param messageDto
     * @return
     */
    public Boolean rollBackSurveyResponseQuota(RollBackResponseMessageDto messageDto) {
        if (messageDto.getSurveyId() == null || messageDto.getResponseId() == null) return false;
        Survey survey = surveyService.requireSurvey(messageDto.getSurveyId());

        //回退配额进度
        if (surveyService.checkSurveyQuota(survey))
            quotaService.rollBackQuota(survey.getId(), messageDto.getResponseId());
        return true;
    }

    public SurveyResponseActionReplyDto getActionReply(long surveyId, long responseId) {
        String sql = "SELECT content,ea.id " +
                "FROM event_action ea " +
                "LEFT JOIN event_result er ON ea.event_id = er.id " +
                "WHERE er.response_id = ? AND ea.survey_id = ? AND ea.action_type = ? AND ea.action_status = 1 "+
                "ORDER BY ea.create_time desc LIMIT 1";

        List<SurveyResponseActionReplyDto> replys = jdbcTemplate.query(
                sql,
                (rs, rowNum) -> {
                    SurveyResponseActionReplyDto dto = new SurveyResponseActionReplyDto();
                    dto.setContent(rs.getString("content"));
                    dto.setActionId(rs.getLong("id"));
                    return dto;
                },
                responseId, surveyId, 12
        );
        if (replys.isEmpty()) {
            return null;
        }
        SurveyResponseActionReplyDto reply = replys.get(0);
        String updateSql = "update event_action set action_status = 3 where id = ?";
        jdbcTemplate.update((con -> {
            PreparedStatement statement = con.prepareStatement(updateSql);
            statement.setLong(1, reply.getActionId());
            return statement;
        }));
        return reply;
    }

    /**
     * 根据条件筛选 需要批量处理的responseId
     *
     * @param surveyId
     * @param queryDto
     * @return
     */
    public List<Long> getSearchResponseIds(Long surveyId, SurveyResponseQueryDto queryDto) {
        if (surveyId == null || queryDto == null) return null;
        ResourceEntityQueryDto<SurveyResponseDetailDto> params = queryDto.transform();
        if (queryDto.getCreateTime_gt() != null)
            params.addCriteria(new ResourceQueryCriteria("createTime", queryDto.getCreateTime_gt(), QueryOperator.GREATER_THAN));
        if (queryDto.getCreateTime_lt() != null)
            params.addCriteria(new ResourceQueryCriteria("createTime", queryDto.getCreateTime_lt(), QueryOperator.LESS_THAN));
        if (queryDto.getFinishTime_gt() != null)
            params.addCriteria(new ResourceQueryCriteria("finishTime", queryDto.getFinishTime_gt(), QueryOperator.GREATER_THAN));
        if (queryDto.getFinishTime_lt() != null)
            params.addCriteria(new ResourceQueryCriteria("finishTime", queryDto.getFinishTime_lt(), QueryOperator.LESS_THAN));
        if (queryDto.getSequence() != null)
            params.addCriteria(new ResourceQueryCriteria("sequence", queryDto.getSequence(), QueryOperator.EQUAL));
        if (!StringUtils.isEmpty(queryDto.getStatus_in())) {
            String[] statusList = queryDto.getStatus_in().split(",");
            params.addCriteria(new ResourceQueryCriteria("status", Arrays.stream(statusList).map(x -> ResponseStatus.valueOf(x)).collect(Collectors.toList()), QueryOperator.IN));
        }
        if (queryDto.getCollectorMethod() != null)
            params.addCriteria(new ResourceQueryCriteria("collectorMethod", queryDto.getCollectorMethod(), QueryOperator.EQUAL));

        List<Long> responseIds = new ArrayList<>();
        while (true) {
            Page<SurveyResponseDetailDto> page = super.findAllEmbeddedMany(surveyId, "surveyId", params);
            page.getContent().forEach(i -> responseIds.add(i.getId()));
            if (!page.hasNext()) {
                break;
            }
            params.setPage(params.getPage() + 1);
        }
        return responseIds;
    }

    public boolean existResponse(long surveyId) {
        return repository.existsBySurveyIdAndStatusInAndIsCompletedIsTrue(surveyId, List.of(ResponseStatus.FINAL_SUBMIT, ResponseStatus.INVALID));
    }

    /**
     * 外部客户数据
     * @param surveyId
     * @param channelId
     * @param externalUserIds
     * @return
     */
    public Map<String,SimpleResponse3> getResponseByExternalUserId(Long surveyId, Long channelId, List<String> externalUserIds) {
        Map<String, SimpleResponse3> responseMap = new HashMap<>();
        List<SimpleResponse3> responses = repository.findBySurveyIdAndChannelIdAndExternalUserIdIn(surveyId,channelId,externalUserIds);
        Optional.ofNullable(responses).ifPresent(responseList ->{
            responseList.forEach(response -> {
                responseMap.put(response.getExternalUserId(),response);
            });
        });
        return responseMap;
    }
}