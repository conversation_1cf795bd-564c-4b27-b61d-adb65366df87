package cn.hanyi.survey.service.download;

import cn.hanyi.survey.client.service.TrackingService;
import cn.hanyi.survey.core.dto.SurveyTrackingDataDto;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.download.dto.ResponseImportApiDto;
import cn.hanyi.survey.service.download.dto.ResponseImportApiListDto;
import cn.hanyi.survey.service.download.dto.ResponseImportContext;
import cn.hanyi.survey.service.download.dto.ResponseImportDataDto;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import cn.hanyi.survey.workertrigger.ISurveyTaskTrigger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Slf4j
@Component
public class ResponseImportApiParser {

    @Autowired
    private ResponseDownloadHelper responseDownloadHelper;

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SurveyResponseCellRepository surveyResponseCellRepository;

    @Autowired
    private ISurveyTaskTrigger surveyTaskTrigger;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private SurveyService surveyService;

    @Autowired
    protected ISurveyEventTrigger surveyEventTrigger;

    @Transactional
    public Boolean importResponseApi(Long surveyId, ResponseImportApiListDto list) {

        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();

        try {
            if (list.getList().isEmpty() || list.getList().size() > 1000)
                throw new BadRequestException("api导入的答卷数量超过限制,限制小于1000条");

            ResponseImportContext context = new ResponseImportContext(surveyId, orgId, userId);
            List<SurveyQuestion> questionList = responseDownloadHelper.buildTemplateSurveyQuestion(context);
            if (context.getSurvey().getEnableResponseImport() == null || !context.getSurvey().getEnableResponseImport())
                throw new BadRequestException("答卷导入功能未开启");
            //加载问卷题目
            context.setQuestionList(questionList);
            context.setCount(list.getList().size());
            int num = 0;
            for (ResponseImportApiDto dto : list.getList()) {
                validData(dto);
                //解析api提交的答卷
                if (parseField(context, dto)) {
                    num++;
                }
            }
            //处理完所有数据
            surveyService.updateResponseNum(surveyId, num);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BadRequestException(e.getMessage());
        }
        return true;
    }

    public void validData(ResponseImportApiDto dto) {
        if (dto.getStartTime() == null || dto.getFinishTime() == null) {
            throw new BadRequestException("开始时间和提交时间为必填字段");
        }
        if (dto.getData().isEmpty()) {
            throw new BadRequestException("传入的答卷数据字段不能为空");
        }

        //如果答题时长为null或者0 用提交时间 - 开始时间计算
        if (dto.getDuration() == null || dto.getDuration() ==0) {
            int durationSeconds = Math.toIntExact((dto.getFinishTime().getTime() - dto.getStartTime().getTime()) / 1000);
            if (durationSeconds < 0) durationSeconds = 0;
            dto.setDuration(durationSeconds);
        }
    }


    public boolean parseField(ResponseImportContext context, ResponseImportApiDto dto) {

        if (context.getQuestionList().isEmpty()) return false;

        //构建答卷基础信息
        SurveyResponse response = new SurveyResponse(context.getSurveyId(), context.getOrgId(), dto.getStartTime(), dto.getFinishTime(), dto.getIp(), dto.getDuration(), dto.getExternalUserId(), dto.getDepartmentCode(), dto.getParameters());
        //解析ip
        if (StringUtils.isNotEmpty(dto.getIp())) {
            SurveyTrackingDataDto trackingDataDto = trackingService.parseIpAddress(dto.getIp());
            response.setCountry(trackingDataDto.getCountry());
            response.setProvince(trackingDataDto.getProvince());
            response.setCity(trackingDataDto.getCity());
        }

        //答卷编号
        response.setSequence(getSurveyResponseSequence(context.getSurveyId()));
        SurveyResponse newResponse = surveyResponseRepository.save(response);

        List<SurveyResponseCell> cells = new ArrayList<>();
        for (ResponseImportDataDto data : dto.getData()) {
            for (SurveyQuestion question : context.getQuestionList()) {
                //如果code不相等 继续下一道题
                if (!data.getCode().equals(question.getCode())) continue;
                SurveyResponseCell cell = new SurveyResponseCell(context.getSurveyId(), question, newResponse);
                setCellValue(question, cell, data, cells);
            }
        }
        surveyResponseCellRepository.saveAll(cells);
        surveyEventTrigger.responseImport(context.getOrgId(), context.getSurveyId(), newResponse.getId(), true);
        return true;
    }

    private void setCellValue(SurveyQuestion question, SurveyResponseCell cell, ResponseImportDataDto data, List<SurveyResponseCell> cells) {
        Integer iValue = null;
        String sValue = null;
        String comment = null;
        switch(question.getType()) {
            case SCORE:
            case NPS:
                if (data.getValue() != null && !StringUtils.isNumeric(data.getValue())) {
                    throw new BadRequestException(question.getCode() + "中存在非数值数据");
                } else {
                    iValue = data.getValue() == null ? null : Integer.parseInt(data.getValue());
                    if (iValue > 10 || iValue < 0)
                        throw new BadRequestException(question.getCode() + "中存在数值大于10或者小于0的数据");
                    cell.setIntValue(iValue);
                    cell.setCommentValue(data.getComment());
                    cell.setTags(data.getTags());
                    cells.add(cell);
                }
                break;
            case SINGLE_CHOICE:
            case COMBOBOX:
                if (StringUtils.isNotEmpty(data.getValue())) {
                    Optional<SurveyQuestionItem> itemValue = question.getItems().stream().filter(item -> item.getText().equals(data.getValue())).findFirst();
                    if (itemValue.isPresent()) {
                        sValue = itemValue.get().getValue();
                        if (itemValue.get().getEnableTextInput() && StringUtils.isNotEmpty(data.getComment())) {
                            comment = data.getComment().replace(itemValue.get().getText(), itemValue.get().getValue());
                        }
                    } else {
                        throw new BadRequestException(question.getCode() + "中出现与题目选项不符的数据");
                    }
                    cell.setStrValue(sValue);
                    cell.setCommentValue(comment);
                    cells.add(cell);
                }

                break;
            case MULTIPLE_CHOICES:
                sValue = data.getValue();
                if (StringUtils.isNotEmpty(sValue)) {
                    for (SurveyQuestionItem item : question.getItems()) {
                        sValue = sValue.replace(item.getText(), item.getValue()).replace(",",";");
                        if (item.getEnableTextInput() && StringUtils.isNotEmpty(data.getComment())) {
                            comment = data.getComment().replace(item.getText(), item.getValue());
                        }
                    }

                    // 将values字符串拆分为数组
                    String[] valueArray = sValue.split(";");
                    //检查所有值是否都在itemList中
                    boolean allValuesPresent = Arrays.stream(valueArray).allMatch(value ->
                            question.getItems().stream().anyMatch(item -> item.getValue().equals(value)));
                    if (!allValuesPresent)
                        throw new BadRequestException(question.getCode() + "中出现与题目选项不符的数据");

                    cell.setStrValue(sValue);
                    cell.setCommentValue(comment);
                    cells.add(cell);
                }

                break;
            case EVALUATION:
            case SCORE_EVALUATION:
                if (StringUtils.isNotEmpty(data.getValue())) {
                    Optional<SurveyQuestionItem> itemValue = question.getItems().stream().filter(item -> item.getText().equals(data.getValue())).findFirst();
                    if (itemValue.isPresent()) {
                        sValue = itemValue.get().getValue();
                    } else {
                        throw new BadRequestException(question.getCode() + "评价中出现与题目选项不符的数据");
                    }
                    cell.setStrValue(sValue);
                    cell.setCommentValue(data.getComment());
                    cell.setTags(data.getTags());
                    cells.add(cell);
                }
                break;
            case TEXT:
                if (StringUtils.isNotEmpty(data.getValue())) {
                    cell.setStrValue(data.getValue());
                    cells.add(cell);
                }
                break;
            default:
        }
    }

    /**
     * 获取答卷sequence
     * @param surveyId
     * @return
     */
    private Long getSurveyResponseSequence(@NotNull long surveyId) {
        String key = String.format("survey_response_sequence:%d", surveyId);
        return stringRedisTemplate.opsForValue().increment(key, 1);
    }

}
