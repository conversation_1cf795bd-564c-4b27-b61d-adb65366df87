package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.dto.lottery.*;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinner;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinnerDto;
import cn.hanyi.survey.core.repository.SurveyLotteryPrizeWinnerRepository;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/10/14 16:56:59
 */
@Service
@Slf4j
public class SurveyLotteryWinnerStatisticsService extends BaseService<SurveyLotteryPrizeWinner, SurveyLotteryPrizeWinnerDto, SurveyLotteryPrizeWinnerRepository> {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 奖励核销统计
     *
     * @param surveyId
     * @param page
     * @param limit
     * @param data
     * @return
     */
    public SurveyPrizeWinnerResult prizeWinnerStatistics(Long surveyId, int page, int limit, String sort, SurveyPrizeWinnerParam data) {
        SurveyPrizeWinnerResult result = new SurveyPrizeWinnerResult();

        Long count = this.statistics(data, surveyId, result);

        String listLimitSql = prizeWinnerStatisticsSqlLimit(data, surveyId, page, limit, sort);
        List<SurveyLotteryStatisticsDto> resultList = jdbcTemplate.query(listLimitSql, new BeanPropertyRowMapper<>(SurveyLotteryStatisticsDto.class));
        result.setPrizeWinnerList(resultList);

        MetaDto meta = new MetaDto();
        meta.setPage(page);
        meta.setLimit(limit);
        meta.setTotal(count);
        result.setMeta(meta);

        return result;
    }

    /**
     * 统计奖励核销
     *
     * @param data
     * @param surveyId
     * @param result   总数量：分页使用
     * @return
     */
    public Long statistics(SurveyPrizeWinnerParam data, Long surveyId, SurveyPrizeWinnerResult result) {
        String totalPrizeSql = prizeWinnerStatisticsCountSql(data, surveyId);
        Long totalPrize = jdbcTemplate.queryForObject(totalPrizeSql, Long.class);
        result.setWinnerNum(totalPrize);

        String isSentSql = prizeWinnerStatisticsCountSql(data, surveyId);
        isSentSql += " and t.status = 1";
        Long isSent = jdbcTemplate.queryForObject(isSentSql, Long.class);
        result.setSendPrizeNum(isSent);

        String notSendSql = prizeWinnerStatisticsCountSql(data, surveyId);
        notSendSql += " and t.status = 0";
        Long notSend = jdbcTemplate.queryForObject(notSendSql, Long.class);
        result.setNotSendPrizeNum(notSend);

        String todaySendPrizeSql = prizeWinnerStatisticsCountSql(data, surveyId);
        todaySendPrizeSql += " and t.status = 1 and t.send_time > CURRENT_DATE";
        Long todaySendPrize = jdbcTemplate.queryForObject(todaySendPrizeSql, Long.class);
        result.setTodaySendPrizeNum(todaySendPrize);

        String todayNotSendPrizeSql = prizeWinnerStatisticsCountSql(data, surveyId);
        todayNotSendPrizeSql += " and t.status = 0 and t.create_time > CURRENT_DATE";
        Long todayNotSendPrize = jdbcTemplate.queryForObject(todayNotSendPrizeSql, Long.class);
        result.setTodayNotSendPrizeNum(todayNotSendPrize);
        return totalPrize;
    }

    public String prizeWinnerStatisticsCountSql(SurveyPrizeWinnerParam data, Long surveyId) {
        String baseSql = "select count(1) from survey_lottery_prize_winner t where survey_id = $surveyId  $condition";
        String $condition = queryCondition(data);
        baseSql = baseSql.replace("$surveyId", surveyId.toString()).replace("$condition", $condition);
        log.debug(baseSql);
        return baseSql;
    }

    /**
     * 结果列表
     *
     * @param data
     * @param surveyId
     * @param page
     * @param limit
     * @return
     */
    public String prizeWinnerStatisticsSqlLimit(SurveyPrizeWinnerParam data, Long surveyId, int page, int limit, String sort) {
        String baseSql = "select $column from survey_lottery_prize_winner t where survey_id = $surveyId  $condition $sort $limit";
        String $column = "id,response_id responseId,type,prize_name prizeName,status,prize_code prizeCode,name,phone,province,city,district,address, $childQuery1,create_time as createTime,$childQuery2,send_time as sendTime,amount";
		String $childQuery1 = "( SELECT lottery_name FROM survey_lottery WHERE id = t.lottery_id ) lotteryName, CAST((SELECT is_verify FROM survey_lottery WHERE id = t.lottery_id) AS UNSIGNED) isVerify";
        String $childQuery2 = "( SELECT truename FROM user WHERE id = t.send_id ) sendName";
        String $condition = queryCondition(data);
        String $limit = String.format(" limit %d,%d", (page - 1) * limit, limit);
        baseSql = baseSql.replace("$column", $column)
                .replace("$childQuery1", $childQuery1)
                .replace("$childQuery2", $childQuery2)
                .replace("$surveyId", surveyId.toString())
                .replace("$condition", $condition)
                .replace("$sort", "order by " + parseSort(sort))
                .replace("$limit", $limit);
        log.debug(baseSql);
        return baseSql;
    }

    /**
     * 解析排序
     *
     * @param sort createTime_desc,sendTime_asc
     * @return
     */
    private String parseSort(String sort) {
	    String result = sort.replace("_", " ");
	    return result;
    }

	public String prizeWinnerDownloadSql(SurveyPrizeWinnerParam data, Long surveyId) {
		String sql = String.format("SELECT\n" +
				" t.response_id responseId,\n" +
				" r.sequence,\n" +
				" r.c_id customerId,\n" +
				" r.euid externalUserId,\n" +
				" r.department_id departmentId,\n" +
				" r.department_name departmentName,\n" +
				" r.customer_name customerName,\n" +
				" r.customer_gender customerGender,\n" +
				" r.department_code departmentCode,\n" +
				" r.external_company_id externalCompanyId,\n" +
				" r.default_pa defaultPa,\n" +
				" r.default_pb defaultPb,\n" +
				" r.default_pc defaultPc,\n" +
				" r.parameters,\n" +
				" t.type,\n" +
				" t.prize_name prizeName,\n" +
				" t.status,\n" +
				" t.prize_code prizeCode,\n" +
				" t.name,\n" +
				" t.phone,\n" +
				" t.province,\n" +
				" t.city,\n" +
				" t.district,\n" +
				" t.address,\n" +
				" l.lottery_name lotteryName,\n" +
				" t.create_time AS createTime,\n" +
				" u.truename AS sendName,\n" +
				" t.send_time AS sendTime,\n" +
				" t.refund_time AS refundTime,\n" +
				" t.amount AS amount\n" +
				"FROM\n" +
				" survey_lottery_prize_winner t\n" +
				" LEFT JOIN survey_lottery l on t.lottery_id=l.id\n" +
				" LEFT JOIN survey_response r ON t.response_id = r.id\n" +
				" LEFT JOIN user u ON t.send_id = u.id \n" +
				"WHERE t.survey_id = %s %s order by createTime desc\n", surveyId, queryCondition(data));
		log.debug(sql);
		return sql;
	}

	/**
	 * 查询条件
	 *
	 * @param data
	 * @return
	 */
	public String queryCondition(SurveyPrizeWinnerParam data) {
		StringBuilder condition = new StringBuilder();
		if (data.getKey() != null && data.getKey() != "") {
			//condition.append(String.format("and (t.prize_name like '%s' or t.name like '%s' or t.phone like '%s' or t.prize_code like '%s' or t.amount like '%s' )", "%" + data.getKey() + "%", "%" + data.getKey() + "%", "%" + data.getKey() + "%", "%" + data.getKey() + "%", "%" + data.getKey() + "%"));
			String key = "%" + data.getKey() + "%";
			condition.append(String.format("and (t.prize_name like '%s' ", key));
			condition.append(String.format("or   t.name like '%s' ", key));
			condition.append(String.format("or   t.phone like '%s' ", key));
			condition.append(String.format("or   t.prize_code like '%s' ", key));
			condition.append(String.format("or   t.amount like '%s' )", key));
		}
		if (data.getPrizeType() != null) {
			condition.append(String.format(" and t.type= %s", data.getPrizeType().ordinal()));
		}
		if (data.getPrizeSendStatus() != null) {
			condition.append(String.format(" and t.status=%s", data.getPrizeSendStatus().ordinal()));
		}
		if (data.getLotteryId() != null) {
			condition.append(String.format(" and t.lottery_id = %s ", data.getLotteryId()));
		}
		return condition.toString();
	}

	/**
	 * 奖励核销统计下载
	 *
	 * @param sid
	 * @param response
	 * @param data
	 */
	@SneakyThrows
	public void downloadStatisticsData(Long sid, HttpServletResponse response, SurveyPrizeWinnerParam data) {
		List<SurveyLotteryWinnerDownloadDto> list = jdbcTemplate.query(prizeWinnerDownloadSql(data, sid), new BeanPropertyRowMapper<>(SurveyLotteryWinnerDownloadDto.class));

		for (SurveyLotteryWinnerDownloadDto dto : list) {
			dto.setResponseId(dto.getResponseId());
			dto.setWinnerInfo(prizeWinnerInfo(dto));
			dto.setParameters("{}".equals(dto.getParameters()) ? null : dto.getParameters());
		}

		response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
		response.setCharacterEncoding("utf-8");
		String fileName = URLEncoder.encode("奖励名单_" + LocalDate.now(), "UTF-8").replaceAll("\\+", "%20");
		response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

		EasyExcel.write(response.getOutputStream(), SurveyLotteryWinnerDownloadDto.class).useDefaultStyle(false).includeColumnFieldNames(includeFiledName(list)).sheet("奖励核销").doWrite(list);
	}

	/**
	 * 获取需要下载的列，排除为空的列
	 *
	 * @param list
	 * @return
	 */
	public Set<String> includeFiledName(List<SurveyLotteryWinnerDownloadDto> list) {
		Set<String> includeFiledName = new HashSet<>();
		int excelPropertySize = SurveyLotteryWinnerDownloadDto.excelPropertySize();
		int existValueSize = 0;
		for (SurveyLotteryWinnerDownloadDto dto : list) {
			Field[] fields = dto.getClass().getDeclaredFields();
			for (int i = 0; i < fields.length; i++) {
				fields[i].setAccessible(true);
				//该列已经存在值
				if (includeFiledName.contains(fields[i].getName())) {
					continue;
				}
				//@ExcelProperty注解中每个属性都有值，直接返回
				//如果第一行的全部属性都有值，不需要继续执行
				if (existValueSize == excelPropertySize) {
					return includeFiledName;
				}
				Object o = null;
				try {
					o = fields[i].get(dto);
				} catch (IllegalAccessException e) {
					e.printStackTrace();
				}
				if (o != null && StringUtils.isNotEmpty(o.toString()) && !"{}".equals(o.toString())) {
					includeFiledName.add(fields[i].getName());
					if (fields[i].getAnnotation(ExcelProperty.class) != null) {
						existValueSize++;
					}
				}
			}
		}
		return includeFiledName;

	}

	/**
	 * 获取不为空的列
	 *
	 * @param dataList
	 * @return
	 */
	public static Set<Integer> buildColumn(List<List> dataList) {
		Set<Integer> notEmptyColumn = new HashSet<>();
		for (int i = 0; i < dataList.size(); i++) {
			List<Object> rows = dataList.get(i);
			for (int j = 0; j < rows.size(); j++) {
				if (notEmptyColumn.size() == rows.size()) {
					return notEmptyColumn;
				}
				if (notEmptyColumn.contains(j)) {
					continue;
				}
				Object o = rows.get(j);
				if (o != null && StringUtils.isNotEmpty(o.toString())) {
					notEmptyColumn.add(j);
				}
			}
		}
		return notEmptyColumn;
	}


	/**
	 * 中奖信息结果列
	 *
	 * @param dto
	 * @return
	 */
	private String prizeWinnerInfo(SurveyLotteryWinnerDownloadDto dto) {
		if (dto.getType() == null) {
			return "";
		}
		if (LotteryPrizeType.VIRTUAL_PRIZE.equals(dto.getType())) {
			return dto.getPrizeCode();
		}
		if (LotteryPrizeType.FIXED_RED_PACK.equals(dto.getType()) || LotteryPrizeType.RANDOM_RED_PACK.equals(dto.getType())) {
			return "抽中微信红包" + (double) dto.getAmount() / 100 + "元！";
		}
		String name = dto.getName() == null ? "" : dto.getName() + "/";
		String phone = dto.getPhone() == null ? "" : dto.getPhone() + "/";
		String province = dto.getProvince() == null ? "" : dto.getProvince();
		String city = dto.getCity() == null ? "" : dto.getCity();
		String district = dto.getDistrict() == null ? "" : dto.getDistrict();
		String address = dto.getAddress() == null ? "" : dto.getAddress();
		String res = name + phone + province + city + district + address;
		return res;
	}
}


















