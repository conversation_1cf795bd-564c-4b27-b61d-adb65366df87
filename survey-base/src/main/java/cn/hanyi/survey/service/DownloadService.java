package cn.hanyi.survey.service;

import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.survey.client.service.SurveyBehaviorService;
import cn.hanyi.survey.client.service.SurveyRandomResultService;
import cn.hanyi.survey.core.constant.DownloadFromType;
import cn.hanyi.survey.core.constant.DownloadType;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.SurveyFileUploadDto;
import cn.hanyi.survey.core.dto.ZipFileDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyBehaviorRecordRepository;
import cn.hanyi.survey.core.repository.SurveyRandomResultRepository;
import cn.hanyi.survey.core.repository.SurveyResponseCellRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.utilis.DownloadUtils;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.core.utilis.WriteExcelUtils;
import cn.hanyi.survey.dto.*;
import cn.hanyi.survey.service.response.DownloadProgressHandler;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.UserTaskService;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.befun.task.constant.TaskStatus;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static cn.hanyi.survey.core.utilis.DownloadUtils.dynamicDownloadFields;
import static cn.hanyi.survey.core.utilis.QuestionsUtils.questionsFilterGroup;
import static cn.hanyi.survey.dto.DownloadFileDto.EX_INCLUDE_TYPE;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DownloadService {

    @Autowired
    SurveyService surveyService;

    @Autowired
    ResponseService responseService;

    @Autowired
    QuestionService questionService;

    @Autowired
    private ExecutorService executorService;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private ChannelService channelService;

    @Autowired
    private SurveyBehaviorService behaviorService;

    @Autowired
    private SurveyBehaviorRecordRepository surveyBehaviorRecordRepository;

    @Autowired
    private SurveyResponseRepository responseRepository;

    @Autowired
    private SurveyResponseCellRepository cellRepository;

    @Autowired
    private UserTaskService userTaskService;

    @Autowired
    private SurveyRandomResultRepository surveyRandomResultRepository;

    @Autowired
    private SurveyRandomResultService surveyRandomResultService;


    /**
     * 使用多线程去获取反而更慢 直接一把梭哈全部拉出来在内存操作
     *
     * @param survey
     * @return
     */
    private List<SurveyResponse> batchLoad(Survey survey, DownloadFromType fromType) {

        List<Integer> status = new ArrayList<>();
        status.add(ResponseStatus.FINAL_SUBMIT.ordinal());
        if (fromType != null && !DownloadFromType.Banner.equals(fromType)) {
            status.add(ResponseStatus.INVALID.ordinal());
        }
        var responses = responseRepository.findAll((r, q, b) ->
                b.and(
                        b.equal(r.get("surveyId"), survey.getId()),
                        b.in(r.get("status")).value(status)
                )
        );
        // deleted = 1;
        // findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndStatusInOrderByCreateTimeAsc
        return responses.stream()
                .filter(r -> !r.getDeleted())
                .filter(SurveyResponse::getIsCompleted)
                .distinct()
                .sorted(Comparator.comparing(SurveyResponse::getCreateTime))
                .collect(Collectors.toList());
    }

    /**
     * 线程池获取下载答题数据
     *
     * @param survey
     * @param total
     * @return
     */
    @Deprecated(since = "1.8.6")
    private List<SurveyResponse> batchLoad(Survey survey, int total) {

        if (total < 1000) {
            Optional<List<SurveyResponse>> surveyResponseOptional = responseService.getBySurvey(survey);
            return surveyResponseOptional.get();
        }

        int size = 800;
        int threads = 10;
        int page = (total % size) > 0 ? (total / size + 1) : (total / size);
        LinkedBlockingQueue<Integer> queue = new LinkedBlockingQueue<>(page);
        List<Integer> pages = IntStream.range(0, page).boxed().collect(Collectors.toList());
        log.info("共{}页，开始加载", page);
        queue.addAll(pages);
        ConcurrentHashMap<Integer, List<SurveyResponse>> allResponses = new ConcurrentHashMap<>();
        CountDownLatch loadCount = new CountDownLatch(page);
        IntStream.range(0, threads).forEach(i -> {
            executorService.execute(() -> {
                Integer p = queue.poll();
                while (p != null) {
                    log.info("正在加载第{}页", p);
                    List<SurveyResponse> list = loadPage(survey, p, size);
                    if (CollectionUtils.isNotEmpty(list)) {
                        allResponses.put(p, list);
                    }
                    loadCount.countDown();
                    p = queue.poll();
                }
            });
        });
        try {
            loadCount.await(60, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }

        if (loadCount.getCount() > 0) {
            throw new RuntimeException("拉取答题数据超时");
        }

        log.info("已加载第{}页", page);
        List<SurveyResponse> allList = new ArrayList<>();
        IntStream.range(0, page).forEach(i -> {
            Optional.ofNullable(allResponses.get(i)).ifPresent(allList::addAll);
        });
        allResponses.clear();
        return allList;
    }

    private List<SurveyResponse> loadPage(Survey survey, int page, int size) {
        Optional<List<SurveyResponse>> surveyResponseOptional = responseService.getBySurvey(survey, PageRequest.of(page, size));
        return surveyResponseOptional.orElse(new ArrayList<>());
    }

    /**
     * 下载答题文件
     *
     * @return
     */
    @SneakyThrows
    public void download(HttpServletResponse response, Long surveyId, DownloadDto dto) {
        log.info("download survey-{} file:", surveyId);
        Survey survey = surveyService.requireSurvey(surveyId);
        String zipFileName = URLEncoder.encode(String.format("%s.zip", RegularExpressionUtils.safeTitle(survey.getTitle())), StandardCharsets.UTF_8.toString());
        sendResponse(response, zipFileName, createFile(surveyId, dto, null));
    }

    @SneakyThrows
    @Transactional
    public ByteArrayOutputStream createFile(Long surveyId, DownloadDto dto, Long taskId) {
        Survey survey = surveyService.requireSurvey(surveyId);
        List<SurveyResponse> surveyResponse = null;
        //根据筛选条件 匹配responseId
        dto.appendResponseIds(responseService.getSearchResponseIds(surveyId, dto.getQueryDto()));
        // #2655 【SurveyLite】客户旅程、回收统计页、下载数据统计口径不一致
        // https://ones.ai/project/#/team/SGajWa97/task/31Ww5P5PRE2khLcH
        // 下载的数据不包括中断，包括提前完成
        if (dto != null && dto.getResponseIds().size() > 0) {
            surveyResponse = responseService.getByIds(dto.getResponseIds());
        } else {
//            int total = responseService.countResponseBySurvey(survey);
//            surveyResponse = batchLoad(survey, total);
            surveyResponse = batchLoad(survey, dto.getFrom());
        }

        questionService.graphQuestion(survey);
        List<SurveyQuestion> surveyQuestion = questionsFilterGroup(survey.getQuestions());

        log.info("{} file size:{}", surveyId, surveyResponse.size());

        DownloadFileDto downloadFileDto = new DownloadFileDto();

        String uploadFolderName = "上传文件/";

        List<ZipFileUrlDto> additionalFiles = new ArrayList<>();

        log.info("问卷:{}构建下载头部", surveyId);
        List originalHeaders = downloadFileDto.buildHeaders(surveyQuestion, surveyResponse, false);
        List codeHeaders = downloadFileDto.buildHeaders(surveyQuestion, surveyResponse, true);
        log.info("问卷:{}构建下载头部完成", surveyId);

        Map<String, SurveyResponseCell> cells = buildCells1(survey, surveyResponse);
        log.info("问卷:{}构建填充数据", surveyId);
        AnswerAndFileDto originalAnswers = buildResponseData(survey, surveyResponse, false, additionalFiles, cells);
        additionalFiles = originalAnswers.getFiles();
        AnswerAndFileDto codeAnswers = buildResponseData(survey, surveyResponse, true, additionalFiles, cells);
        log.info("问卷:{}构建填充数据完成", surveyId);

        //行为记录下载
        List behaviorHeader = null;
        List behaviorData = null;
        List<SurveyBehaviorRecord> behaviorRecord = behaviorService.findAllBySurveyId(surveyId);
        if (behaviorRecord.size() > 0) {
            behaviorHeader = behaviorService.getBehaviorHeader(surveyId, behaviorRecord.get(behaviorRecord.size() - 1).getResponseId());
            //行为记录 第一列是responseId 问卷总页数 = (header.size() - 1) / 2
            Integer totalPage = behaviorHeader != null && behaviorHeader.size() > 1 ? (behaviorHeader.size() - 1) / 2 : 0;
            behaviorData = behaviorService.getBehaviorData(surveyResponse.stream().map(s -> s.getId()).collect(Collectors.toList()), totalPage);
        }

        //问题随机结果表
        List randomResultHeader = null;
        List randomResultData = null;
        Long randomResult = surveyRandomResultRepository.existsRandomResult(surveyId);
        if (randomResult != null) {
            randomResultHeader = surveyRandomResultService.getRandomResultHeader(surveyId, survey.getQuestions());
            randomResultData = surveyRandomResultService.getRandomResultData(surveyId, survey.getQuestions(), surveyResponse);
        }

        if (taskId != null) {
            userTaskService.updateTaskStatus(taskId, TaskStatus.RUNNING);
            // 编码数据+原始数据+行为数据 + 3个head
            var total = (2 + 2 * surveyResponse.size()) + (behaviorRecord.size() > 0 ? behaviorRecord.size() + 1 : 0);
            // 随机结果表
            total += CollectionUtils.isNotEmpty(randomResultData) ? randomResultData.size() + 1 : 0;
            userTaskService.updateTaskTotalSize(taskId, total, true);
        }

        List<ZipFileDto> innerFile = new ArrayList<>();
        String safaTitle = RegularExpressionUtils.safeTitle(survey.getTitle());

        var downloadProgressHandler = new DownloadProgressHandler(taskId, userTaskService);

        //csv
        if (DownloadType.CSV.equals(dto.getDownloadType())) {
            ByteArrayOutputStream originalOutputStream = easyCsv(originalHeaders, getDataList(originalAnswers.getAnswers(), surveyResponse), downloadProgressHandler);
            ByteArrayOutputStream codeOutputStream = easyCsv(codeHeaders, getDataList(codeAnswers.getAnswers(), surveyResponse), downloadProgressHandler);
            innerFile.add(new ZipFileDto(safaTitle + "_原始数据.csv", originalOutputStream.toByteArray()));
            innerFile.add(new ZipFileDto(safaTitle + "_编码数据.csv", codeOutputStream.toByteArray()));
            originalOutputStream.close();
            codeOutputStream.close();
            //行为数据
            if (behaviorData != null && behaviorData.size() > 0 && surveyResponse.size() > 0) {
                ByteArrayOutputStream behaviorOutputStream = easyCsv(behaviorHeader, behaviorData, downloadProgressHandler);
                innerFile.add(new ZipFileDto(safaTitle + "_行为数据.csv", behaviorOutputStream.toByteArray()));
                behaviorOutputStream.close();
            }
            //随机结果
            if (randomResult != null) {
                ByteArrayOutputStream behaviorOutputStream = easyCsv(randomResultHeader, randomResultData, downloadProgressHandler);
                innerFile.add(new ZipFileDto(safaTitle + "_随机结果.csv", behaviorOutputStream.toByteArray()));
                behaviorOutputStream.close();
            }
        } else {
            //excel
            ByteArrayOutputStream original = easyExcel(originalHeaders, getDataList(originalAnswers.getAnswers(), surveyResponse), downloadProgressHandler);
            ByteArrayOutputStream code = easyExcel(codeHeaders, getDataList(codeAnswers.getAnswers(), surveyResponse), downloadProgressHandler);
            innerFile.add(new ZipFileDto(safaTitle + "_原始数据.xlsx", original.toByteArray()));
            innerFile.add(new ZipFileDto(safaTitle + "_编码数据.xlsx", code.toByteArray()));
            original.close();
            code.close();
            //行为数据
            if (behaviorData != null && behaviorData.size() > 0 && surveyResponse.size() > 0) {
                ByteArrayOutputStream behavior = easyExcel(behaviorHeader, behaviorData, downloadProgressHandler);
                innerFile.add(new ZipFileDto(safaTitle + "_行为数据.xlsx", behavior.toByteArray()));
                behavior.close();
            }
            //随机结果
            if (randomResult != null) {
                ByteArrayOutputStream behaviorOutputStream = easyExcel(randomResultHeader, randomResultData, downloadProgressHandler);
                innerFile.add(new ZipFileDto(safaTitle + "_随机结果.xlsx", behaviorOutputStream.toByteArray()));
                behaviorOutputStream.close();
            }
        }

        //  清楚动态字段cache
        dynamicDownloadFields().clear();

        for (ZipFileUrlDto file : additionalFiles) {
            innerFile.add(new ZipFileDto(uploadFolderName + file.getFileName(), fileStorageService.download(file.getUrl()).bytes()));
        }
        log.info("问卷:{}下载文件完成", surveyId);
        return DownloadUtils.zipFiles(innerFile);

    }

    public void sendResponse(HttpServletResponse response, String fileName, ByteArrayOutputStream zipOutputStream) {
        DownloadUtils.responseRequest(response, fileName, zipOutputStream);
    }

    /**
     * 构建csv中的数据
     *
     * @param survey
     * @param response
     * @return
     */
    private AnswerAndFileDto buildResponseData(Survey survey, List<SurveyResponse> response, Boolean transCode, List<ZipFileUrlDto> files, Map<String, SurveyResponseCell> cells) {
        List<ZipFileUrlDto> cellFiles = new ArrayList<>();
        List<QuestionType> transCodeQuestions = List.of(
                QuestionType.SINGLE_CHOICE,
                QuestionType.COMBOBOX,
                QuestionType.MULTIPLE_CHOICES,
                QuestionType.MATRIX_CHOICE,
                QuestionType.EVALUATION,
                QuestionType.SCORE_EVALUATION
        );

        List<List> answers = questionsFilterGroup(survey.getQuestions()).stream().filter(q -> !EX_INCLUDE_TYPE.contains(q.getType())).map(q -> {
            return response.stream().map(c -> {

                List ans = new ArrayList();
                SurveyResponseCell cell = cells.get(String.format("%s%s", c.getId(), q.getName()));

                // q没有items的话直接取value
                if (q.getItems().isEmpty() && !q.getHasOther()) {

                    if (cell != null) {
                        switch (q.getType()) {
                            case FILE:
                            case SIGNATURE:
                                ans.add(String.format("%s_%s", c.getId(), q.getCode()));
                                // 为空表示第一次调用需要插入数据
                                if (files.isEmpty()) {
                                    AtomicInteger index = new AtomicInteger(0);
                                    ((List<SurveyFileUploadDto>) cell.getValue()).forEach(file -> {
                                        if (file.getPath() == null) {
                                            return;
                                        }
                                        long repeatCount = cellFiles.stream().filter(f -> f.getFileName().equals(String.format("%s/%s_%s_%s", c.getId(), c.getId(), q.getCode(), file.getFileName()))).count();
                                        // 如果文件名重复，则添加一个尾数
                                        String name = file.getFileName();
                                        String fileName = repeatCount > 0
                                                ? name.substring(0, (name.length() - file.getType().length() - 1)) + "_" + index.getAndIncrement() + "." + file.getType()
                                                : name;
                                        String formatFileName = String.format("%s/%s_%s_%s", c.getId(), c.getId(), q.getCode(), fileName);
                                        cellFiles.add(new ZipFileUrlDto(formatFileName, file.getPath()));
                                    });
                                }
                                break;
                            case AREA:
                            case DROP_DOWN:
                                ans.addAll((ArrayList) cell.getValue());
                                break;
                            case DATE:
                                ans.add(cell.getValue() == null ? "" : new SimpleDateFormat(q.getAreaType().getFormat()).format(cell.getValue()));
                                break;
                            case ORGANIZE:
                                ans.add(String.join("/", (ArrayList) cell.getValue()));
                                break;
                            case LOCATION:
                                final Map<String, Object> res = JsonHelper.toMap(cell.getValue());
                                //数据来源，经度，纬度，位置名称
                                ans.add(res.get("locationType"));
                                ans.add(res.get("longitude"));
                                ans.add(res.get("latitude"));
                                ans.add(res.get("location"));
                                break;
                            default:
                                ans.add(cell.getValue());
                        }

                    } else {
                        // 由于下拉题存在没有题干的情况，只能添加2个空格
                        if (q.getType() == QuestionType.DROP_DOWN) {
                            if ("".equals(q.getConfigure())) {
                                ans.addAll(List.of("", ""));
                            } else {
                                QuestionDropDownDto config = JsonHelper.toObject(q.getConfigure(), QuestionDropDownDto.class);
                                config.getProps().forEach(x -> ans.add(""));
                            }
                        } else if (q.getType() == QuestionType.AREA) {
                            String[] split = q.getAreaType().getFormat().split("-");
                            //根据选项设置占多少列
                            for (int i = 0; i < split.length; i++) {
                                ans.add("");
                            }
                        } else if (q.getType() == QuestionType.LOCATION) {
                            //未答题地理位置题空四列
                            ans.addAll(List.of("", "", "", ""));
                        } else {
                            ans.add("");
                        }
                    }
                } else {
                    // 有items的情况需要将value转化成text
                    Boolean enableTextInput = false;
                    if (cell != null) {

                        List<Object> cellValue;
                        List<Object> r = new ArrayList<>();
                        if (cell.getValue() instanceof List) {
                            cellValue = (List<Object>) cell.getValue();
                        } else {
                            cellValue = Arrays.asList(cell.getValue());
                        }
                        //打分评价提-打分
                        if (q.getType().equals(QuestionType.SCORE_EVALUATION)) {
                            r.add(cell.getCellScore());
                        }

                        boolean evl = false;
                        for (SurveyQuestionItem i : q.getItems()) {
                            if (i.getEnableTextInput()) enableTextInput = true;
                            if (cellValue.contains(i.getValue())) {
                                evl = true;
                                r.add(transCode && transCodeQuestions.contains(q.getType())
                                        ? (q.getType() == QuestionType.MULTIPLE_CHOICES ? 1 : q.getItems().indexOf(i) + 1)
                                        : RegularExpressionUtils.replaceHtml(Objects.toString(i.getText())));
                                //如果开启了设置文本输入
                                if (i.getEnableTextInput()) {
//                                    HashMap<String, Object> hashMap = JSON.parseObject(cell.getCommentValue(), HashMap.class);
                                    HashMap<String, Object> hashMap = JsonHelper.toObject(cell.getCommentValue(), HashMap.class);
                                    r.add(hashMap == null ? "" : hashMap.getOrDefault(i.getValue(), "").toString());
                                }
                                if (q.getType() == QuestionType.EVALUATION || q.getType() == QuestionType.SCORE_EVALUATION) {
                                    r.add(cell.getTags() == null ? "" : RegularExpressionUtils.replaceHtml(cell.getTags()));
                                    r.add(StringUtils.isEmpty(cell.getCommentValue()) ? "" : cell.getCommentValue());
                                    break;
                                }
                            } else if (
                                    q.getType() == QuestionType.MATRIX_SCORE
                                            || q.getType() == QuestionType.MATRIX_CHOICE
                                            || q.getType() == QuestionType.MATRIX_SLIDER
                                            || q.getType() == QuestionType.RANKING) {

                                Object value = ((LinkedHashMap<?, ?>) cellValue.get(0)).get(i.getValue());
                                if (value instanceof Number) {
                                    r.add(Integer.valueOf(value.toString()));
                                    continue;
                                } else if(value == null) {
                                    r.add("");
                                }
                                // 矩阵单选会有columns
                                String nameValue = Objects.toString(((LinkedHashMap<?, ?>) cellValue.get(0)).get(i.getValue()), "");
                                if (q.getColumns() != null && !q.getColumns().isEmpty()) {
                                    Optional<SurveyQuestionColumn> column = q.getColumns().stream().filter(n -> n.getValue().equals(nameValue)).findFirst();
                                    if (column.isPresent()) {
/*                                        stringBuilder = transCode && transCodeQuestions.contains(q.getType())
                                                ? new StringBuilder(String.valueOf(q.getColumns().indexOf(column.get()) + 1))
                                                : new StringBuilder(Objects.toString(column.orElse(new SurveyQuestionColumn()).getText(), ""));
                                        */
                                        if (transCode && transCodeQuestions.contains(q.getType())) {
                                            r.add(q.getColumns().indexOf(column.get()) + 1);
                                        } else {
                                            r.add(RegularExpressionUtils.replaceHtml(Objects.toString(column.orElse(new SurveyQuestionColumn()).getText(), "")));
                                        }
                                    }
                                }
                                // r.add(RegularExpressionUtils.replaceHtml(stringBuilder.toString()));
                            } else if (q.getType() == QuestionType.EXPERIMENT) { //实验题
                                //解析答题端联合实验题数据
                                List<ExperimentDto> dtos = JsonHelper.toList(cell.getValue().toString(), ExperimentDto.class);
                                //编辑端展示的属性和水平值
                                String config = q.getConfigure();
//                                List<Map<String, Object>> values = (List<Map<String, Object>>) JSON.parse(config);
                                List<Map> values = JsonHelper.toList(config, Map.class);

                                //将编辑端所有的 ”组合x:属性y“ 添加到集合中
                                List<String> list = new ArrayList<>();
                                q.getItems().stream().forEach(x -> {
                                    values.stream().forEach(y -> {
                                        String result = x.getValue() + ":" + y.get("id");
                                        //将编辑端所有”组合:属性“放在集合里，用于判断答题端是否存在这个组合值
                                        list.add(result);
                                    });
                                });

                                //遍历答题端组合
                                dtos.stream().forEach(group -> {
                                    //答题端属性
                                    List<Map<String, List<String>>> attrs = group.getAttributes();
                                    for (Map<String, List<String>> map : attrs) {
                                        for (Map.Entry<String, List<String>> entry : map.entrySet()) {
                                            //组合:属性
                                            String str = String.format("%s:%s", group.getCode(), entry.getValue().get(1));
                                            //答题端的 "组合:属性" 在编辑端存在
                                            if (list.contains(str)) {
                                                r.add(RegularExpressionUtils.replaceHtml(entry.getValue().get(0)));
                                            } else {
                                                r.add("");
                                            }
                                        }
                                    }
                                });
                                break;
                            } else {
                                if (q.getType() == QuestionType.EVALUATION || q.getType() == QuestionType.SCORE_EVALUATION) {
                                    continue;
                                }
                                if (transCode && q.getType() == QuestionType.MULTIPLE_CHOICES) {
                                    //code中多选题未选中用0表示
                                    r.add(0);
                                    if (i.getEnableTextInput()) r.add("");
                                } else {
                                    r.add("");
                                    if (i.getEnableTextInput()) r.add("");
                                }
                            }
                        }

                        // 单选题只留一个选项
                        if (q.getType() == QuestionType.SINGLE_CHOICE || q.getType() == QuestionType.COMBOBOX) {
                            r.removeIf(x -> "".equals(x.toString()));
                            if (enableTextInput && (StringUtils.isEmpty(cell.getCommentValue()))) r.add("");
                        }

                        //处理表情评价题切换数量 数据变化问题
                        if (q.getType() == QuestionType.EVALUATION && !evl) {
                            for (int i = 0; i < 3; i++) {
                                r.add("");
                            }
                        }
                        ans.addAll(r.isEmpty() ? Collections.singleton("") : r);

                    } else {
                        if (q.getType() == QuestionType.EVALUATION || q.getType() == QuestionType.SCORE_EVALUATION) {
                            for (int i = 0; i < 3; i++) {
                                ans.add("");
                            }
                            //打分评价多一列
                            if (q.getType() == QuestionType.SCORE_EVALUATION) {
                                ans.add("");
                            }
                        } else {
                            Boolean singleTextInput = false;
                            for (SurveyQuestionItem i : q.getItems()) {
                                enableTextInput = false;
                                if (i.getEnableTextInput()) {
                                    enableTextInput = true;
                                    singleTextInput = true;
                                }
                                if (q.getType() != QuestionType.SINGLE_CHOICE && q.getType() != QuestionType.COMBOBOX) {
                                    ans.add("");
                                    if (enableTextInput) ans.add("");
                                }
                            }
                            // 单选题只留一个选项
                            if (q.getType() == QuestionType.SINGLE_CHOICE || q.getType() == QuestionType.COMBOBOX) {
                                ans.add("");
                                //单选题未选择，任意一个选项添加了文本输入都需要执行
                                if (singleTextInput) ans.add("");
                            }
                        }
                    }
                }

                // TODO 兼容other有其他选项的话需要获取commentValue
                if (q.getHasOther() && q.getType() != QuestionType.EVALUATION && q.getType() != QuestionType.SCORE_EVALUATION) {
                    if (cell != null) {
                        ans.add(transCode && transCodeQuestions.contains(q.getType())
                                ? String.valueOf(Math.max(q.getItems().size(), q.getColumns().size()) + 1)
                                : StringUtils.isEmpty(cell.getCommentValue()) ? "" : cell.getCommentValue());
                    } else {
                        ans.add("");
                    }

                }
                return ans;
            }).collect(Collectors.toList());
        }).collect(Collectors.toList());

        return new AnswerAndFileDto(answers, files.isEmpty() ? cellFiles : files);
    }


    /**
     * 构建cellmap
     */
    @Deprecated(since = "1.8.6")
    private Map<String, SurveyResponseCell> buildCells(Survey survey, List<SurveyResponse> responseList) {
        int batchSize = 200;
        Map<Long, SurveyQuestion> questionMap = survey.getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        // <qId+qName, cells>
        Map<String, SurveyResponseCell> cells = new ConcurrentHashMap<>();
        ListUtils.partition(responseList, batchSize).forEach(batchResponse -> {
            Map<Long, SurveyResponse> responseMap = batchResponse.stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
            List<SurveyResponseCell> resCells = responseService.getCells(survey, batchResponse);
            resCells.parallelStream().forEach(cell -> {
                SurveyQuestion question = questionMap.get(cell.getQuestionId());
                SurveyResponse response = responseMap.get(cell.getResponseId());
                if (question != null && response != null) {
                    try {
                        cells.put(String.format("%s%s", response.getId(), question.getName()), cell);
                    } catch (javax.persistence.EntityNotFoundException e) {
                        // 删除了的问题就不用再填充
                    }
                }
            });
        });
        return cells;
    }

    private Map<String, SurveyResponseCell> buildCells1(Survey survey, List<SurveyResponse> responseList) {
        Map<Long, SurveyQuestion> questionMap = survey.getQuestions().stream().collect(Collectors.toMap(BaseEntity::getId, Function.identity()));
        Map<String, SurveyResponseCell> cellMap = new ConcurrentHashMap<>();
        // <rId+qName, cells>
        cellRepository.findAll((r, q, b) -> b.and(b.equal(r.get("surveyId"), survey.getId())))
                .parallelStream()
                .forEach(
                        cell -> {
                            SurveyQuestion question = questionMap.get(cell.getQuestionId());
                            if (question != null) {
                                cellMap.put(String.format("%s%s", cell.getResponseId(), question.getName()), cell);
                            }
                        }
                );
        return cellMap;
    }

    public ByteArrayOutputStream easyExcel(List headers, List<List> dataList, WriteHandler handler) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();

        List<String> header = headers;

        // 获取标题信息
        List<List<String>> headTitleInfo = WriteExcelUtils.excelTitle(header);
        // 获取数据信息
        List<List<Object>> excelDataInfo = WriteExcelUtils.excelData(dataList);

        // 获取合并单元格信息
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(byteArrayOutputStream).useDefaultStyle(false);
        if (handler != null) {
            excelWriterBuilder.registerWriteHandler(handler);
        }
        excelWriterBuilder.head(headTitleInfo).sheet("导出")
                .doWrite(excelDataInfo);

        byteArrayOutputStream.flush();
        byteArrayOutputStream.close();
        return byteArrayOutputStream;
    }

    public ByteArrayOutputStream easyCsv(List headers, List<List> dataList, WriteHandler handler) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        // 写入bom, 防止中文乱码
        byte[] bytes = {(byte) 0xEF, (byte) 0xBB, (byte) 0xBF};
        byteArrayOutputStream.write(bytes);

        List<String> header = headers;

        // 获取标题信息
        List<List<String>> headTitleInfo = WriteExcelUtils.excelTitle(header);
        // 获取数据信息
        List<List<Object>> excelDataInfo = WriteExcelUtils.excelData(dataList);

        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(byteArrayOutputStream).useDefaultStyle(false);
        if (handler != null) {
            excelWriterBuilder.registerWriteHandler(handler);
        }

        excelWriterBuilder.excelType(ExcelTypeEnum.CSV).head(headTitleInfo).sheet("导出").doWrite(excelDataInfo);

        byteArrayOutputStream.flush();
        byteArrayOutputStream.close();
        return byteArrayOutputStream;
    }

    private List<List<Object>> getDataList(List<List> answers, List<SurveyResponse> response) {
        Map<Long, List<SurveyChannel>> channelMap = null;
        if (response.size() > 0) {
            Long surveyId = response.get(0).getSurveyId();
            channelMap = getChannelMap(surveyId);
        }

        List<List<Object>> dataList = new ArrayList<>();
        for (int i = 0; i < response.size(); i++) {

            Long channelId = response.get(i).getChannelId();
            List<SurveyChannel> surveyChannels = new ArrayList<>();
            if (channelId != null) {
                surveyChannels = channelMap.get(channelId);
            }

            List attributes = new DownloadFileDto().buildResponse(response.get(i), surveyChannels);
            int finalI = i;
            List<List> answer = (List) answers.stream().map(ans -> ans.get(finalI)).collect(Collectors.toList());
            List answerFlat = (List) answer.stream().flatMap(Collection::stream).collect(Collectors.toList());
            attributes.addAll(answerFlat);
            dataList.add(attributes);
        }
        return dataList;
    }

    /**
     * Map<channelId,List<SurveyChannel>
     *
     * @param sid
     * @return
     */
    public Map<Long, List<SurveyChannel>> getChannelMap(Long sid) {
        List<SurveyChannel> channelList = channelService.getChannelList(sid);
        Map<Long, List<SurveyChannel>> result = channelList.stream().collect(Collectors.groupingBy(x -> x.getId()));
        return result;
    }
}