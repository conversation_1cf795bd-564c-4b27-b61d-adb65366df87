package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.question.FormatType;
import cn.hanyi.survey.core.constant.question.InputType;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.SurveyResponseFinishTimeOnly;
import cn.hanyi.survey.core.dto.lottery.MetaDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.core.utilis.RegularExpressionUtils;
import cn.hanyi.survey.dto.analysis.*;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppType;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.entity.Organization;
import org.befun.auth.service.OrganizationService;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.EntityUtility;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.ParamsHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.persistence.EntityManager;
import javax.persistence.Tuple;
import javax.persistence.TypedQuery;
import javax.persistence.criteria.*;
import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class AnalyticService {
    @Autowired
    private EntityManager entityManager;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private QuestionsDynamicItemService questionsDynamicItemService;

    @Autowired
    private SurveyQuestionRepository questionRepository;

    @Value("${survey.free-analysis-limit:5000}")
    private Integer FREE_ANALYSIS_LIMIT;

    @Autowired
    private OrganizationService organizationService;

    /**
     * example
     * - measures:
     * - count(id) - rolling: day
     * - dimensions:
     * - create_time
     *
     * @param cubeDto
     */
    public List<Map<String, Object>> queryByCube(Survey survey, CubeDto cubeDto) throws NoSuchFieldException {
        Assert.notNull(cubeDto, "missing cube");
        checkVersionLimit();

        List<FilterDto> filters = cubeDto.getFilters();
        CriteriaBuilder builder = entityManager.getCriteriaBuilder();
        CriteriaQuery<Tuple> query = builder.createTupleQuery();
        Root<SurveyResponse> from = query.from(SurveyResponse.class);
//        List<SearchCriteria> filterCriteriaList = buildFilter(filters, from, survey);
        List<ResourceQueryCriteria> filterCriteriaList = buildFilter(filters, from, survey);

        List<Selection<?>> measures = new ArrayList<>();
        List<Expression<?>> dimensions = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        List<String> measureNames = new ArrayList<>();
        List<String> measureTitles = new ArrayList<>();

        Map<String, Object> measureMap = new HashMap<>();

        for (int i = 0; i < cubeDto.getMeasures().size(); i++) {
            MeasureDto measure = cubeDto.getMeasures().get(i);
            Selection expression = buildMeasure(builder, from, measure);
            if (expression != null) {
                measureMap.put(measure.getField(), expression);
                measures.add(expression);
            }
            measureNames.add(measure.getName());
            measureTitles.add(measure.getTitle());
        }

        for (int i = 0; i < cubeDto.getDimensions().size(); i++) {
            DimensionDto dimension = cubeDto.getDimensions().get(i);
            Selection expression = buildDimension(builder, from, dimension);
            if (expression != null) {
                measureMap.put(dimension.getField(), expression);
                measures.add(expression);
            }
            measureNames.add(dimension.getName());
            measureTitles.add(dimension.getTitle());
            dimensions.add((Expression<?>) expression);
        }

        // grouping
        CriteriaQuery<Tuple> select = query.multiselect(measures);
        select = select.groupBy(dimensions);


        // filter, TBD, cube should have other filter as well...
        if (filterCriteriaList.size() > 0) {
            ArrayList<Specification<SurveyResponse>> specifications = new ArrayList<>();
            GenericSpecification<SurveyResponse> specification = new GenericSpecification<>();

            // 处理日期
            HashMap<String, String> dateMap = new HashMap<>(2) {{
                put("createTime", "createTime");
                put("finishTime", "finishTime");
            }};

            filterCriteriaList.forEach(s -> {
                if (dateMap.getOrDefault(s.getKey(), null) != null) {
                    specifications.add((Specification<SurveyResponse>) (root, query1, builder1) -> {
                        if (QueryOperator.GREATER_THAN_EQUAL == s.getOperator()) {
                            Expression<String> formattedDate = builder1.function("DATE_FORMAT_DAY", String.class, builder.function("IFNULL", String.class, from.get("finishTime"), from.get("createTime")));
                            return builder1.greaterThanOrEqualTo(formattedDate, DateHelper.toLocalDate((Date) s.getValue()).toString());
                        }
                        return builder1.equal(builder1.function("DATE_FORMAT_DAY", String.class, from.get(dateMap.get(s.getKey()))), DateHelper.toLocalDate((Date) s.getValue()).toString());
                    });
                } else {
                    specification.add(s);
                }
            });
            specifications.add(specification);
            select = select.where(specifications.stream().map(s -> s.toPredicate(from, query, builder)).toArray((value) -> {
                return new Predicate[specifications.size()];
            }));
        }

        // order by
        Sort sort = cubeDto.getSort();
        if (sort != null) {
            Sort.Direction sortDirection = sort.get().findFirst().get().getDirection();
            String sortProperty = sort.get().findFirst().get().getProperty();

            if (measureMap.containsKey(sortProperty)) {
                if (sortDirection == Sort.Direction.DESC) {
                    select.orderBy(builder.desc((Expression<?>) measureMap.get(sortProperty)));
                }
                if (sortDirection == Sort.Direction.ASC) {
                    select.orderBy(builder.asc((Expression<?>) measureMap.get(sortProperty)));
                }
            }
        }

        TypedQuery<Tuple> typedQuery = entityManager.createQuery(select);
        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            typedQuery.setParameter(entry.getKey(), entry.getValue());
        }
        // limit
        if (cubeDto.getLimit() != 0) {
            typedQuery = typedQuery.setMaxResults(cubeDto.getLimit());
        }
        List<Tuple> result = typedQuery.getResultList();

        List<Map<String, Object>> transResult = new ArrayList<>();
        for (int i = 0; i < result.size(); i++) {
            Map<String, Object> item = new HashMap<>();
            for (int j = 0; j < result.get(i).toArray().length; j++) {
                item.put(measureNames.get(j), result.get(i).get(j));
            }
            transResult.add(item);
        }
        return transResult;
    }

    @SneakyThrows
    private List<ResourceQueryCriteria> buildFilter(List<FilterDto> filters, Root<? extends BaseEntity> from, Survey survey) {
        List<ResourceQueryCriteria> criteriaList = new ArrayList<>();

        ResourceQueryCriteria criteria = new ResourceQueryCriteria("surveyId", survey.getId());
        criteriaList.add(criteria);

        for (int i = 0; i < filters.size(); i++) {
            FilterDto filter = filters.get(i);

            if (!filter.isValid()) {
                log.warn("invalid filter {}", filter.getName());
                continue;
            }
            Field field = EntityUtility.getFieldsByClass(from.getModel().getJavaType()).get(filter.getName());
            criteria = ParamsHelper.parseCriteria(filter.getName(), Objects.toString(filter.getValue()), field);
            if (QueryOperator.valueOfAlias(filter.getOperator()) != null) {
                criteria.setOperator(QueryOperator.valueOfAlias(filter.getOperator()));
            }
            criteriaList.add(criteria);
        }

        return criteriaList;
    }

    private Selection buildDimension(CriteriaBuilder builder,
                                     Root<? extends BaseEntity> from,
                                     DimensionDto dimension) throws NoSuchFieldException {
        String field = dimension.getField();
        Class<?> type = getEntityFieldType(from.getModel().getJavaType(), field).getType();


        if (Date.class.equals(type)) {
            // deal time dimension..
            switch (dimension.getGranularity()) {
                case DAY:
                    return builder.function("DATE_FORMAT_DAY", String.class, from.get(field));
                case MONTH:
                    return builder.function("DATE_FORMAT_MONTH", String.class, from.get(field));
                case YEAR:
                    return builder.function("DATE_FORMAT_YEAR", String.class, from.get(field));
                case DAY_IFNULL:
                    return builder.function("DATE_FORMAT_DAY", String.class, builder.function("IFNULL", String.class, from.get("finishTime"), from.get("createTime")), from.get(field));
            }
        }
        return from.get(field);
    }

    private Field getEntityFieldType(Class<? extends BaseEntity> entityClass, String name) throws NoSuchFieldException {
        Field field;
        if (name.equals("createTime") || name.equals("modifyTime")) {
            field = entityClass.getField(name);
        } else {
            field = entityClass.getDeclaredField(name);
        }
        return field;
    }

    private Selection buildMeasure(CriteriaBuilder builder, Root<? extends BaseEntity> from, MeasureDto measure) {
        String field = measure.getField();

        switch (measure.getType()) {
            case AVG:
                return builder.avg(from.get(field));
            case SUM:
                return builder.sum(from.get(field));
            case MIN:
                return builder.min(from.get(field));
            case MAX:
                return builder.max(from.get(field));
            case COUNT:
                if (measure.getFilters() == null || measure.getFilters().size() == 0) {
                    return builder.count(from);
                }
                CriteriaBuilder.Case selectCase = builder.<Integer>selectCase();
                for (int i = 0; i < measure.getFilters().size(); i++) {
                    FilterDto filter = measure.getFilters().get(i);
                    Path<? extends BaseEntity> path = from.get(filter.getName());

                    // 将measure中filter转为enum
                    try {
                        Field filterField = getEntityFieldType(from.getModel().getJavaType(), filter.getName());
                        if (filterField.getType().isEnum() && !filter.getValue().getClass().isEnum()) {
                            String filterValue = filter.getValue().toString().toUpperCase();
                            List<?> filterEnum = Arrays.stream(filterField.getType().getEnumConstants())
                                    .filter(x -> filterValue.equals(x.toString().toUpperCase()))
                                    .collect(Collectors.toList());
                            filter.setValue(Objects.toString((((Enum) filterEnum.get(0)).ordinal())));
                        }
                    } catch (IndexOutOfBoundsException | NoSuchFieldException e) {
                        e.printStackTrace();
                        log.warn("unknown filter {}", filter.getName());
                    }

                    switch (filter.getOperator()) {
                        case "eq": {
                            selectCase = selectCase.when(builder.equal(path, filter.getValue()), 1);
                            selectCase.otherwise(0);
                            break;
                        }
                        case "neq": {
                            selectCase = selectCase.when(builder.notEqual(path, filter.getValue()), 1);
                            selectCase.otherwise(0);
                            break;
                        }
                        default:
                            throw new RuntimeException("unexpected operator in measure " + measure.getField());
                    }
                }
                return builder.sum(selectCase);
            case COUNT_DISTINCT:
                return builder.countDistinct(from.get(field));
            case CALC:
                // 动态计算的聚合之后再计算,数据库层面忽略
                return null;
            case DIFF_AVG:
                String name1 = measure.getFilters().get(0).getName();
                String name2 = measure.getFilters().get(1).getName();

                Path<Object> path1 = from.get(name1);
                Path<Object> path2 = from.get(name2);

                Expression<Long> diff1;
                Expression<Long> diff2;
                if (path1.getJavaType().equals(Date.class)) {
                    diff1 = builder.function("UNIX_TIMESTAMP", Long.class, path1);
                    diff2 = builder.function("UNIX_TIMESTAMP", Long.class, path2);
                } else {
                    diff1 = path1.as(Long.class);
                    diff2 = path2.as(Long.class);
                }

                return builder.avg(builder.diff(diff1, diff2));
            default:
                return from.get(field);
        }
    }


    /**
     * @param survey
     * @param questionId
     * @return
     */
    public SingleAnalysisResponseDto<?> singleAnalysis(@NotNull Survey survey, Optional<Long> questionId, SingleAnalysisRequest data) {
        checkVersionLimit();
        String responseCondition = filterAnalysis(data);
        if (!questionId.isPresent()) {
            return countTotalAndRecentTime(survey, responseCondition);
        } else {

            SurveyQuestion question = survey.getQuestions().stream().
                    filter(x -> x.getId().equals(questionId.get())).
                    findFirst().
                    orElseThrow(EntityNotFoundException::new);

            Integer total = validTotal(survey.getId(), question.getId(), responseCondition);

            switch (question.getType()) {
                case SINGLE_CHOICE:
                case COMBOBOX:
                    return countSingleChoice(survey, question, total, responseCondition);
                case MULTIPLE_CHOICES:
                    return countMultipleChoice(survey, question, total, responseCondition);
                case SCORE:
                    return countScore(survey, question, total, responseCondition);
                case MATRIX_SCORE:
                    return countMatrix(survey, question, total, responseCondition);
                case MATRIX_SLIDER:
                    return countSlider(survey, question, total, responseCondition);
                case MATRIX_CHOICE:
                    return countMatrixSingle(survey, question, total, responseCondition);
                case EVALUATION:
                case SCORE_EVALUATION:
                    return countEvaluation(survey, question, total, responseCondition);
                case RANKING:
                    return countRanking(survey, question, total, responseCondition);
                case TEXT:
                    return countText(survey, question, data, responseCondition);
                case BLANK:
                case MULTIPLE_BLANK:
                    return countBlank(survey, question, data, responseCondition);
                case NPS:
                    return countNps(survey, question, total, responseCondition);
                case AREA:
                    return countArea(survey, question, total, responseCondition);
                default:
                    throw new BadRequestException();
            }
        }

    }

    /**
     * 获取文本题答题内容
     *
     * @param survey
     * @param question
     * @param data
     * @return
     */
    private SingleAnalysisResponseDto<?> countText(Survey survey, SurveyQuestion question, SingleAnalysisRequest data, String responseCondition) {
        String countSql = "SELECT count(r.id) " +
                "FROM (select * from survey_response sr where s_id=%s %s) r " +
                "INNER JOIN survey_response_cell c ON r.id = c.r_id " +
                "WHERE c.q_id = %s AND c.s_val != ''  $condition ";

        String baseSql = "SELECT r.id as responseId, r.sequence as sequence, c.s_val as cellValue " +
                "FROM (select * from survey_response sr where s_id=%s %s) r " +
                "INNER JOIN survey_response_cell c ON r.id = c.r_id " +
                "WHERE c.q_id = %s AND c.s_val != ''  $condition order by r.finish_time desc $limit";
        baseSql = String.format(baseSql, survey.getId(), responseCondition, question.getId());
        countSql = String.format(countSql, survey.getId(), responseCondition, question.getId());
        //搜索条件
        if (data.getTextResponseCell() == null || "".equals(data.getTextResponseCell().trim())) {
            baseSql = baseSql.replace("$condition", "");
            countSql = countSql.replace("$condition", "");
        } else {
            String condition = " and c.s_val like '%" + data.getTextResponseCell() + "%'";
            baseSql = baseSql.replace("$condition", condition);
            countSql = countSql.replace("$condition", condition);
        }

        //分页
        baseSql = baseSql.replace("$limit", String.format("limit %s,%s", (data.getPage() - 1) * data.getLimit(), data.getLimit()));

        Integer count = jdbcTemplate.queryForObject(countSql, Integer.class);
        List<SingleAnalysisResponseDto.Text> valueCount = jdbcTemplate.query(baseSql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.Text.class));
        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(valueCount.size() == 0 ? new ArrayList<>() : (ArrayList) valueCount);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(count));
        responseDto.setMeta(new MetaDto(data.getPage(), data.getLimit(), (long) count));
        return responseDto;
    }


    private SingleAnalysisResponseDto<?> countBlank(Survey survey, SurveyQuestion question, SingleAnalysisRequest data, String responseCondition) {
        StringBuffer groupConcat = new StringBuffer("GROUP_CONCAT(");
        StringBuffer groupOrder = new StringBuffer();
        question.getItems().stream().sorted(Comparator.comparing(SurveyQuestionItem::getSequence)).map(BaseQuestionItem::getValue).collect(Collectors.toList()).forEach(v -> {
            groupConcat.append(String.format("JSON_UNQUOTE(JSON_EXTRACT(c.j_val, '$.\"%s\"')),';',", v));
            groupOrder.append("'").append(v).append("',");
        });

        groupOrder.delete(groupOrder.length() - 1, groupOrder.length());
        groupConcat.delete(groupConcat.length() - 5, groupConcat.length());

        groupConcat.append(String.format("ORDER BY FIELD(JSON_KEYS(c.j_val), %s))", groupOrder));


        String countSql = "SELECT count(r.id) " +
                "FROM (select * from survey_response sr where s_id=%s %s) r " +
                "INNER JOIN survey_response_cell c ON r.id = c.r_id " +
                "WHERE c.q_id = %s AND c.j_val != ''  $condition ";

        String baseSql = "SELECT r.id as responseId, r.sequence as sequence, %s as cellValue " +
                "FROM (select * from survey_response sr where s_id=%s %s) r " +
                "INNER JOIN survey_response_cell c ON r.id = c.r_id " +
                "WHERE c.q_id = %s AND c.j_val != ''  $condition GROUP BY r.id  order by r.finish_time desc $limit";
        baseSql = String.format(baseSql, groupConcat, survey.getId(), responseCondition, question.getId());
        countSql = String.format(countSql, survey.getId(), responseCondition, question.getId());
        //搜索条件
        if (data.getTextResponseCell() == null || "".equals(data.getTextResponseCell().trim())) {
            baseSql = baseSql.replace("$condition", "");
            countSql = countSql.replace("$condition", "");
        } else {
            String condition = " and c.j_val like '%" + data.getTextResponseCell() + "%'";
            baseSql = baseSql.replace("$condition", condition);
            countSql = countSql.replace("$condition", condition);
        }

        //分页
        baseSql = baseSql.replace("$limit", String.format("limit %s,%s", (data.getPage() - 1) * data.getLimit(), data.getLimit()));

        Integer count = jdbcTemplate.queryForObject(countSql, Integer.class);
        List<SingleAnalysisResponseDto.Text> valueCount = jdbcTemplate.query(baseSql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.Text.class));
        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(valueCount.size() == 0 ? new ArrayList<>() : (ArrayList) valueCount);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(count));
        responseDto.setMeta(new MetaDto(data.getPage(), data.getLimit(), (long) count));
        return responseDto;
    }

    /**
     * 驼峰转下划线  createTime desc --> create_time desc
     *
     * @param sort
     * @return
     */
    public String parseSort(String sort) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isEmpty(sort)) {
            return "";
        }
        for (int i = 0; i < sort.length(); i++) {
            char c = sort.charAt(i);
            if (Character.isUpperCase(c)) {
                sb = sb.append("_" + Character.toLowerCase(c));
            } else {
                sb = sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 统计答题数以及最新答题时间
     *
     * @param survey
     * @return
     */
    private SingleAnalysisResponseDto countTotalAndRecentTime(@NotNull Survey survey, String responseCondition) {
//        Long count = responseService.numOfResponse(survey, ResponseStatus.FINAL_SUBMIT);

        String baseSql = "select count(*) from survey_response sr where s_id=%1$s %2$s";
        String sql = String.format(baseSql, survey.getId(), responseCondition);

        Long count = jdbcTemplate.queryForObject(sql, Long.class);

        SurveyResponseFinishTimeOnly time = responseService.LatestResponseTime(survey, ResponseStatus.FINAL_SUBMIT);

        SingleAnalysisResponseDto returnData = new SingleAnalysisResponseDto();

        returnData.setRows(new ArrayList<>(Arrays.asList(
                new SingleAnalysisResponseDto.SingleCount("已收集有效问卷", count),
                new SingleAnalysisResponseDto.SingleCount("最后回收时间", time == null ? null : time.getFinishTime())
        )));

        return returnData;
    }

    private List<SurveyQuestionItem> getQuestionItems(SurveyQuestion question) {
        List<SurveyQuestionItem> items = null;
        if (question != null) {
            if (question.getIsDynamicItem() == null || !question.getIsDynamicItem()) {
                items = question.getItems();
            } else {
                items = questionsDynamicItemService.findAllByQuestion(question.getId());
            }
        }
        if (items == null) {
            items = new ArrayList<>();
        }
        return items;
    }

    /**
     * 统计单选题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countSingleChoice(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {
        List<SurveyQuestionItem> items = getQuestionItems(question);
        if (items.isEmpty()) {
            SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
            responseDto.setRows(new ArrayList<SingleAnalysisResponseDto.Choice>());
            responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));
            return responseDto;
        }

        String baseSql = "select t1.text, t1.value, t1.value/%3$s percent from " +
                "(select s_val text, count(id) value from survey_response_cell " +
                "where s_id=%1$s and q_id=%2$s and r_id in (select id from survey_response sr where s_id=%1$s %4$s) group by s_val ) t1;";
        String sql = String.format(baseSql, survey.getId(), question.getId(), total, responseCondition);
        log.debug(sql);
        List<SingleAnalysisResponseDto.Choice> valueCount = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.Choice.class));

        Map<String, SingleAnalysisResponseDto.Choice> valueMap = new HashMap<>();
        Optional.ofNullable(valueCount).ifPresent(i -> {
            i.forEach(j -> {
                valueMap.put(j.getText(), j);
            });
        });

        BiFunction<String, SurveyQuestionItem, SingleAnalysisResponseDto.Choice> getOrDefault = (key, item) -> {
            SingleAnalysisResponseDto.Choice j = valueMap.get(key);
            if (j == null) {
                j = new SingleAnalysisResponseDto.Choice();
                j.setValue(0);
                j.setPercent(0.0);
            }
            j.setName(j.getText());
            j.setTextInputView(item.getEnableTextInput());
            j.setText(item.getText());
            return j;
        };

        List<SingleAnalysisResponseDto.Choice> rows = items
                .stream()
                .map(i -> getOrDefault.apply(i.getValue(), i))
                .collect(Collectors.toList());

   /*     if (question.getHasOther()) {
            rows.add(getOrDefault.apply("other", question.getOtherLabel()));
        }*/

        SingleAnalysisResponseDto returnData = new SingleAnalysisResponseDto();
        returnData.setRows((ArrayList) rows);
        returnData.setExtraData(new SingleAnalysisResponseDto.Extra(total));
        return returnData;
    }

    /**
     * 统计多选题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countMultipleChoice(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {
        List<SurveyQuestionItem> items = getQuestionItems(question);
        if (items.isEmpty()) {
            SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
            responseDto.setRows(new ArrayList<SingleAnalysisResponseDto.Choice>());
            responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));
            return responseDto;
        }

        LinkedHashMap<String, Object> valueTextMap = new LinkedHashMap<>();

        items.forEach(item -> {
            valueTextMap.put(item.getValue(), item.getText());
        });

        if (question.getHasOther()) {
            valueTextMap.put("other", question.getOtherLabel());
        }

        StringBuffer baseSqlBuffer = new StringBuffer();


        String baseSql = "SELECT " +
                "  item AS text," +
                "  value," +
                "  value / %4$s AS percent" +
                "  FROM (SELECT item,SUM(IF(s_val LIKE CONCAT('%%', item, '%%'), 1, 0)) AS value" +
                "  FROM (%1$s) AS items" +
                "  JOIN survey_response_cell AS cells" +
                "  ON cells.s_val LIKE CONCAT('%%', items.item, '%%')" +
                "  WHERE cells.s_id = %2$s" +
                "    AND cells.q_id = %3$s" +
                "    AND cells.r_id IN (" +
                "      SELECT id" +
                "      FROM survey_response AS sr" +
                "      WHERE sr.s_id = %2$s %5$s" +
                "    )" +
                "  GROUP BY items.item" +
                ") AS results;";

        StringBuffer union = new StringBuffer();

        valueTextMap.keySet().forEach(key -> {
            union.append(String.format("select '%1$s' item union ", key));
        });

        union.delete(union.length() - 6, union.length());

        baseSqlBuffer.append(
                String.format(
                        baseSql,
                        union,
                        survey.getId(),
                        question.getId(),
                        total,
                        responseCondition
                ));

        String sql = baseSqlBuffer.toString();
        log.debug(sql);
        List<SingleAnalysisResponseDto.Choice> valueCount = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.Choice.class));
        Map<String, Boolean> textInputMap = items.stream().collect(Collectors.toMap(SurveyQuestionItem::getValue, SurveyQuestionItem::getEnableTextInput));
        valueCount.forEach(x -> {
            if (valueTextMap.containsKey(x.getText())) {
                x.setName(x.getText());
                x.setTextInputView(textInputMap.get(x.getText()));
                x.setText(valueTextMap.get(x.getText()).toString());
            }
        });

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) valueCount);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));

        return responseDto;
    }

    /**
     * 统计量表题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countScore(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {

        List<SingleAnalysisResponseDto.Score> valueTextMap = new ArrayList<>();

        List<String> labels = question.getLabels();
        String minLabel = labels.size() > 0 ? labels.get(0) : "";
        String maxLabel = labels.size() >= 1 ? labels.get(labels.size() - 1) : "";

        // 我不明白为啥前端说是不同题型 说不好改 总有人刷锅
        // nps 0-10 写死
        // 非nps 数字  min max
        //       点赞  maxLength
        //       星星  maxLength
        //       爱心  maxLength
        Integer min = 0;
        Integer max = 10;

        if (!question.getIsNps()) {
            if (InputType.NUMBER.equals(question.getInputType())) {
                min = question.getMin();
                max = question.getMax();
            } else {
                min = 1;
                max = question.getMaxLength();
            }
        }

        // 用于将0-10映射为0-10分
        for (int i = min; i <= max; i++) {
            String label = i == min ? minLabel : i == max ? maxLabel : "";
            valueTextMap.add(new SingleAnalysisResponseDto.Score(String.format("%d分", i), 0, 0.00, label));
        }

        // -1映射为不适用
        if (question.getInapplicable()) {
            valueTextMap.add(new SingleAnalysisResponseDto.Score(question.getInapplicableLabel(), 0, 0.00, ""));
        }

        String baseSql = "select t1.text, t1.value, t1.value/%3$s percent from " +
                "(select i_val text, count(id) value from survey_response_cell " +
                "where s_id=%1$s and q_id=%2$s and r_id in (select id from survey_response sr where s_id=%1$s %4$s) group by i_val ) t1 " +
                "join " +
                "(select count(id) total from survey_response_cell where s_id=%1$s and q_id=%2$s and r_id in (select id from survey_response sr where s_id=%1$s %4$s))t2 ";
        String sql = String.format(baseSql, survey.getId(), question.getId(), total, responseCondition);
        log.debug(sql);
        List<SingleAnalysisResponseDto.Score> valueCount = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.Score.class));


        Double recommend = null;
        Double average = null;
        List<SingleAnalysisResponseDto.Recommender> recommenders = null;

        // 除去不适用
        // 总数量
        int totalOut = valueCount.stream().filter(x -> !x.getText().equals("-1")).mapToInt(SingleAnalysisResponseDto.Score::getValue).sum();

        if (question.getIsNps()) {
            // 打分大于等于9
            int nine = valueCount.stream().filter(x -> !x.getText().equals("-1") && Integer.parseInt(x.getText()) >= 9).mapToInt(SingleAnalysisResponseDto.Score::getValue).sum();
            // 打分小于等于6
            int six = valueCount.stream().filter(x -> !x.getText().equals("-1") && Integer.parseInt(x.getText()) <= 6).mapToInt(SingleAnalysisResponseDto.Score::getValue).sum();
            // 净推荐
            recommend = Double.parseDouble(String.format(
                    "%.4f",
                    totalOut < 1 ? 0.00 : ((nine - six) / (double) totalOut)
            ));


            recommenders = new ArrayList<>();
            // 贬损者(0-6)
            double detractor = valueCount.stream().filter(x -> !"-1".equals(x.getText()) && Integer.parseInt(x.getText()) >= 0 && Integer.parseInt(x.getText()) <= 6).mapToDouble(SingleAnalysisResponseDto.Score::getPercent).sum();
            recommenders.add(new SingleAnalysisResponseDto.Recommender("贬损者", Double.parseDouble(String.format("%.2f", detractor * 100))));
            // 中立者(7-8)
            double neutral = valueCount.stream().filter(x -> !"-1".equals(x.getText()) && Integer.parseInt(x.getText()) >= 7 && Integer.parseInt(x.getText()) <= 8).mapToDouble(SingleAnalysisResponseDto.Score::getPercent).sum();
            recommenders.add(new SingleAnalysisResponseDto.Recommender("中立者", Double.parseDouble(String.format("%.2f", neutral * 100))));
            // 推荐者(9-10)
            double recommender = valueCount.stream().filter(x -> !"-1".equals(x.getText()) && Integer.parseInt(x.getText()) >= 9 && Integer.parseInt(x.getText()) <= 10).mapToDouble(SingleAnalysisResponseDto.Score::getPercent).sum();
            recommenders.add(new SingleAnalysisResponseDto.Recommender("推荐者", Double.parseDouble(String.format("%.2f", recommender * 100))));
        } else {
            int countScore = 0;
            List<SingleAnalysisResponseDto.Score> scores = valueCount.stream().filter(x -> !"-1".equals(x.getText())).collect(Collectors.toList());
            for (SingleAnalysisResponseDto.Score score : scores) {
                countScore += Integer.parseInt(score.getText()) * score.getValue();
            }
            average = totalOut < 1 ? 0.00 : (countScore / (double) totalOut);
        }

        valueTextMap.forEach(x -> {
            valueCount.stream().filter(y ->
                    y.getText().equals(x.getText().substring(0, x.getText().length() - 1)) || (question.getInapplicableLabel() != null && question.getInapplicableLabel().equals(x.getText()) && "-1".equals(y.getText()))
            ).findFirst().ifPresent(y -> {
                x.setValue(y.getValue());
                x.setPercent(Double.parseDouble(String.format("%.2f", y.getPercent() * 100)));
            });
        });

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) valueTextMap);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(
                total,
                recommend,
                average,
                recommenders
        ));

        return responseDto;
    }

    /**
     * 统计矩阵量表题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countMatrix(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {
        StringBuffer baseSqlBuffer = new StringBuffer();
        StringBuffer sumSqlBuffer = new StringBuffer();
        StringBuffer textSqlBuffer = new StringBuffer();

        Integer min = 0;
        Integer max = 10;

        if (!question.getIsNps()) {
            if (InputType.NUMBER.equals(question.getInputType())) {
                min = question.getMin();
                max = question.getMax();
            } else {
                min = 1;
                max = question.getMaxLength();
            }
        }

        List<Integer> loopList = IntStream.rangeClosed(min, max).boxed().collect(Collectors.toList());

        List<String> labels = question.getLabels();
        String minLabel = labels.size() > 0 ? labels.get(0) : "";
        String maxLabel = labels.size() >= 1 ? labels.get(labels.size() - 1) : "";

        // -1映射为不适用
        if (question.getInapplicable()) {
            loopList.add(-1);
        }

        textSqlBuffer.append("select t2.text,");

        Integer finalMin = min;
        Integer finalMax = max;
        loopList.forEach(i -> {
            String text = question.getInapplicable() && i == -1 ? question.getInapplicableLabel() : String.valueOf(i + "分");
            // 标签
            text = i.equals(finalMin) ? String.format("%s(%s)", text, minLabel) : text;
            text = i.equals(finalMax) ? String.format("%s(%s)", text, maxLabel) : text;

            textSqlBuffer.append(String.format("t2.`%1$s` '%2$s',", text.strip(), text.strip().replace("'", "\\'")));
            sumSqlBuffer.append(String.format("sum(if(score=%1$s,1,0)) '%2$s',", i, text.replace("'", "\\'")));
        });

        // 去除最后一个逗号
        sumSqlBuffer.deleteCharAt(sumSqlBuffer.length() - 1);
        textSqlBuffer.deleteCharAt(textSqlBuffer.length() - 1);

        question.getItems().forEach(item -> {
            baseSqlBuffer.append(String.format(" union select '%s' text,", item.getText().replace("'", "\\'"))).
                    append(sumSqlBuffer).
                    append(String.format(" from (select id, json_extract(j_val, '$.%s') as score", item.getValue())).
                    append(String.format(" from survey_response_cell where s_id=%1$s and q_id =%2$s and r_id in (select id from survey_response sr where s_id=%1$s %3$s)) t", survey.getId(), question.getId(), responseCondition));
        });

        // 去除第一个union
        baseSqlBuffer.delete(0, 6);

        textSqlBuffer.append(" from (");
        baseSqlBuffer.insert(0, textSqlBuffer);
        baseSqlBuffer.append(") t2;");

        log.debug(baseSqlBuffer.toString());
        List<Map<String, Object>> valueCount = jdbcTemplate.queryForList(baseSqlBuffer.toString());

        if (total > 0) {
            valueCount.forEach(value -> {
                // 统计除去不适用的总数

                double totalOutInapplicable = value.entrySet().stream().filter(
                        i -> !i.getKey().equals(question.getInapplicableLabel())
                                && !"text".equals(i.getKey())
                ).mapToDouble(i -> Integer.parseInt(String.valueOf(i.getValue()))).sum();

                double numTotal = value.entrySet().stream().filter(
                        i -> !i.getKey().equals(question.getInapplicableLabel())
                                && !"text".equals(i.getKey())
                ).mapToDouble(i -> Integer.parseInt(String.valueOf(i.getValue())) * Integer.parseInt(i.getKey().replaceAll("分\\(?.*\\)?", ""))).sum();

                double avg = totalOutInapplicable < 1 ? 0.00 : (numTotal / totalOutInapplicable);
                value.put("avg", Double.parseDouble(String.format("%.2f", avg)));
            });
        }

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) valueCount);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));

        return responseDto;
    }

    /**
     * 滑动题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countSlider(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {
        StringBuffer baseSqlBuffer = new StringBuffer();
        StringBuffer sumSqlBuffer = new StringBuffer();
        StringBuffer textSqlBuffer = new StringBuffer();


        textSqlBuffer.append("select t2.text,");

        String minString = "最小值";
        String maxString = "最大值";
        String varianceString = "方差";
        String stddevString = "标准差";
        String avgString = "平均值";

        textSqlBuffer.append(String.format("t2.`%1$s` '%1$s',", minString));
        textSqlBuffer.append(String.format("t2.`%1$s` '%1$s',", maxString));
        textSqlBuffer.append(String.format("t2.`%1$s` '%1$s',", varianceString));
        textSqlBuffer.append(String.format("t2.`%1$s` '%1$s',", stddevString));
        textSqlBuffer.append(String.format("t2.`%1$s` '%1$s',", avgString));

        sumSqlBuffer.append(String.format("IFNULL(min(score),0) '%1$s',", minString));
        sumSqlBuffer.append(String.format("IFNULL(max(score),0) '%1$s',", maxString));
        sumSqlBuffer.append(String.format("IFNULL(variance(score),0) '%1$s',", varianceString));
        sumSqlBuffer.append(String.format("IFNULL(stddev(score),0) '%1$s',", stddevString));
        sumSqlBuffer.append(String.format("IFNULL(avg(score),0) '%1$s',", avgString));

        // 去除最后一个逗号
        sumSqlBuffer.deleteCharAt(sumSqlBuffer.length() - 1);
        textSqlBuffer.deleteCharAt(textSqlBuffer.length() - 1);

        question.getItems().forEach(item -> {
            baseSqlBuffer.append(String.format(" union select '%s' text,", item.getText())).
                    append(sumSqlBuffer).
                    append(String.format(" from (select id, json_extract(j_val, '$.%s') as score", item.getValue())).
                    // 需要排除不适用 -1
                            append(String.format(" from survey_response_cell where s_id=%1$s and q_id =%2$s and r_id in (select id from survey_response sr where s_id=%1$s %3$s)) t where t.score!=-1", survey.getId(), question.getId(), responseCondition));
        });

        // 去除第一个union
        baseSqlBuffer.delete(0, 6);

        textSqlBuffer.append(" from (");
        baseSqlBuffer.insert(0, textSqlBuffer);
        baseSqlBuffer.append(") t2;");

        log.debug(baseSqlBuffer.toString());
        List<Map<String, Object>> valueCount = jdbcTemplate.queryForList(baseSqlBuffer.toString());

        valueCount.forEach(value -> {
            value.put(varianceString, Double.parseDouble(String.format("%.2f", value.get(varianceString))));
            value.put(stddevString, Double.parseDouble(String.format("%.2f", value.get(stddevString))));
            value.put(avgString, Double.parseDouble(String.format("%.2f", value.get(avgString))));
        });

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) valueCount);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));

        return responseDto;
    }


    /**
     * 统计矩阵单选
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countMatrixSingle(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {
        StringBuffer baseSqlBuffer = new StringBuffer();
        StringBuffer sumSqlBuffer = new StringBuffer();
        StringBuffer textSqlBuffer = new StringBuffer();

        textSqlBuffer.append("select t2.text,");

        question.getColumns().forEach(c -> {
            textSqlBuffer.append(String.format("t2.`%1$s` '%1$s',", c.getValue()));
            sumSqlBuffer.append(String.format("sum(if(score='%1$s',1,0)) '%1$s',", c.getValue()));
        });

        // 去除最后一个逗号
        sumSqlBuffer.deleteCharAt(sumSqlBuffer.length() - 1);
        textSqlBuffer.deleteCharAt(textSqlBuffer.length() - 1);

        question.getItems().forEach(item -> {
            baseSqlBuffer.append(String.format(" union select '%s' text,", item.getText().replace("'", "\\'"))).
                    append(sumSqlBuffer).
                    append(String.format(" from (select id, json_extract(j_val, '$.%s') as score", item.getValue())).
                    append(String.format(" from survey_response_cell where s_id=%1$s and q_id =%2$s and r_id in (select id from survey_response sr where s_id=%1$s %3$s)) t", survey.getId(), question.getId(), responseCondition));
        });

        // 去除第一个union
        baseSqlBuffer.delete(0, 6);

        textSqlBuffer.append(" from (");
        baseSqlBuffer.insert(0, textSqlBuffer);
        baseSqlBuffer.append(") t2;");

        log.debug(baseSqlBuffer.toString());
        List<Map<String, Object>> valueCount = jdbcTemplate.queryForList(baseSqlBuffer.toString());

        if (total > 0) {
            valueCount.forEach(value -> {
                double totalOutInapplicable = value.entrySet().stream().filter(
                        i -> !i.getKey().equals(question.getInapplicableLabel())
                                && !"text".equals(i.getKey())
                ).mapToDouble(i -> Integer.parseInt(String.valueOf(i.getValue()))).sum();

                value.put("total", Double.parseDouble(String.format("%.2f", totalOutInapplicable)));
            });
        }

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) valueCount);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));

        return responseDto;
    }

    /**
     * 统计评价题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countEvaluation(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {
        if (question.getItems().isEmpty()) {
            SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
            responseDto.setRows(new ArrayList<SingleAnalysisResponseDto.Choice>());
            responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));
            return responseDto;
        }

        String cellValue = String.format("select s_val, tags,cell_score from survey_response_cell where s_id=%1$s and q_id =%2$s and r_id in (select id from survey_response sr where s_id=%1$s %3$s)", survey.getId(), question.getId(), responseCondition);
        List<SingleAnalysisResponseDto.StringValueTags> valueTags = jdbcTemplate.query(cellValue, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.StringValueTags.class));

        ArrayList<SingleAnalysisResponseDto.ValuePercent> rows = new ArrayList<>();

        question.getItems().stream().sorted(Comparator.comparing(SurveyQuestionItem::getSequence)).forEach(item -> {
            String text = item.getText();
            int count = Math.toIntExact(valueTags.stream().filter(i -> i.getS_val().equals(item.getValue())).count());
            Double percent = total == 0 ? 0.00 : Double.parseDouble(String.format("%.2f", count * 100.0 / total));

            ArrayList<SingleAnalysisResponseDto.ValuePercent> children = new ArrayList<>();
            Arrays.stream(item.getConfigure().split(",")).forEach(conf -> {
                if ("".equals(conf)) {
                    return;
                }
                int subCount = Math.toIntExact(valueTags.stream().filter(i -> i.getS_val().equals(item.getValue())).filter(i -> i.getTags().contains(conf)).count());
                Double subPercent = count == 0 ? 0.00 : Double.parseDouble(String.format("%.2f", subCount * 100.0 / count));
                children.add(new SingleAnalysisResponseDto.ValuePercent(conf, subCount, subPercent));
            });
            SingleAnalysisResponseDto.ValuePercent valuePercent = new SingleAnalysisResponseDto.ValuePercent(text, count, item.getValue(), percent, question.getHasOther(), children);
            rows.add(valuePercent);
        });

        //计算打分评价题分数
        double totalScore = valueTags.stream().filter(i -> i.getCellScore() != null).mapToInt(SingleAnalysisResponseDto.StringValueTags::getCellScore).sum();
        double avgScore = Double.parseDouble(String.format("%.2f", total < 1 ? 0.00 : totalScore / total));

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) rows);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total, QuestionType.SCORE_EVALUATION.equals(question.getType()) ?
                avgScore : null));

        return responseDto;
    }

    /**
     * 统计Nps评价题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countNps(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {

        List<SingleAnalysisResponseDto.ValuePercent> valueTextMap = new ArrayList<>();

        List<String> labels = question.getLabels();
        String minLabel = labels.size() > 0 ? labels.get(0) : "";
        String maxLabel = labels.size() >= 1 ? labels.get(labels.size() - 1) : "";

        Integer min = 0;
        Integer max = 10;

        // 用于将0-10映射为0-10分
        for (int i = min; i <= max; i++) {
            String label = i == min ? minLabel : i == max ? maxLabel : "";
            valueTextMap.add(new SingleAnalysisResponseDto.ValuePercent(String.format("%d分", i), 0, 0.00, label));
        }

        // -1映射为不适用
        if (question.getInapplicable()) {
            valueTextMap.add(new SingleAnalysisResponseDto.ValuePercent(question.getInapplicableLabel(), 0, 0.00, ""));
        }
        //统计NPS题分数
        String baseSql = "select t1.text, t1.value, t1.value/%3$s percent from " +
                "(select i_val text, count(id) value from survey_response_cell " +
                "where s_id=%1$s and q_id=%2$s and r_id in (select id from survey_response sr where s_id=%1$s %4$s) group by i_val ) t1 ";
        String sql = String.format(baseSql, survey.getId(), question.getId(), total, responseCondition);
        log.debug(sql);
        List<SingleAnalysisResponseDto.ValuePercent> valueCount = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.ValuePercent.class));
        //获取选择的标签 以及文本输入
        String cellValue = String.format("select i_val, s_val, tags, cell_score from survey_response_cell where s_id=%1$s and q_id =%2$s and r_id in (select id from survey_response sr where s_id=%1$s %3$s)", survey.getId(), question.getId(), responseCondition);
        List<SingleAnalysisResponseDto.StringValueTags> valueTags = jdbcTemplate.query(cellValue, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.StringValueTags.class));


        Double average = null;
        List<SingleAnalysisResponseDto.Recommender> recommenders = new ArrayList<>();

        // 除去不适用
        // 总数量
        int totalOut = valueCount.stream().filter(x -> !x.getText().equals("-1")).mapToInt(SingleAnalysisResponseDto.ValuePercent::getValue).sum();

        // 打分大于等于9
        int nine = valueCount.stream().filter(x -> !x.getText().equals("-1") && Integer.parseInt(x.getText()) >= 9).mapToInt(SingleAnalysisResponseDto.ValuePercent::getValue).sum();
        // 打分小于等于6
        int six = valueCount.stream().filter(x -> !x.getText().equals("-1") && Integer.parseInt(x.getText()) <= 6).mapToInt(SingleAnalysisResponseDto.ValuePercent::getValue).sum();
        // 净推荐
        Double recommend = Double.parseDouble(String.format(
                "%.4f",
                totalOut < 1 ? 0.00 : ((nine - six) / (double) totalOut)
        ));

        // 贬损者(0-6)
        double detractor = valueCount.stream().filter(x -> !"-1".equals(x.getText()) && Integer.parseInt(x.getText()) >= 0 && Integer.parseInt(x.getText()) <= 6).mapToDouble(SingleAnalysisResponseDto.ValuePercent::getPercent).sum();
        recommenders.add(new SingleAnalysisResponseDto.Recommender("贬损者", Double.parseDouble(String.format("%.2f", detractor * 100))));
        // 中立者(7-8)
        double neutral = valueCount.stream().filter(x -> !"-1".equals(x.getText()) && Integer.parseInt(x.getText()) >= 7 && Integer.parseInt(x.getText()) <= 8).mapToDouble(SingleAnalysisResponseDto.ValuePercent::getPercent).sum();
        recommenders.add(new SingleAnalysisResponseDto.Recommender("中立者", Double.parseDouble(String.format("%.2f", neutral * 100))));
        // 推荐者(9-10)
        double recommender = valueCount.stream().filter(x -> !"-1".equals(x.getText()) && Integer.parseInt(x.getText()) >= 9 && Integer.parseInt(x.getText()) <= 10).mapToDouble(SingleAnalysisResponseDto.ValuePercent::getPercent).sum();
        recommenders.add(new SingleAnalysisResponseDto.Recommender("推荐者", Double.parseDouble(String.format("%.2f", recommender * 100))));


        valueTextMap.forEach(x -> {
            String value = x.getText().substring(0, x.getText().length() - 1);
            valueCount.stream().filter(y ->
                    y.getText().equals(value) || (question.getInapplicableLabel() != null && question.getInapplicableLabel().equals(x.getText()) && "-1".equals(y.getText()))
            ).findFirst().ifPresent(y -> {
                x.setValue(y.getValue());
                x.setPercent(Double.parseDouble(String.format("%.2f", y.getPercent() * 100)));
            });
            ArrayList<SingleAnalysisResponseDto.ValuePercent> children = new ArrayList<>();
            Integer iValue = Integer.parseInt(value);
            SurveyQuestionItem item = getNpsItem(iValue, question.getItems());
            if (item != null && StringUtils.isNotEmpty(item.getConfigure())) {
                Arrays.stream(item.getConfigure().split(",")).forEach(conf -> {
                    if ("".equals(conf)) {
                        return;
                    }
                    int subCount = Math.toIntExact(valueTags.stream()
                            .filter(i -> Objects.equals(i.getS_val(), item.getValue()))
                            .filter(i -> Objects.equals(iValue,i.getI_val()))
                            .filter(i -> i.getTags() != null && i.getTags().contains(conf)).count());
                    Double subPercent = x.getValue() == 0 ? 0.00 : Double.parseDouble(String.format("%.2f", subCount * 100.0 / x.getValue()));
                    children.add(new SingleAnalysisResponseDto.ValuePercent(conf, subCount, subPercent));
                });
            }
            x.setChildren(children);
            x.setTextInputView(question.getHasOther());
            if (item != null) {
                x.setName(item.getValue());
            }
        });

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) valueTextMap);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(
                total,
                recommend,
                average,
                recommenders
        ));

        return responseDto;
    }

    /**
     * 根据分数 获取nps题的item
     * @param value
     * @param items
     * @return
     */
    private SurveyQuestionItem getNpsItem(Integer value, List<SurveyQuestionItem> items) {
        if (items.size() != 3) return null;
        SurveyQuestionItem item = null;
        switch (value) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
                item = items.get(0);break;
            case 7:
            case 8:
                item = items.get(1);break;
            case 9:
            case 10:
                item = items.get(2);break;
            default:
        }
        return item;
    }


    /**
     * 统计排序题
     *
     * @param survey
     * @param question
     * @return
     */
    private SingleAnalysisResponseDto countRanking(@NotNull Survey survey, SurveyQuestion question, Integer total, String responseCondition) {

        StringBuffer baseSqlBuffer = new StringBuffer();
        StringBuffer sumSqlBuffer = new StringBuffer();
        StringBuffer textSqlBuffer = new StringBuffer();

        List<Integer> loopList = question.getItems().stream().map(SurveyQuestionItem::getSequence).sorted().collect(Collectors.toList());

        textSqlBuffer.append("select t2.text text,");

        loopList.forEach(i -> {
            // 获取i的index
            int sequence = loopList.indexOf(i) + 1;
            String text = "排序第" + sequence;
            textSqlBuffer.append(String.format("t2.`%1$s` '%1$s',", text.strip()));
            sumSqlBuffer.append(String.format("sum(if(score=%1$s,1,0)) '%2$s',", sequence, text));
        });

        // 去除最后一个逗号
        sumSqlBuffer.deleteCharAt(sumSqlBuffer.length() - 1);
        textSqlBuffer.deleteCharAt(textSqlBuffer.length() - 1);

        question.getItems().forEach(item -> {
            baseSqlBuffer.append(String.format(" union select '%s' text,", item.getText())).
                    append(sumSqlBuffer).
                    append(String.format(" from (select id, json_extract(j_val, '$.%s') as score", item.getValue())).
                    append(String.format(" from survey_response_cell where s_id=%1$s and q_id =%2$s and r_id in (select id from survey_response sr where s_id=%1$s %3$s)) t", survey.getId(), question.getId(), responseCondition));
        });

        // 去除第一个union
        baseSqlBuffer.delete(0, 6);

        textSqlBuffer.append(" from (");
        baseSqlBuffer.insert(0, textSqlBuffer);
        baseSqlBuffer.append(") t2;");

        log.debug(baseSqlBuffer.toString());
        List<Map<String, Object>> valueCount = jdbcTemplate.queryForList(baseSqlBuffer.toString());

        if (total > 0) {
            String key = "text";
            valueCount.forEach(value -> {
                // 去除html标签
                value.put(key, RegularExpressionUtils.replaceHtml(value.get(key).toString()));
            });
        }

        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        responseDto.setRows(total == 0 ? new ArrayList<>() : (ArrayList) valueCount);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));

        return responseDto;
    }


    private Integer validTotal(Long surveyId, Long questionId, String responseCondition) {
        String sql = String.format("select count(id)" +
                "from survey_response_cell " +
                "where s_id=%1$s and q_id=%2$s " +
                "and r_id in " +
                "(select id from survey_response sr where s_id=%1$s %3$s)", surveyId, questionId, responseCondition);
        log.debug("total sql: {}", sql);
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }

    public SingleAnalysisResponseDto countTags(Survey survey) {

        String responseCondition = filterAnalysis(new SingleAnalysisRequest());
        String baseSql = "SELECT value as text, COUNT(*) AS value\n" +
                "FROM\n" +
                "    survey_response\n" +
                "    CROSS JOIN JSON_TABLE(\n" +
                "        tags,\n" +
                "        '$[*]' COLUMNS (value VARCHAR(100) PATH '$')\n" +
                "    ) AS tags_table\n" +
                "where s_id =%1$s %2$s    \n" +
                "GROUP BY\n" +
                "    text\n" +
                "ORDER BY\n" +
                "    value DESC;";
        String sql = String.format(baseSql, survey.getId(), responseCondition);

        //  SingleAnalysisResponseDto.SingleCount.class
        List<SingleAnalysisResponseDto.SingleCount> valueCount = jdbcTemplate.query(sql, (rs, rowNum) -> {
            String text = rs.getString("text");
            Integer value = rs.getInt("value");
            return new SingleAnalysisResponseDto.SingleCount(text, value);
        });

        SingleAnalysisResponseDto returnData = new SingleAnalysisResponseDto();
        returnData.setRows((ArrayList) valueCount);
        return returnData;
    }

    /**
     * 获取指定选项的文本输入值
     *
     * @param sid
     * @param data
     */
    public Page<TextInputViewDto> getItemResponseCell(Long sid, TextInputViewRequest data) {

        String responseCondition = filterAnalysis(data);

        String baseSql = "SELECT r.id as responseId, r.sequence as sequence, c.comment_val as cellValue FROM survey_response r\n" +
                "INNER JOIN survey_response_cell c ON r.id = c.r_id AND r.`status` = 1 AND r.deleted = 0 \n" +
                "WHERE r.s_id = %s AND c.q_id = %s %s ";
        baseSql = String.format(baseSql, sid, data.getQid(), responseCondition);

        StringBuffer executeSql = new StringBuffer(baseSql);
        if (StringUtils.isNotEmpty(data.getName())) {
            //多选题含多个选项
            executeSql.append(String.format("AND LOCATE('%s',c.s_val) > 0 ", data.getName()));
        }
        if (StringUtils.isNotEmpty(data.getCellValue())) {
            executeSql.append("AND c.comment_val like '%").append(data.getCellValue()).append("%'");
        }
        String sort = StringUtils.isNotEmpty(data.getSort()) ? data.getSort().replace("_", " ") : "sequence desc";
        executeSql.append("order by ").append(sort);
        List<TextInputViewDto> list = jdbcTemplate.query(executeSql.toString(), new BeanPropertyRowMapper<>(TextInputViewDto.class));
        list = list.stream().filter(x -> StringUtils.isNotEmpty(x.getCellValue())).collect(Collectors.toList());
        SurveyQuestion question = questionRepository.findById(data.getQid()).orElseThrow(() -> new BadRequestException("问题不存在"));
        //单选、多选单独处理答题值
        if (question.getType().equals(QuestionType.SINGLE_CHOICE) || question.getType().equals(QuestionType.MULTIPLE_CHOICES)) {
            list = this.parseMultipleChoice(list, data.getName());
        }

        int size = list.size();
        int fromIndex = (data.getPage() - 1) * data.getLimit();
        int toIndex = data.getPage() * data.getLimit();
        if (toIndex > size) {
            toIndex = size;
        }

        List<TextInputViewDto> res = list.subList(fromIndex, toIndex);
        return new PageImpl<>(res, PageRequest.of(data.getPage() - 1, data.getLimit()), list.size());
    }

    /**
     * 多选题cell值保存的对象，需要处理
     *
     * @param list
     * @return
     */
    private List<TextInputViewDto> parseMultipleChoice(List<TextInputViewDto> list, String name) {
        List<TextInputViewDto> collect = list.stream().map(dto -> {
            try {
                Map<String, Object> map = JsonHelper.toMap(dto.getCellValue());
                if (map == null || Objects.isNull(map.get(name))) {
                    return null;
                }
                dto.setCellValue(map.get(name).toString());
                return dto;
            } catch (Exception e) {
                dto.setCellValue(dto.getCellValue());
            }
            return dto;
        }).filter(x -> x != null).collect(Collectors.toList());
        return collect;
    }

    private SingleAnalysisResponseDto<?> countArea(Survey survey, SurveyQuestion question, int total, String responseCondition) {
        SingleAnalysisResponseDto responseDto = new SingleAnalysisResponseDto();
        if (total == 0) {
            return responseDto;
        }
        List<SingleAnalysisResponseDto.Area> data = getAreaData(survey, question, total, responseCondition);
        responseDto.setRows((ArrayList) data);
        responseDto.setExtraData(new SingleAnalysisResponseDto.Extra(total));
        return responseDto;
    }

    /**
     * 获取地区题省市数据
     *
     * @param survey
     * @param question
     * @param total
     * @param responseCondition
     * @return
     */
    private List<SingleAnalysisResponseDto.Area> getAreaData(Survey survey, SurveyQuestion question, int total, String responseCondition) {

        String sql = String.format("SELECT\n" +
                "SUBSTRING_INDEX(s_val,';',1) province,\n" +
                "IF(LOCATE(';',s_val) > 0,SUBSTRING_INDEX(SUBSTRING_INDEX(s_val,';',2),';',-1),'') city,\n" +
                "count(SUBSTRING_INDEX(SUBSTRING_INDEX(s_val,';',2),';',-1)) count\n" +
                "FROM survey_response_cell \n" +
                "WHERE\n" +
                "s_id = %s \n" +
                "AND q_id = %s \n" +
                "AND type = 12 \n" +
                "AND s_val <> '' \n" +
                "AND r_id IN ( SELECT id FROM survey_response WHERE s_id = %s %s ) \n" +
                "GROUP BY SUBSTRING_INDEX(s_val,';',1) , IF(LOCATE(';',s_val) > 0,SUBSTRING_INDEX(SUBSTRING_INDEX(s_val,';',2),';',-1),'')\n" +
                "order by SUBSTRING_INDEX(s_val,';',1)", survey.getId(), question.getId(), survey.getId(), responseCondition);

        List<AreaAnalysisDto> AreaAnalysisDtos = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(AreaAnalysisDto.class));

        Map<String, Integer> provinceCountSum = AreaAnalysisDtos.stream().filter(x -> x.getCount() != null && x.getCount() instanceof Number)
                .collect(Collectors.groupingBy(
                        province -> province.getProvince(),
                        Collectors.summingInt(count -> count.getCount())
                ));

        //province
        List<SingleAnalysisResponseDto.Area> resultList = provinceCountSum.entrySet().stream()
                .map(entry -> {
                    SingleAnalysisResponseDto.Area area = new SingleAnalysisResponseDto.Area();
                    area.setText(entry.getKey());
                    area.setValue(entry.getValue());
                    BigDecimal bd = new BigDecimal(entry.getValue() * 1.0 / total);
                    bd = bd.setScale(2, RoundingMode.HALF_UP);
                    area.setPercent(bd.doubleValue() * 100);
                    return area;
                })
                .collect(Collectors.toList());

        if (question.getAreaType() == FormatType.PROVINCE) {
            return resultList;
        }

        Map<String, List<AreaAnalysisDto>> provinceMap = AreaAnalysisDtos.stream().collect(Collectors.groupingBy(x -> x.getProvince()));

        //city
        resultList.forEach(area -> {
            area.setChildren(provinceMap.get(area.getText()).stream()
                    .filter(x -> x.getCity() != null && StringUtils.isNotEmpty(x.getCity())).map(x -> {
                        SingleAnalysisResponseDto.Area childArea = new SingleAnalysisResponseDto.Area();
                        childArea.setText(x.getCity());
                        childArea.setValue(x.getCount());
                        BigDecimal bd = new BigDecimal(childArea.getValue() * 1.0 / area.getValue());
                        bd = bd.setScale(2, RoundingMode.HALF_UP);
                        childArea.setPercent(bd.doubleValue() * 100);
                        return childArea;
                    }).collect(Collectors.toList()));
        });
        return resultList;
    }


    /**
     * 地区题每个省
     *
     * @param sid
     * @param qid
     * @param total
     * @param responseCondition
     * @return
     */
    public List<SingleAnalysisResponseDto.Area> statisticsAreaProvince(Long sid, Long qid, long total, String responseCondition) {
        //每个省份
        String countSql = String.format("SELECT SUBSTRING_INDEX(s_val,';',1) name, count(*) count ,FORMAT(count(*)/" + total + ",2) percent " +
                "FROM survey_response_cell \n" +
                "WHERE s_id = %1$s " +
                "and q_id=%2$s \n" +
                "and type=12 \n" +
                "and s_val <> '' \n" +
                "and r_id in (select id from survey_response where s_id = %1$s  %3$s) \n" +
                "GROUP BY SUBSTRING_INDEX(s_val,';',1) ", sid, qid, responseCondition);
        List<SingleAnalysisResponseDto.Area> data = jdbcTemplate.query(countSql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.Area.class));
        return data;

    }

    /**
     * 地区题每个市
     *
     * @param sid
     * @param qid
     * @param provinceName
     * @param total
     * @param responseCondition
     * @return
     */
    public List<SingleAnalysisResponseDto.Area> statisticsAreaCity(Long sid, Long qid, String provinceName, long total, String responseCondition) {
        String countSql = String.format("SELECT\n" +
                " SUBSTRING_INDEX(SUBSTRING_INDEX(s_val,';',2),';',-1) name,\n" +
                " count(*) count, \n" +
                " FORMAT(count(*)/" + total + ",2) percent " +
                "FROM survey_response_cell \n" +
                "WHERE\n" +
                " s_id = %1$s  \n" +
                " and q_id = %2$s  \n" +
                " and type=12 \n" +
                " and LOCATE(';',s_val)\n" +
                " and SUBSTRING_INDEX(s_val,';',1)='%3$s' " +
                " and r_id in (select id from survey_response where s_id = %1$s  %4$s) \n" +
                " GROUP BY\n" +
                " SUBSTRING_INDEX(SUBSTRING_INDEX(s_val,';',2),';',-1)\n", sid, qid, provinceName, responseCondition);

        List<SingleAnalysisResponseDto.Area> data = jdbcTemplate.query(countSql, new BeanPropertyRowMapper<>(SingleAnalysisResponseDto.Area.class));
        return data;
    }

    /**
     * 统计分析筛选
     *
     * @param data
     * @return and xxx=aaa
     */
    public String filterAnalysis(SingleAnalysisRequest data) {
        StringBuilder res = new StringBuilder(" ");
        res.append("and status = 1 and deleted=0");
        //筛选提交时间
        if (data.getSubmitDate() != null && data.getEndDate() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            res.append(String.format(" and finish_time between '%s' and '%s' ", sdf.format(data.getSubmitDate()), sdf.format(DateHelper.atEndOfDay(data.getEndDate()))));
        }
        //筛选渠道类型
        if (data.getChannelTypes().size() > 0) {
            StringJoiner joiner = new StringJoiner(",", "(", ")");
            data.getChannelTypes().forEach(x -> {
                joiner.add(x.ordinal() + "");
            });
            res.append(" and connector_method in " + joiner);
        }
        //筛选渠道
        if (data.getChannelIds().size() > 0) {
            StringJoiner joiner = new StringJoiner(",", "(", ")");
            data.getChannelIds().forEach(x -> joiner.add(x + ""));
            res.append(" and channel_id in " + joiner);
        }
        //筛选省份
        if (StringUtils.isNotEmpty(data.getProvince())) {
            if ("海外".equals(data.getProvince()) || "海外".equals(data.getCity())) {
                res.append(" and country != '中国'");
            } else {
                res.append(String.format(" and locate(province,'%s')", data.getProvince()));
                //市
                if (StringUtils.isNotEmpty(data.getCity())) {
                    res.append(String.format(" and locate(city,'%s')", data.getCity()));
                }
            }
        }
        return res.toString();
    }

    public void checkVersionLimit() {
        // 当前免费版企业本年度所有问卷回收数量
        if (organizationService.parseOrgVersion(TenantContext.getCurrentTenant()) == AppVersion.FREE) {
            Organization organization = organizationService.getCurrent();
            List<AppType> appTypes = organizationService.parseOrgAppTypes(organization);
            // 只判断cem
            if (appTypes.size() == 1 && appTypes.contains(AppType.cem)) {
                // 统计当前免费版企业本年度问卷回收数量
                String sql = String.format("SELECT count(id) from survey_response where org_id=%s and status=1 and YEAR(create_time) = YEAR(CURDATE());", TenantContext.getCurrentTenant());
                Integer currentYearTotal = jdbcTemplate.queryForObject(sql, Integer.class);
                if (currentYearTotal > FREE_ANALYSIS_LIMIT) {
                    throw new SurveyErrorException(SurveyErrorCode.SURVEY_ANALYSIS_LIMIT.formatMsg(currentYearTotal, FREE_ANALYSIS_LIMIT));
                }
            }
        }

    }


}