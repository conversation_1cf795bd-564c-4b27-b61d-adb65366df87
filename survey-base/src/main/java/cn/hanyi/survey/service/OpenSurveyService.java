package cn.hanyi.survey.service;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.projection.SimpleResponse4;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.dto.open.ExternalUserResponseStatusDto;
import cn.hanyi.survey.dto.open.VerifyResponseDto;
import cn.hanyi.survey.dto.open.VerifyResponseResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OpenSurveyService {

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    /**
     * 验证答题有效性
     */
    public VerifyResponseResultDto verifyResponse(VerifyResponseDto dto) {
        if (dto.getSurveyId() == null && dto.getSurveyCode() == null) {
            throw new BadRequestException(SurveyErrorCode.SURVEY_CODE_REPEAT.getMessage());
        }

        AtomicReference<Survey> survey = new AtomicReference<>();

        // 优先使用问卷code
        Optional.ofNullable(dto.getSurveyCode()).flatMap(code -> surveyRepository.findOneBySurveyCode(dto.getSurveyCode())).ifPresent(survey::set);
        // 如果没有使用问卷code，使用问卷id
        if (survey.get() == null) {
            Optional.ofNullable(dto.getSurveyId()).flatMap(id -> surveyRepository.findById(dto.getSurveyId())).ifPresent(survey::set);
        }

        if (survey.get() == null) {
            throw new BadRequestException(SurveyErrorCode.SURVEY_NOT_FOUND.getMessage());
        }

        Optional<SurveyResponse> surveyResponseOptional = surveyResponseRepository.findOneBySurveyIdAndId(survey.get().getId(), dto.getResponseId());

        if (surveyResponseOptional.isEmpty()) {
            throw new BadRequestException(SurveyErrorCode.SURVEY_RESPONSE_NOT_FOUND.getMessage());
        }

        SurveyResponse surveyResponse = surveyResponseOptional.get();

        VerifyResponseResultDto result = VerifyResponseResultDto.builder()
                .surveyId(survey.get().getId())
                .responseId(surveyResponse.getId())
                .surveyCode(survey.get().getSurveyCode())
                .surveyTitle(survey.get().getTitle())
                .responseStatus(surveyResponse.getStatus())
                .startTime(surveyResponse.getCreateTime())
                .finishTime(surveyResponse.getFinishTime())
                .build();
        return result;
    }

    public List<SimpleResponse4> verifyResponseStatus(ExternalUserResponseStatusDto dto) {
        if (CollectionUtils.isEmpty(dto.getSurveyIds())) {
            throw new BadRequestException(SurveyErrorCode.SURVEY_NOT_FOUND.getMessage());
        }

        return surveyResponseRepository.findBySurveyIdInAndExternalUserId(dto.getSurveyIds(), dto.getExternalUserId());

    }
}
