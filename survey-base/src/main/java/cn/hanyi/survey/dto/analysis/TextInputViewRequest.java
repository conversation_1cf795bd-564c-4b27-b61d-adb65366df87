package cn.hanyi.survey.dto.analysis;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.validation.ValidSearchText;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/2/14 14:29:02
 */
@Getter
@Setter
public class TextInputViewRequest extends SingleAnalysisRequest {

    @Schema(description = "问题id")
    @NotNull
    private Long qid;

    @Schema(description = "问题name值")
    @NotNull
    private String name;

    @Schema(description = "答题数据")
    @ValidSearchText
    private String cellValue;

}