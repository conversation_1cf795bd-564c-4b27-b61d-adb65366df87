package cn.hanyi.survey.dto.survey;

import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyGroup;
import cn.hanyi.survey.core.entity.SurveyGroupDto;
import cn.hanyi.survey.dto.SurveySimpleListDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceCustomQueryWithGroupDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.utils.EnumHelper;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Getter
@Setter
public class SurveySearchDto extends ResourceCustomQueryWithGroupDto<Survey, SurveySimpleListDto, SurveyGroup, SurveyGroupDto> {

    @Schema(description = "问卷状态")
    private String status;

    @Schema(hidden = true)
    public boolean isAudit() {
        return getType().equals("audit");
    }

    @Override
    public Boolean getHasGroup() {
        return StringUtils.isEmpty(status) && Optional.ofNullable(super.getHasGroup()).orElse(true);
    }

    @Override
    public ResourceEntityQueryDto<SurveySimpleListDto> transformQueryResource(Set<Long> excludeGroupIds) {
        ResourceEntityQueryDto<SurveySimpleListDto> queryDto = super.transformQueryResource(excludeGroupIds);
        buildStatusCriteria().ifPresent(queryDto::addCriteria);
        if (StringUtils.isNotEmpty(getQ())) {
            if (StringUtils.isNumeric(getQ()) && getQ().length() >= 16) {
                queryDto.addCriteria(new ResourceQueryCriteria("id", getQ()));
                queryDto.setQ(null);
            }
        }
        if (queryDto.getSorts() == null || queryDto.getSorts().isUnsorted()) {
            queryDto.setSorts(Sort.by(Sort.Direction.DESC, "modifyTime"));
        }
        return queryDto;
    }

    public Optional<ResourceQueryCriteria> buildStatusCriteria() {
        if (StringUtils.isNotEmpty(status)) {
            List<SurveyStatus> statusList = EnumHelper.parseList(SurveyStatus.values(), status);
            if (CollectionUtils.isNotEmpty(statusList)) {
                return Optional.of(new ResourceQueryCriteria("status", statusList, QueryOperator.IN));
            }
        }
        return Optional.empty();
    }
}
