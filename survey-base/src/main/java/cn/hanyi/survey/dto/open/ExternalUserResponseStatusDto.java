package cn.hanyi.survey.dto.open;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * 验证答题有效性
 */
@Setter
@Getter
public class ExternalUserResponseStatusDto extends BaseDTO {
    @Schema(description = "外部客户Id")
    private String externalUserId;
    @Schema(description = "问卷Id", required = true)
    @Size(max = 5, message = "surveyIds长度限制5")
    private List<Long> surveyIds;
}
