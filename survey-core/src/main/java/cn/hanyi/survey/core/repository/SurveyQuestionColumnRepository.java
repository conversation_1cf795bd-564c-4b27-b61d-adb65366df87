package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.SurveyQuestionColumn;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SurveyQuestionColumnRepository extends ResourceRepository<SurveyQuestionColumn, Long> {

    List<SurveyQuestionColumn> findByQuestionIdOrderBySequenceAsc(Long id);

    void deleteByQuestionIdAndIdNotIn(Long questionId, List<Long> ids);
}
