package cn.hanyi.survey.core.dto.ext;

import cn.hanyi.survey.core.entity.SurveyGroup;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;


@Getter
@Setter
public class SurveyGroupExtDto extends BaseEntityDTO<SurveyGroup> {

    public SurveyGroupExtDto() {
    }

    public SurveyGroupExtDto(SurveyGroup entity) {
        super(entity);
    }

    @Schema(description = "创建人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;

    @Schema(description = "修改人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser editorUser;

    @Schema(description = "问卷数")
    @JsonView(ResourceViews.Basic.class)
    private int countSurvey;

    @Schema(description = "问卷")
    @JsonView(ResourceViews.Basic.class)
    private List<SimpleSurvey> surveys;

}
