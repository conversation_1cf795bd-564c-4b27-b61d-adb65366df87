package cn.hanyi.survey.core.utilis;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.SneakyThrows;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;


/**
 * AES-256-ECB加密模式，AES/ECB/PKCS5Padding。
 * key 32位不足自动补0
 *
 * <AUTHOR>
 */
public class AESUtils {


    private static int keyLength = 32;

    private static final String APIKEY = "5xos2BUyxnEwtdTWh3";
    private static final String APISECRET = "Mh0A72FJ6fwnvz6oO2Jpk000ZBeN4u7H";

    private static String completeKey(String sKey) throws Exception {
        if (sKey == null) {
            throw new Exception("Key为空null");
        }

        StringBuilder key = new StringBuilder();
        for (int i = 0; i < keyLength - sKey.length(); i++) {
            key.append("0");
        }

        return sKey + key;
    }

    public static String encrypt(String key, String content) throws Exception {
        key = completeKey(key);
        byte[] raw = key.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(content.getBytes("utf-8"));
        return Base64.encodeBase64URLSafeString(encrypted);
    }

    public static String decrypt(String key, String content) throws Exception {
        key = completeKey(key);
        byte[] raw = key.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        byte[] encrypted = Base64.decodeBase64(content);
        try {
            byte[] original = cipher.doFinal(encrypted);
            return new String(original, "utf-8");
        } catch (Exception e) {
            throw new SurveyErrorException(SurveyErrorCode.PARAMS_DECRYPT_FAILED);
        }
    }

    @SneakyThrows
    public static void main(String[] args) {
        Map<String,Object> customerParamMap = new HashMap<>();
        customerParamMap.put("orderNum","20240621669557112816349191");
        customerParamMap.put("merchant","10");
        String url = getQuestionnaireUrl("6490639012194304","1001699","","","",customerParamMap);
        System.out.println(url);

        System.out.println(encrypt(APISECRET,"20240621669557112816349191"));
        System.out.println(decrypt(APISECRET,"JbvSfaw6mUBYF5mF7FL1U9yJRZH3uZtdbzDS29U8agA"));
    }

    public static String getQuestionnaireUrl(String sid, String externalUserId, String customerId, String departmentId, String departmentCode, Map<String, Object> customerParamMap) throws Exception {

        String urlPrefix =  "https://www.xmplus.cn/lite/" + sid + "?__t=";
        StringBuilder urlBuilder = new StringBuilder();
        urlBuilder.append("externalUserId=").append("@").append(AESUtils.encrypt(APISECRET, externalUserId)).append("@");
        if (StringUtils.isNotEmpty(customerId)) {
            urlBuilder.append("&customerId=").append("@").append(AESUtils.encrypt(APISECRET, customerId)).append("@");
        }
        if (StringUtils.isNotEmpty(departmentId)) {
            urlBuilder.append("&departmentId=").append("@").append(AESUtils.encrypt(APISECRET, departmentId)).append("@");
        }
        if (StringUtils.isNotEmpty(departmentCode)) {
            urlBuilder.append("&departmentCode=").append("@").append(AESUtils.encrypt(APISECRET, departmentCode)).append("@");
        }
        if (customerParamMap != null && !customerParamMap.isEmpty()) {
            Set<String> keySet = customerParamMap.keySet();
            for (String key : keySet) {
                urlBuilder.append("&_").append(key).append("=").append("@").append(AESUtils.encrypt(APISECRET, (String) customerParamMap.get(key))).append("@");
                //urlBuilder.append("&_").append(key).append("=").append(customerParamMap.get(key));
            }
        }
        System.out.println(urlBuilder);
        String url = urlPrefix + java.util.Base64.getEncoder().encodeToString(urlBuilder.toString().replaceAll("\r|\n", "").getBytes("UTF-8"));
        return url;
    }

}