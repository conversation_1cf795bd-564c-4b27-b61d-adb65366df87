package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.CellTextLabel;
import cn.hanyi.survey.core.dto.SurveyFileUploadDto;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.utils.JsonHelper;
import org.springframework.util.Assert;

import javax.persistence.*;
import java.util.*;
import java.util.stream.Collectors;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_response_cell")
@DtoClass(includeAllFields = true)
public class SurveyResponseCell extends BaseEntity {

    @Column(name = "s_id")
    private Long surveyId;

    @Column(name = "q_id")
    private Long questionId;

    @Column(name = "r_id")
    private Long responseId;

    @Enumerated
    private QuestionType type;

    @Column(name = "i_val")
    private Integer intValue;

    @Column(name = "d_val")
    private Double doubleValue;

    @Column(name = "s_val")
    private String strValue;

    @Column(name = "j_val")
    @Convert(converter = HashMapConverter.class)
    private Map<String, Object> jsonValue;

    @Column(name = "comment_val")
    private String commentValue;

    @Column(name = "t_val")
    private Long dateValue;

    @Column(name = "tags")
    private String tags;

    @Column(name = "cell_score")
    private Integer cellScore;

    @Deprecated(since = "1.10.2")
    @Column(name = "flag")
    @Schema(description = "文本题时 0-POSITIVE, 1-NEGATIVE 目前体验指标sql查询时用到")
    private Integer flag;

    @Column(name = "text_label")
    @Schema(description = "文本题时 0-POSITIVE, 1-NEGATIVE 目前体验指标sql查询时用到, {sentiment: 0, classification: 'xxx'}")
    private String textLabel;

    @Transient
    private String qName;


    public String getTextLabel() {
        if (Strings.isNullOrEmpty(textLabel) && flag != null) {
            return JsonHelper.toJson(new CellTextLabel(flag, null));
        }
        return textLabel;
    }

    /**
     * 先比较，如果内容不同，则把目标内容复制过来
     */
    public boolean compareAndCopyFrom(SurveyResponseCell other) {
        boolean same = Objects.equals(getIntValue(), other.getIntValue())
                && Objects.equals(getDoubleValue(), other.getDoubleValue())
                && Objects.equals(getStrValue(), other.getStrValue())
                && compareMap(getJsonValue(), other.getJsonValue())
                && Objects.equals(getCommentValue(), other.getCommentValue())
                && Objects.equals(getDateValue(), other.getDateValue())
                && Objects.equals(getTags(), other.getTags())
                && Objects.equals(getCellScore(), other.getCellScore());
        if (!same) {
            setIntValue(other.getIntValue());
            setDoubleValue(other.getDoubleValue());
            setStrValue(other.getStrValue());
            setJsonValue(other.getJsonValue());
            setCommentValue(other.getCommentValue());
            setDateValue(other.getDateValue());
            setTags(other.getTags());
            setCellScore(other.getCellScore());
        }
        return same;
    }

    private static boolean compareMap(Map<String, Object> o1, Map<String, Object> o2) {
        if (o1 == null && o2 == null) {
            return true;
        } else if (o1 == null || o2 == null || o1.size() != o2.size()) {
            return false;
        }
        return Maps.difference(o1, o2).areEqual();
    }

    public SurveyResponseCell(Long surveyId, Long responseId, Long questionId, QuestionType questionType, Object value) {
        this.surveyId = surveyId;
        this.questionId = questionId;
        this.responseId = responseId;
        setType(questionType);
        setValue(value);
    }

    public SurveyResponseCell(Survey survey, SurveyQuestion question, SurveyResponse response, Object value) {
        Assert.notNull(question, "invalid question");
        this.surveyId = survey.getId();
        this.questionId = question.getId();
        this.responseId = response.getId();
        setType(question.getType());

        setValue(question, value);
    }


    public SurveyResponseCell(Long surveyId, Long responseId, Long questionId, QuestionType questionType) {
        this.surveyId = surveyId;
        this.questionId = questionId;
        this.responseId = responseId;
        setType(questionType);
    }

    public SurveyResponseCell(Long surveyId, SurveyQuestion question, SurveyResponse response) {
        Assert.notNull(question, "invalid question");

        this.surveyId = surveyId;
        this.questionId = question.getId();
        this.responseId = response.getId();
        setType(question.getType());
    }

    public SurveyResponseCell(Survey survey, SurveyQuestion question, SurveyResponse response) {
        Assert.notNull(question, "invalid question");

        this.surveyId = survey.getId();
        this.questionId = question.getId();
        this.responseId = response.getId();
        setType(question.getType());
    }

    /**
     * 根据不同题型，返回不同数值
     *
     * @return
     */
    public Object getValue() {
        switch (getType()) {
            case SINGLE_CHOICE:
            case COMBOBOX:
            case MOBILE:
            case TEXT:
            case MARK:
            case EMAIL:
            case EVALUATION:
            case EXPERIMENT:
            case SCORE_EVALUATION:
                return getStrValue();
            case MATRIX:
            case SCORE:
            case NPS:
                return getIntValue();
            case NUMBER:
                return getDoubleValue();
            case AREA:
            case DROP_DOWN:
            case MULTIPLE_CHOICES:
            case ORGANIZE:
                List<Object> arrayList = new ArrayList<>();
                if (!Strings.isNullOrEmpty(getStrValue())) {
                    Collections.addAll(arrayList, getStrValue().split(";"));
                }
                return arrayList;
            case MATRIX_SCORE:
            case MATRIX_CHOICE:
            case MATRIX_SLIDER:
            case RANKING:
            case LOCATION:
            case BLANK:
            case MULTIPLE_BLANK:
                return getJsonValue();
            case DATE:
                return getDateValue();
            case FILE:
            case MEDIA:
            case SIGNATURE:
                return JsonHelper.toList(getStrValue(), SurveyFileUploadDto.class);
            default:
                break;
        }
        return null;
    }

    /**
     * 根据不同题型，设置不同数值
     *
     * @param question
     * @param value
     */
    public void setValue(SurveyQuestion question, Object value) {
        setType(question.getType());
        setValue(value);
    }

    public void setValue(Object value) {
        switch (getType()) {
            case SINGLE_CHOICE:
            case COMBOBOX:
            case EVALUATION:
            case TEXT:
            case MARK:
            case MOBILE:
            case EMAIL:
            case EXPERIMENT:
            case SCORE_EVALUATION:
                this.setStrValue(value.toString());
                break;
            case NUMBER:
                this.setDoubleValue(Double.valueOf(value.toString()));
                break;
            case MATRIX:
            case SCORE:
            case NPS:
                this.setIntValue((int) value);
                break;
            case AREA:
            case MULTIPLE_CHOICES:
            case MATRIX_SCORE:
            case MATRIX_CHOICE:
            case DROP_DOWN:
            case MATRIX_SLIDER:
            case ORGANIZE:
            case RANKING:
            case LOCATION:
            case BLANK:
            case MULTIPLE_BLANK:
                if (value instanceof String[]) {
                    String[] saVal = (String[]) value;
                    this.setStrValue(Arrays.stream(saVal).collect(Collectors.joining(";")));
                } else if (value instanceof ArrayList) {
                    this.setStrValue(((List<String>) value).stream().collect(Collectors.joining(";")));
                } else if (value instanceof Map) {
                    this.setJsonValue((Map<String, Object>) value);
                } else {
                    throw new RuntimeException("invalid multiple items");
                }
                break;
            case DATE:
                this.setDateValue(Long.parseLong(value.toString()));
                break;
            case FILE:
            case MEDIA:
            case SIGNATURE:
                this.setStrValue(JsonHelper.toJson(value));
            default:
                break;
        }
    }

    /**
     * 联合实验题答题数据
     */
    @Transient
    private Map<String/*item*/, Map<String/*attr*/, String/*value*/>> experimentValue;
}
