package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.dto.ext.SurveyGroupExtDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Entity
@Getter
@Setter
@Table(name = "survey_group")
@AllArgsConstructor
@NoArgsConstructor
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER, resource = "SURVEY_GROUP")
@DtoClass(superClass = SurveyGroupExtDto.class, includeAllFields = true)
public class SurveyGroup extends EnterpriseOwnerEntity {

    @Schema(description = "顺序")
    @JsonView(ResourceViews.Basic.class)
    private int sequence = 0;

    @Size(max = 200, message = "组名长度超过限制")
    @Schema(description = "组名")
    @JsonView(ResourceViews.Basic.class)
    private String title = "";

    @DtoProperty(ignore = true)
    @Column(name = "editor_id")
    private Long editorId;

    @PrePersist
    public void prePersist() {
        setEditorId(TenantContext.getCurrentUserId());
    }

    @PreUpdate
    public void preUpdate() {
        setEditorId(TenantContext.getCurrentUserId());
    }

}
