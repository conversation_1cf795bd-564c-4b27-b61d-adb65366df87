package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.projection.SimpleSurvey2;
import cn.hanyi.survey.core.projection.SimpleSurvey3;
import cn.hanyi.survey.core.projection.SimpleSurveyWithGroup;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface SurveyRepository extends ResourceRepository<Survey, Long> {
    boolean existsByOrgId(Long org_id);

    SimpleSurvey findSimpleById(Long id);

    SimpleSurvey2 findSimple2ById(Long id);

    SimpleSurvey3 findSimple3ById(Long id);

    SimpleSurvey findSimpleBySurveyCode(String surveyCode);

    Optional<Survey> findByIdOrSurveyCode(Long id, String surveyCode);

    List<SimpleSurvey> findAllBy(Sort sort);

	List<SimpleSurvey> findAllByGroupId(Long groupId, Sort sort);

    Optional<Survey> findOneBySurveyCode(String surveyCode);

    Optional<Survey> findFirstBySurveyCodeAndOrgId(String surveyCode, Long orgId);

	Optional<Survey> getOneById(Long id);

	Optional<Survey> findFirstByGroupIdOrderByModifyTimeDesc(Long groupId);

	@Query(
			nativeQuery = true,
			value = "select * from survey where id=?1"
	)
	Optional<Survey> getOneByIdSql(Long id);

	@Query(nativeQuery = true,
			value = "SELECT s.* FROM survey s INNER JOIN survey_verify sv\n" +
					"ON s.id = sv.s_id and s.verify_version = sv.version \n" +
					"WHERE sv.s_id = ?1 and sv.user_id = ?2")
	Survey getOneVerifySurvey(Long sid, Long userid);

	List<SimpleSurveyWithGroup> findBy(Sort createTime);

	@Query(nativeQuery = true,
			value = "select * from survey where deleted = 0 and id > :id order by id asc limit :page, :limit ")
	List<Survey> findAllSurvey(Long id, int page, int limit);

	boolean existsByIdAndOrgId(Long id, Long orgId);

	@Transactional
	@Modifying
	@Query("update Survey s set s.userId = ?1 where s.orgId = ?2 and s.id in ?3")
	int updateUserIdByOrgIdAndIdIn(Long userId, Long orgId, Collection<Long> ids);

	List<SimpleSurvey> findByIdIn(Collection<Long> ids);

}
