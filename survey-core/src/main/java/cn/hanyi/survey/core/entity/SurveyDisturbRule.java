package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.disturb.DisturbMethod;
import cn.hanyi.survey.core.constant.disturb.DisturbType;
import cn.hanyi.survey.core.dto.ext.SurveyDisturbRuleExtDto;
import cn.hanyi.survey.core.dto.survey.SurveyDisturbFilterDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.converter.LongListConverter;
import org.befun.core.converter.StringListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/12/30 10:36
 */
@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
//@EntityQueryable({"sids","titles"})
@Table(name = "survey_disturb_rule")
@DtoClass(includeAllFields = true, superClass = SurveyDisturbRuleExtDto.class)
public class SurveyDisturbRule extends EnterpriseEntity {
    private static final long serialVersionUID = 6529685098267757690L;

    @Schema(description = "用户id")
    @Column(name = "user_id")
    protected Long userId;

    @Size(max = 20, message = "名称超出20长度")
    @Schema(description = "规则名称", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String name = "";

    @Convert(converter = LongListConverter.class)
    @Schema(description = "s_id list", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "sids")
    private List<Long> sids = new ArrayList<>();

    @Size(max = 2000, message = "问卷标题长度超多限制")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷标题", required = false)
    @Column(name = "titles")
    private String titles;

    @Convert(converter = StringListConverter.class)
    @Schema(description = "免打扰渠道", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "channels")
    private List<String> channels = new ArrayList<>();

    @Schema(description = "免打扰类型", required = true)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "disturb_type")
    private DisturbType disturbType = DisturbType.DATE;

    @Schema(description = "免打扰方式", required = true)
    @JsonIgnore
    @Column(name = "disturb_method")
    private DisturbMethod disturbMethod = DisturbMethod.OPEN;

    @Schema(description = "天数", required = true)
    @JsonIgnore
    @Column(name = "disturb_days")
    private Integer disturbDays = 1;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "disturb_filter")
    @Type(type = JsonColumn.TYPE)
    private List<SurveyDisturbFilterDto> disturbFilter;

    @Schema(description = "启用状态 0:未设置，1:回收中", required = true)
    @JsonView(ResourceViews.Basic.class)
    private Boolean status = false;

    @Schema(description = "编辑者id")
    @Column(name = "editor_id")
    protected Long editorId;

    @PrePersist
    public void prePersist() {
        setEditorId(TenantContext.getCurrentUserId());
    }

    @PreUpdate
    public void preUpdate() {
        setEditorId(TenantContext.getCurrentUserId());
    }

}
