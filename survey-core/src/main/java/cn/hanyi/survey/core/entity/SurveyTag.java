package cn.hanyi.survey.core.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.RootAware;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "survey_tag")
@DtoClass(includeAllFields = true)
public class SurveyTag extends BaseEntity implements RootAware<Survey> {
    private static final long serialVersionUID = 6529685098267757690L;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "s_id")
    @DtoProperty(ignore = true)
    @JsonIgnore
    private Survey survey;

    @Size(max = 10, message = "标签名称超出10个字符")
    @Schema(description = "答卷标签名称", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String name = "";

    @Size(max = 2000, message = "SEL表达式长度超多限制")
    @Schema(description = "SEL表达式", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String expression = "";


    @Override
    @JsonIgnore
    public Survey getRoot() {
        return survey;
    }

}
