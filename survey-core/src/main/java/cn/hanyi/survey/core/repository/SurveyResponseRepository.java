package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.dto.SurveyResponseFinishTimeOnly;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.projection.SimpleResponse;
import cn.hanyi.survey.core.projection.SimpleResponse2;
import cn.hanyi.survey.core.projection.SimpleResponse3;
import cn.hanyi.survey.core.projection.SimpleResponse4;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.*;

@Repository
public interface SurveyResponseRepository extends ResourceRepository<SurveyResponse, Long> {
    // 确保只能找到一个
    Optional<SurveyResponse> findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(Long surveyId, String clientId);

    Optional<List<SurveyResponse>> findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndStatusInOrderByCreateTimeAsc(Long surveyId, List<ResponseStatus> statusList);

    Optional<List<SurveyResponse>> findDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndStatusInOrderByCreateTimeAsc(Long surveyId, List<ResponseStatus> statusList, Pageable pageable);

    int countDistinctClientIdBySurveyIdAndIsCompletedIsTrueAndStatusInOrderByCreateTimeAsc(Long surveyId, List<ResponseStatus> statusList);

    @Modifying
    @Query("update SurveyResponse set deleted =1 where surveyId=?1")
    void deleteBySurveyId(Long surveyId);

    long countBySurveyIdAndStatus(Long surveyId, ResponseStatus status);

    @Query(nativeQuery = true, value = "SELECT count(*)  FROM survey_response FORCE INDEX ( s_id_status_delete )  WHERE ( deleted = 0 ) and  s_id = ?1  AND STATUS = ?2")
    long countBySurveyIdAndStatusForceIndex(Long surveyId, int status);

    long countBySurveyIdAndStatusAndCollectorMethodNot(Long surveyId, ResponseStatus status, SurveyCollectorMethod method);

    long countBySurveyIdAndStatusInAndCollectorMethod(Long surveyId, List<ResponseStatus> statusList, SurveyCollectorMethod method);


//    @Query(
//            value = "select s_id surveyId, count(id) total from  survey_response sr FORCE INDEX(s_id_status_delete)  where sr.s_id in :ids and sr.status=:status and sr.deleted=0 group by s_id",
//            nativeQuery = true)
//
//    List<Map<String, Object>> numRespSurveyIdsAndStatus(@Param(value = "ids") Set<Long> ids, @Param(value = "status") int status);

    @Query(nativeQuery = true, value = "select id surveyId, response_finish_num total from  survey  where id in :ids")
    List<Map<String, Object>> numRespSurveyIdsAndStatus(@Param(value = "ids") Set<Long> ids);

    Long countBySurveyIdAndDepartmentId(Long surveyId, long departmentId);

    Long countBySurveyIdAndDepartmentIdAndStatus(Long surveyId, long departmentId, ResponseStatus status);


    List<SurveyResponse> findBySurveyIdAndStatus(Long surveyId, ResponseStatus status, Pageable pageable);

    List<SurveyResponse> findBySurveyIdAndStatusInAndCollectorMethod(Long surveyId, List<ResponseStatus> statusList, SurveyCollectorMethod method, Pageable pageable);

    List<SurveyResponse> findBySurveyIdAndStatusAndCollectorMethodNot(Long surveyId, ResponseStatus status, SurveyCollectorMethod method, Pageable pageable);

    List<SurveyResponse> findAllByIdIn(Iterable<Long> ids);

    SurveyResponseFinishTimeOnly findTopBySurveyIdAndStatusOrderByFinishTimeDesc(Long surveyId, ResponseStatus status);

    List<SurveyResponse> findAllBySurveyIdAndChannelId(Long surveyId, Long channelId);

    @Modifying
    @Transactional
    void deleteAllByIdIn(Iterable<Long> ids);

    Optional<SurveyResponse> findOneBySurveyIdAndOpenidAndIsCompletedIsTrue(Long surveyId, String openid);

    Optional<SurveyResponse> findOneBySurveyIdAndId(Long surveyId, Long id);

    @Modifying
    @Query("update SurveyResponse sr set sr.status = :status where sr.id in :ids")
    void updateStatusByIdIn(@Param(value = "status") ResponseStatus status, @Param(value = "ids") List<Long> ids);

    Optional<SurveyResponse> findFirstBySurveyIdAndIsCompletedIsTrueAndIp(Long surveyId, String ip);

    Optional<SurveyResponse> findFirstBySurveyIdAndChannelIdAndIsCompletedIsTrueAndIp(Long surveyId, Long channelId, String ip);

    Long countBySurveyIdAndChannelIdAndStatus(Long surveyId, Long channelId, ResponseStatus status);

    List<SimpleResponse> findSimpleByIdIn(Iterable<Long> ids);

    // 客户端填答缓存时用到下面的方法

    // count survey-status 上面有了
    // long countBySurveyIdAndStatus(Long surveyId, ResponseStatus status);
    // list survey-status
    // exists survey-ip

    List<SimpleResponse> findSimpleBySurveyIdAndIsCompletedIsTrueAndIp(Long surveyId, String ip, Pageable pageable);

    List<SimpleResponse> findSimpleBySurveyIdAndIsCompletedIsTrue(Long surveyId, Pageable pageable);

    List<SimpleResponse> findSimpleBySurveyIdAndChannelIdAndIsCompletedIsTrue(Long surveyId, Long channelId, Pageable pageable);
    List<SimpleResponse> findSimpleBySurveyIdAndChannelIdAndIsCompletedIsTrueAndIp(Long surveyId, Long channelId, String ip, Pageable pageable);
    List<SimpleResponse> findSimpleBySurveyIdAndChannelIdAndIsCompletedIsTrueAndOpenid(Long surveyId, Long channelId, String openid, Pageable pageable);

    Optional<SurveyResponse> findFirstBySurveyIdAndStatusOrderByIdDesc(Long surveyId, ResponseStatus status);

    // download list
    List<SurveyResponse> findBySurveyIdAndStatusOrderByIdAsc(Long surveyId, ResponseStatus status);

    List<SurveyResponse> findBySurveyIdAndStatusAndIdGreaterThan(Long surveyId, ResponseStatus status, Long id, Pageable pageable);

    List<SurveyResponse> findBySurveyIdAndStatusInAndIdGreaterThan(Long surveyId, List<ResponseStatus> statusList, Long id, Pageable pageable);

    List<SimpleResponse2> findSimpleBySurveyIdAndStatusAndIdGreaterThan(Long surveyId, ResponseStatus status, Long id, Pageable pageable);

    List<SurveyResponse> findBySurveyIdAndIdIn(Long surveyId, Collection<Long> ids);


    List<SurveyResponse> findBySurveyId(Long surveyId);

    boolean existsBySurveyIdAndStatusInAndIsCompletedIsTrue(Long surveyId, List<ResponseStatus> status);

    SimpleResponse2 findSimple2ById(Long responseId);

    List<SurveyResponse> findBySurveyIdAndStatusAndIdBetween(Long surveyId, ResponseStatus status, Long idStart, Long idEnd, Pageable pageable);

    List<SimpleResponse3> findBySurveyIdAndChannelIdAndExternalUserIdIn(Long surveyId, Long channelId,Collection<String> externalUserIds);

    List<SimpleResponse4> findBySurveyIdInAndExternalUserId(Collection<Long> surveyIds, String externalUserId);

}
