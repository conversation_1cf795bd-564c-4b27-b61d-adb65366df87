package cn.hanyi.survey.core.service;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.SurveyLinkDto;
import cn.hanyi.survey.core.dto.question.DynamicQuestionDto;
import cn.hanyi.survey.core.dto.survey.CreateShortLinkDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.projection.SimpleDynamicItemQuestion;
import cn.hanyi.survey.core.projection.SimpleSurvey;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.annotations.AddTenantContext;
import org.befun.auth.service.DepartmentService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.extension.entity.Link;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

import static cn.hanyi.survey.core.constant.error.SurveyErrorCode.CHANNEL_STATUS_ERROR;
import static org.befun.auth.constant.TenantContext.TenantFields.*;

@Service
public class SurveyLinkService {

    @Autowired
    private SurveyRepository repository;
    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;
    @Autowired
    private LinkService linkService;
    @Autowired
    private DepartmentService departmentService;


    @AddTenantContext({OrgId, ISAdmin, RoleIds, SubDepartmentId})
    public SurveyLinkDto createShortLink(CreateShortLinkDto dto, Integer source, Boolean checkExist) {
        SimpleSurvey survey = null;
        Long surveyId;
        String surveyCode;
        if ((surveyId = dto.getSurveyId()) != null && surveyId > 0) {
            survey = repository.findSimpleById(surveyId);
        } else if (StringUtils.isNotEmpty(surveyCode = dto.getSurveyCode())) {
            survey = repository.findSimpleBySurveyCode(surveyCode);
        } else {
            throw new BadRequestException("问卷id和问卷编号必须要有一个");
        }
        if (survey == null) {
            throw new BadRequestException("抱歉，您访问的问卷不存在");
        }

        if (SurveyStatus.COLLECTING != survey.getStatus()) {
            throw new SurveyErrorException(CHANNEL_STATUS_ERROR);
        }

        surveyId = survey.getId();

        checkQuestionDynamicItems(surveyId, dto.getDynamicQuestionItems());
        fillDepartmentInfo(dto, TenantContext.getCurrentTenant());
        if (StringUtils.isEmpty(dto.getClientId())) {
            dto.setClientId(UUID.randomUUID().toString());
        }
        SurveyLinkDto r = new SurveyLinkDto();
        Link link = linkService.createLink(surveyId, source, dto.buildMap(), checkExist);
        r.setOriginUrl(linkService.toOriginUrl(link));
        r.setShortUrl(linkService.toShortUrl(link));
        r.setShortCode(linkService.toShortCode(link));
        return r;
    }

    public void checkQuestionDynamicItems(Long surveyId, List<DynamicQuestionDto> dynamicQuestionItems) {
        Survey survey = new Survey();
        survey.setId(surveyId);
        List<SimpleDynamicItemQuestion> list = surveyQuestionRepository.findBySurveyAndIsDynamicItem(survey, true);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        if (CollectionUtils.isEmpty(dynamicQuestionItems)) {
            throw new SurveyErrorException(SurveyErrorCode.CREATE_SHORT_LINK_MISSION_DYNAMIC_ITEM_ERROR);
        }
        Map<String, List<SurveyQuestionItem>> itemMap = new HashMap<>();
        dynamicQuestionItems.forEach(i -> itemMap.put(i.getCode(), i.convertToItems()));
        list.forEach(i -> {
            List<SurveyQuestionItem> items = itemMap.get(i.getCode());
            if (CollectionUtils.isEmpty(items)) {
                throw new SurveyErrorException(SurveyErrorCode.CREATE_SHORT_LINK_MISSION_DYNAMIC_ITEM_ERROR);
            }
        });
    }

    private void fillDepartmentInfo(CreateShortLinkDto dto ,Long orgId) {
         Optional.ofNullable(departmentService.getByIdOrCodeOrRoot(orgId, dto.getDepartmentId(), dto.getDepartmentCode(), false)).ifPresent(i -> {
             dto.setDepartmentName(i.getTitle());
         });
    }
}
