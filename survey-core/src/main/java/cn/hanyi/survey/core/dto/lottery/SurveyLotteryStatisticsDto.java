package cn.hanyi.survey.core.dto.lottery;

import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/10/21 17:08:02
 */
@Setter
@Getter
public class SurveyLotteryStatisticsDto {
    private Long id;
    private Long responseId;
    private Long sequence;
    private String parameters;
    private LotteryPrizeType type;
    private String prizeName;
    private PrizeSendStatus status;
    private String prizeCode;
    private String name;
    private String phone;
    private String province;
    private String city;
    private String district;
    private String address;
    private String lotteryName;
    private Date createTime;
    private Date sendTime;
    private String sendName;
    private Integer amount;
    private Boolean isVerify = false;
}
