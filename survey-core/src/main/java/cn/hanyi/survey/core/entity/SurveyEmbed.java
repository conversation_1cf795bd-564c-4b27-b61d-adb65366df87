package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.survey.*;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import javax.persistence.Transient;
import java.io.Serializable;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
public class SurveyEmbed implements Serializable {
    private static final long serialVersionUID = 6529685098267757690L;

    @Column(name = "inject_type")
    @JsonView(ResourceViews.Basic.class)
    private String injectType = "1";

    @Column(name = "embed_height")
    @JsonView(ResourceViews.Basic.class)
    private String embedHeight = "50%";

    @Column(name = "embed_padding_width")
    @JsonView(ResourceViews.Basic.class)
    private String embedPaddingWidth = "0px";

    @Column(name = "embed_pc_width")
    @JsonView(ResourceViews.Basic.class)
    private Integer embedPcWidth = 450;

    @Column(name = "embed_height_mode")
    @JsonView(ResourceViews.Basic.class)
    private EmbedHeightMode embedHeightMode = EmbedHeightMode.AUTO;

    @Column(name = "embed_align")
    @JsonView(ResourceViews.Basic.class)
    private EmbedPosition embedAlign = EmbedPosition.CENTER;

    @Column(name = "embed_vertical_align")
    @JsonView(ResourceViews.Basic.class)
    private EmbedPosition embedVerticalAlign = EmbedPosition.BOTTOM;

    @Column(name = "embed_type")
    @JsonView(ResourceViews.Basic.class)
    private String embedType = "1";

    @Column(name = "embed_delay")
    @JsonView(ResourceViews.Basic.class)
    private Integer embedDelay = 0;

    @Column(name = "gap_time")
    @JsonView(ResourceViews.Basic.class)
    private Integer gapTime = 0;

    @Column(name = "gap_type")
    @JsonView(ResourceViews.Basic.class)
    private EmbedGapType gapType = EmbedGapType.DAY;

//    @Column(name = "gap_check")
//    @JsonView(ResourceViews.Basic.class)
//    private Boolean gapCheck = false;

//    @Column(name = "gap_first_check")
//    @JsonView(ResourceViews.Basic.class)
//    private Boolean gapFirstCheck = false;

    @Column(name = "gap_day")
    @JsonView(ResourceViews.Basic.class)
    private Integer gapDay = 0;

//    @Column(name = "gap_custom_check")
//    @JsonView(ResourceViews.Basic.class)
//    private Boolean gapCustomCheck = false;

    @Column(name = "gap_custom_time")
    @JsonView(ResourceViews.Basic.class)
    private Integer gapCustomTime = 1;

    @Column(name = "gap_custom_type")
    @JsonView(ResourceViews.Basic.class)
    private EmbedGapType gapCustomType = EmbedGapType.DAY;


    @Schema(description = "显示频率")
    @Column(name = "frequency")
    @JsonView(ResourceViews.Basic.class)
    private EmbedFrequency embedFrequency = EmbedFrequency.REPEAT;

    @Column(name = "repeat_action")
    @Schema(description = "重复周期行为")
    @JsonView(ResourceViews.Basic.class)
    private EmbedRepeatAction embedRepeatAction = EmbedRepeatAction.REFRESH;

    @Column(name = "once_action")
    @Schema(description = "单次周期行为")
    @JsonView(ResourceViews.Basic.class)
    private EmbedRepeatAction embedOnceAction = EmbedRepeatAction.NEVER;

    @Transient
    @Schema(description = "问卷状态")
    @JsonView(ResourceViews.Basic.class)
    private SurveyStatus surveyStatus;

    @Column(name = "embed_back_ground")
    @JsonView(ResourceViews.Basic.class)
    private Boolean embedBackGround = false;


    public EmbedFrequency getEmbedFrequency() {
        return embedFrequency == null ? EmbedFrequency.REPEAT : embedFrequency;
    }

    public EmbedRepeatAction getEmbedRepeatAction() {
        return embedRepeatAction == null ? EmbedRepeatAction.NEVER : embedRepeatAction;
    }

    public EmbedRepeatAction getEmbedOnceAction() {
        return embedOnceAction == null ? EmbedRepeatAction.NEVER : embedOnceAction;
    }
}
