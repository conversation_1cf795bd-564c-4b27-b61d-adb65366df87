package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.constant.lottery.SurveyLotteryStatus;
import cn.hanyi.survey.core.dto.lottery.SurveyLotteryExtDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 11:05:45
 */
@Entity
@Setter
@Getter
@DtoClass(includeAllFields = true, superClass = SurveyLotteryExtDto.class)
@Table(name = "survey_lottery")
public class SurveyLottery extends BaseEntity {

    @Schema(description = "问卷id")
    @Column(name = "s_id")
    @JsonView(ResourceViews.Basic.class)
    private Long sid;

    @Schema(description = "抽奖渠道名称")
    @Column(name = "lottery_name")
    @JsonView(ResourceViews.Basic.class)
    @Size(max = 100, message = "抽奖活动名称长度不能大于100")
    private String lotteryName;

    @Schema(description = "抽奖渠道类型")
    @Column(name = "lottery_type")
    @JsonView(ResourceViews.Basic.class)
    private LotteryType lotteryType = LotteryType.NINE_PRIZE;

    @Schema(description = "抽奖渠道状态")
    @Enumerated
    @Column(name = "status")
    @JsonView(ResourceViews.Basic.class)
    private SurveyLotteryStatus status = SurveyLotteryStatus.PAUSING;

    @Schema(description = "抽奖渠道创建人")
    @JsonView(ResourceViews.Basic.class)
    private Long creator;

    @Schema(description = "活动中奖总数量")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "total_winner_num")
    private Integer totalWinnerNum = 0;

    @Schema(description = "奖品")
    @OneToMany(mappedBy = "lottery", cascade = {CascadeType.ALL}, fetch = FetchType.LAZY, orphanRemoval = true)
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(type = SurveyLotteryPrizeDto.class)
    private List<SurveyLotteryPrize> prizes = new ArrayList<>();

    @Schema(description = "抽奖期限")
    @Column(name = "is_time_limit", columnDefinition = "bit(1) default 0")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isTimeLimit = false;

    @Schema(description = "抽奖期限开始时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time")
    @JsonView(ResourceViews.Basic.class)
    private Date startTime;

    @Schema(description = "抽奖期限结束时间")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "end_time")
    @JsonView(ResourceViews.Basic.class)
    private Date endTime;

    @Schema(description = "抽奖条件-正常结束后可参与")
    @Column(name = "is_complete", columnDefinition = "bit(1) default 1")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isComplete = true;

    @Schema(description = "是否打开每日奖品限额")
    @Column(name = "is_limit", columnDefinition = "bit(1) default 0")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isLimit = false;

    @Schema(description = "每日奖品限额")
    @Column(name = "prize_limit")
    @JsonView(ResourceViews.Basic.class)
    private Integer prizeLimit;

    @Schema(description = "发奖人姓名")
    @Column(name = "send_lottery_name")
    @JsonView(ResourceViews.Basic.class)
    private String sendLotteryName;

    @Schema(description = "兑奖说明开关")
    @Column(name = "is_lottery_desc", columnDefinition = "bit(1) default 1")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isLotteryDesc = true;

    @Schema(description = "发奖人联系方式")
    @Column(name = "send_lottery_contact")
    @JsonView(ResourceViews.Basic.class)
    private String sendLotteryContact;

    @Schema(description = "抽奖说明")
    @Column(name = "lottery_desc")
    @JsonView(ResourceViews.Basic.class)
    @Size(max = 300, message = "抽奖说明长度大于300")
    private String lotteryDesc;

    @Schema(description = "红包渠道是否支付")
    @Column(name = "is_pay", columnDefinition = "bit(1) default 0")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isPay = false;

    @Schema(description = "是否审核后发放")
    @Column(name = "is_verify", columnDefinition = "bit(1) default 1")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isVerify = false;

    @Schema(description = "微信抽奖渠道支付订单id")
    @Column(name = "order_id")
    @JsonView(ResourceViews.Basic.class)
    private Long orderId;

    public Integer getTotalNumber() {
        Integer totalNumber = prizes.stream().filter(x -> x.getNumber() != null).mapToInt(SurveyLotteryPrize::getNumber).sum();
        return totalNumber;
    }

/*    public Integer getTotalWinnerNum() {
        Integer totalWinnerNumber = prizes.stream().filter(x -> x.getWinnerNum() != null && x.getIsPrize()).mapToInt(SurveyLotteryPrize::getWinnerNum).sum();
        return totalWinnerNumber;
    }*/

    public void copyProperties(SurveyLottery lottery, SurveyLotteryDto lotteryDto) {
        List<SurveyLotteryPrizeDto> prizes = new ArrayList<>();
        for (SurveyLotteryPrize prize : lottery.getPrizes()) {
            SurveyLotteryPrizeDto dto = new SurveyLotteryPrizeDto();
            dto.setId(prize.getId());
            dto.setIsAddress(prize.getIsAddress());
            dto.setAddress(prize.getAddress());
            dto.setIsPrize(prize.getIsPrize());
            dto.setImage(prize.getImage());
            dto.setPrizeName(prize.getPrizeName());
            dto.setSequence(prize.getSequence());
            dto.setType(prize.getType());
            dto.setIsImporveAwardInfo(prize.getIsImporveAwardInfo() == null || prize.getImporveAwardInfo());
            prizes.add(dto);
        }
        lotteryDto.setPrizes(prizes);
        lotteryDto.setId(lottery.getId());
        lotteryDto.setIsLotteryDesc(lottery.getIsLotteryDesc());
        lotteryDto.setLotteryType(lottery.getLotteryType());
        lotteryDto.setLotteryName(lottery.getLotteryName());
        lotteryDto.setIsComplete(lottery.getIsComplete());
        lotteryDto.setSendLotteryName(lottery.getSendLotteryName());
        lotteryDto.setSendLotteryContact(lottery.getSendLotteryContact());
        lotteryDto.setLotteryDesc(lottery.getLotteryDesc());
    }
}
















