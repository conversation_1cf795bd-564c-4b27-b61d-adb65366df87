package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Enumerated;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/20 11:31:01
 */
@Entity
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "survey_lottery_prize_winner")
@DtoClass(includeAllFields = true)
public class SurveyLotteryPrizeWinner extends BaseEntity {

    @Schema(description = "问卷id")
    @Column(name = "survey_id")
    private Long surveyId;

    @Schema(description = "奖品渠道id")
    @Column(name = "lottery_id")
    private Long lotteryId;

    @Schema(description = "奖品id")
    @Column(name = "prize_id")
    private Long prizeId;

    @Schema(description = "答卷id")
    @Column(name = "response_id")
    private Long responseId;

    @Schema(description = "奖品类型")
    @Enumerated
    private LotteryPrizeType type;

    @Schema(description = "奖品名称")
    @Column(name = "prize_name")
    private String prizeName;

    @Schema(description = "红包中奖金额")
    @Column(name = "amount")
    private Integer amount;

    @Schema(description = "奖品兑换码")
    @Column(name = "prize_code")
    private String prizeCode;

    @Schema(description = "收货人")
    private String name;

    @Schema(description = "手机号码")
    private String phone;

    @Schema(description = "收获地址：省")
    private String province;

    @Schema(description = "收获地址：市")
    private String city;

    @Schema(description = "收货地址：区/县")
    private String district;

    @Schema(description = "实物收货详细地址")
    private String address;

    @Schema(description = "奖品发放状态")
    @Enumerated
    private PrizeSendStatus status = PrizeSendStatus.NOT_SEND;

    @Schema(description = "红包是否被领取（是否扫码）")
    @Column(name = "is_received", columnDefinition = "bit(1) default 0")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isReceived = false;

    @Schema(description = "奖品id")
    @Column(name = "send_id")
    private Long sendId;

    @Schema(description = "发放时间")
    @Column(name = "send_time")
    private Date sendTime;

    @Schema(description = "退回时间")
    @Column(name = "refund_time")
    private Date refundTime;

    @Schema(description = "openid")
    @Column(name = "openid")
    private String openid;
}
















