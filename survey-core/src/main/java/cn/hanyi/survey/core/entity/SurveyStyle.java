package cn.hanyi.survey.core.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.io.Serializable;

@Embeddable
@Getter
@Setter
@NoArgsConstructor
public class SurveyStyle implements Serializable {
    private static final long serialVersionUID = 6529685098267757690L;

    @Column(name = "font_size")
    @Schema(description = "字体大小: medium | large | 30px", example = "medium")
    @JsonView(ResourceViews.Detail.class)
    private String fontSize = "medium";

    @Column(name = "main_color")
    @Schema(description = "主色", example = "#3181F6")
    @JsonView(ResourceViews.Detail.class)
    private String mainColor = "#3181F6";

    @Column(name = "title_color")
    @Schema(description = "标题文本颜色", example = "#222222")
    @JsonView(ResourceViews.Detail.class)
    private String titleColor = "#222222";

    @Column(name = "welcoming_color")
    @Schema(description = "欢迎文本颜色", example = "#333333")
    @JsonView(ResourceViews.Detail.class)
    private String welcomingColor = "#333333";

    @Column(name = "question_color")
    @Schema(description = "题干文本颜色", example = "#333333")
    @JsonView(ResourceViews.Detail.class)
    private String questionColor = "#333333";

    @Column(name = "item_color")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "选项颜色", example = "#333333")
    private String itemColor = "#333333";

    @Column(name = "tag_color")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "标签颜色", example = "#666666")
    private String tagColor = "#666666";

    @Column(name = "default_tag_padding")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "默认标签填充", example = "#F5F6F7")
    private String defaultTagPadding = "#F5F6F7";

    @Column(name = "default_tag_stroke")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "默认标签描边", example = "#F5F6F7")
    private String defaultTagStroke = "#F5F6F7";

    @Column(name = "selected_tag_padding")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "选中标签填充", example = "rgba(49,129,246,0.1)")
    private String selectedTagPadding = "rgba(49,129,246,0.1)";

    @Column(name = "selected_tag_stroke")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "选中标签描边", example = "#3181F6")
    private String selectedTagStroke = "#3181F6";

    @Column(name = "enable_custom_button", columnDefinition = "bit(1) default 0")
    @Schema(description = "按钮自定义")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableCustomButton = false;

    @Column(name = "button_border_color")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "默认按钮描边", example = "#234154")
    private String buttonBorderColor = "#234154";

    @Column(name = "button_color")
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "按钮颜色", example = "#3181F6")
    private String buttonColor = "#3181F6";

    @Column(name = "background_color")
    @Schema(description = "背景颜色", example = "#FFFFFF")
    @JsonView(ResourceViews.Detail.class)
    private String backgroundColor = "#FFFFFF";

    @Column(name = "background_opacity")
    @Schema(description = "背景不透明度(0-1)", example = "1.0")
    @JsonView(ResourceViews.Detail.class)
    private Double backgroundOpacity = 1.0;

    @Column(name = "close_btn_color")
    @Schema(description = "关闭图标颜色")
    @JsonView(ResourceViews.Basic.class)
    private String closeBtnColor = "#556976";

    @Column(name = "text_rounded")
    @Schema(description = "标签/文本框圆角")
    @JsonView(ResourceViews.Basic.class)
    private String textRounded = "4px";

    @Column(name = "button_rounded")
    @Schema(description = "按钮圆角")
    @JsonView(ResourceViews.Basic.class)
    private String buttonRounded = "4px";
}
