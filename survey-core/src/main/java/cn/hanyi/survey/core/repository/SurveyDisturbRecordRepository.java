package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.disturb.DisturbMethod;
import cn.hanyi.survey.core.dto.disturb.LastSurveyDisturbRecord;
import cn.hanyi.survey.core.entity.SurveyDisturbRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Optional;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2022/9/26 16:09
 */
@Repository
public interface SurveyDisturbRecordRepository extends ResourceRepository<SurveyDisturbRecord,Long> {

    Optional<SurveyDisturbRecord> findFirstByOrgIdAndSidAndExternalUserIdAndDisturbMethodOrderByIdDesc(Long orgId, Long sid, String externalUserId, DisturbMethod disturbMethod);

    long countByIdGreaterThanAndOrgIdAndSidAndExternalUserIdAndDisturbMethod(Long id, Long orgId, Long sid, String externalUserId, DisturbMethod disturbMethod);

    long countByIdGreaterThanAndOrgIdAndSidInAndExternalUserIdAndDisturbMethod(Long id, Long orgId, Collection<Long> sids, String externalUserId, DisturbMethod disturbMethod);

    LastSurveyDisturbRecord findFirstByOrgIdAndSidIsInAndExternalUserIdAndDisturbMethodOrderByCreateTimeDesc(Long orgId, Collection<Long> sids, String externalUserId, DisturbMethod disturbMethod);

    @Query(nativeQuery = true, value = "select max(id) from survey_disturb_record where org_id=?1 and euid=?2 and s_id in (?3) and disturb_method=?4")
    Long findMaxIdByDisturbMethod(Long orgId, String externalUserId, Collection<Long> sids, int disturbMethod);

}
