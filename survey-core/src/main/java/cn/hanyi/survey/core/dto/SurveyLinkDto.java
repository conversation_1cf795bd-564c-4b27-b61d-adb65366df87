package cn.hanyi.survey.core.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class SurveyLinkDto {

    @JsonView(ResourceViews.Basic.class)
    private String shortCode;
    @JsonView(ResourceViews.Basic.class)
    private String shortUrl;
    @JsonView(ResourceViews.Basic.class)
    private String originUrl;
    @JsonView(ResourceViews.Basic.class)
    private String surveyUrl;


    public SurveyLinkDto() {
    }

    public SurveyLinkDto(String surveyUrl) {
        this.surveyUrl = surveyUrl;
    }
}
