package cn.hanyi.survey.core.repository;


import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.dto.lottery.SimpleSurveyLotteryResult;
import cn.hanyi.survey.core.entity.SurveyLottery;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/20 17:30:15
 */
@Repository
public interface SurveyLotteryRepository extends ResourceRepository<SurveyLottery, Long> {

    List<SurveyLottery> findBySid(Long sid);

    List<SimpleSurveyLotteryResult> findAllBySid(Long sid);

    List<SurveyLottery> findBySid(Long sid, Sort sort);

    Page<SurveyLottery> findBySid(Long sid, Pageable pageable);

    int countBySidAndAndLotteryType(Long sid, LotteryType type);

}
