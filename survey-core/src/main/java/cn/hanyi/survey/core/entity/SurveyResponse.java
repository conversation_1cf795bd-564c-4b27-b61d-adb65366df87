package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.annotation.AESDecryptField;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.dto.SurveyTrackingDataDto;
import cn.hanyi.survey.core.utilis.AESUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.ApiKey;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.converter.ListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
//@EntityListeners({EntityChangeListener.class})
@SQLDelete(sql = "UPDATE survey_response SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
@Table(name = "survey_response")
@DtoClass(includeAllFields = true)
public class SurveyResponse extends EnterpriseEntity {

    @Column(name = "s_id")
    private Long surveyId;

    @Schema(description = "客户中心客户ID")
    @Column(name = "c_id")
    private Long customerId;

    @Size(max = 100, message = "外部用户ID长度超多限制")
    @Schema(description = "外部用户ID")
    @Column(name = "euid")
    private String externalUserId;

    @Size(max = 100, message = "客户端长度超多限制")
    @Schema(description = "客户端ID")
    @Column(name = "client_id")
    private String clientId;

    @Schema(description = "场景ID")
    @Column(name = "scene_id")
    private Long sceneId;

    @Schema(description = "部门ID")
    @Column(name = "department_id")
    private Long departmentId;

    @Schema(description = "部门名称")
    @Column(name = "department_name")
    private String departmentName;

    @Schema(description = "客户名称")
    @Column(name = "customer_name")
    private String customerName;

    @Schema(description = "客户性别")
    @Column(name = "customer_gender")
    private String customerGender;

    @Schema(description = "外部组织编号")
    @Column(name = "department_code")
    private String departmentCode;

    @Schema(description = "外部企业ID")
    @Column(name = "external_company_id")
    private String externalCompanyId;

    @Schema(description = "默认参数")
    @Column(name = "default_pa")
    private String defaultPa;

    @Schema(description = "默认参数")
    @Column(name = "default_pb")
    private String defaultPb;

    @Schema(description = "默认参数")
    @Column(name = "default_pc")
    private String defaultPc;

    @Schema(description = "是否结束")
    @Column(name = "is_completed")
    private Boolean isCompleted = false;

    private String ip;
    @JsonView(ResourceViews.Basic.class)
    private String country = "未知";
    ;
    @JsonView(ResourceViews.Basic.class)
    private String province = "未知";
    ;
    @JsonView(ResourceViews.Basic.class)
    private String city = "未知";

    private String device;
    private String os;
    private String browser;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "duration_seconds")
    @Schema(description = "答题持续时间(秒单位)")
    private Integer durationSeconds;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "total_score")
    @Schema(description = "总得分")
    private Integer totalScore = 0;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "track_id")
    @Schema(description = "跟踪id")
    private String trackId;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答题结束时间")
    @Column(name = "finish_time")
    private Date finishTime;

    @Column(name = "user_agent")
    private String userAgent;

    @Column(name = "parameters")
    @Convert(converter = HashMapConverter.class)
    private Map<String, Object> parameters = new HashMap<>();

    @OneToMany(mappedBy = "responseId", cascade = CascadeType.REMOVE, fetch = FetchType.LAZY)
    private Collection<SurveyResponseCell> cells;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "status")
    @Enumerated
    private ResponseStatus status = ResponseStatus.INIT;

    @Column(name = "connector_method")
    @Enumerated
    @Schema(description = "收集方法", example = "URL")
    private SurveyCollectorMethod collectorMethod = SurveyCollectorMethod.LINK;

    @Schema(description = "渠道ID")
    @Column(name = "channel_id")
    private Long channelId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "sequence")
    @Schema(description = "答卷编号")
    private Long sequence;

    @Schema(description = "微信openid")
    private String openid;

    @JsonIgnore
    @Schema(description = "答卷是否删除")
    private Boolean deleted = false;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "tags")
    @Schema(description = "答卷标签")
    @Convert(converter = ListConverter.class)
    private List<String> tags;


    @JsonView(ResourceViews.Basic.class)
    @Column(name = "addition_data")
    @Schema(description = "附加数据")
    private String additionData;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "lottery_num")
    @Schema(description = "默认抽奖次数")
    private Integer lotteryNum = 1;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信接收人名称")
    private String name;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信接收人手机号")
    private String phone;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "邮件渠道收件人邮箱账号")
    private String email;

    public SurveyResponse(Long orgId, Long surveyId, SurveyTrackingDataDto trackingDataDto, SurveySubmitRequestDto submitDto) {
        this.setOrgId(orgId);
        this.setSurveyId(surveyId);

        // Tracking, UA
        this.setUserAgent(trackingDataDto.getUserAgent());
        this.setIp(trackingDataDto.getIp());
        this.setCountry(trackingDataDto.getCountry());
        this.setProvince(trackingDataDto.getProvince());
        this.setCity(trackingDataDto.getCity());
        this.setBrowser(trackingDataDto.getBrowser());
        this.setDevice(trackingDataDto.getDevice());
        this.setOs(trackingDataDto.getOs());

        // Submit Dto
        fillUpSurveySubmitRequestDto(this, submitDto);
    }

    public SurveyResponse(SurveyTrackingDataDto trackingDataDto, SurveySubmitRequestDto submitDto, Survey survey) {
        this.setSurveyId(survey.getId());
        this.setOrgId(survey.getOrgId());

        // Tracking, UA
        this.setUserAgent(trackingDataDto.getUserAgent());
        this.setIp(trackingDataDto.getIp());
        this.setCountry(trackingDataDto.getCountry());
        this.setProvince(trackingDataDto.getProvince());
        this.setCity(trackingDataDto.getCity());
        this.setBrowser(trackingDataDto.getBrowser());
        this.setDevice(trackingDataDto.getDevice());
        this.setOs(trackingDataDto.getOs());

        // Submit Dto
        fillUpSurveySubmitRequestDto(this, submitDto);
    }

    public SurveyResponse(Long surveyId, Long orgId, Date startTime, Date finishTime, String ip, Integer durationSeconds, String externalUserId, String departmentCode, Map<String,Object> parameters) {
        this.setSurveyId(surveyId);
        this.setOrgId(orgId);
        this.setCreateTime(startTime);
        this.setModifyTime(finishTime);
        this.setFinishTime(finishTime);
        this.setIp(ip);
        this.setDurationSeconds(durationSeconds);
        this.setExternalUserId(externalUserId);
        this.setDepartmentCode(departmentCode);
        this.setParameters(parameters);
        this.setStatus(ResponseStatus.FINAL_SUBMIT);
        this.setCollectorMethod(SurveyCollectorMethod.IMPORT);
        this.setClientId(UUID.randomUUID().toString());
        this.setIsCompleted(true);
        this.setTotalScore(null);
    }

    public SurveyResponse fillUpSurveySubmitRequestDto(SurveyResponse response, SurveySubmitRequestDto submitDto) {
        if(StringUtils.isEmpty(response.getClientId())) response.setClientId(submitDto.getClientId());
        response.setCollectorMethod(submitDto.getCollectorMethod());
        if(StringUtils.isEmpty(response.getExternalUserId())) response.setExternalUserId(submitDto.getExternalUserId());
        if(response.getCustomerId() == null) response.setCustomerId(submitDto.getCustomerId());
        if(response.getSceneId() == null) response.setSceneId(submitDto.getSceneId());
        if(StringUtils.isEmpty(response.getTrackId())) response.setTrackId(submitDto.getTrackId());
        if(response.getDepartmentId() == null) response.setDepartmentId(submitDto.getDepartmentId());
        if(StringUtils.isEmpty(response.getDepartmentName())) response.setDepartmentName(submitDto.getDepartmentName());
        if(StringUtils.isEmpty(response.getCustomerName())) response.setCustomerName(submitDto.getCustomerName());
        if(StringUtils.isEmpty(response.getCustomerGender())) response.setCustomerGender(submitDto.getCustomerGender());
        if(response.getParameters() == null || response.getParameters().size() == 0) response.setParameters(submitDto.getParameters());
        if(StringUtils.isEmpty(response.getDepartmentCode())) response.setDepartmentCode(submitDto.getDepartmentCode());
        if(StringUtils.isEmpty(response.getExternalCompanyId())) response.setExternalCompanyId(submitDto.getExternalCompanyId());
        if(response.getChannelId() == null) response.setChannelId(submitDto.getChannelId());
        if(StringUtils.isEmpty(response.getDefaultPa())) response.setDefaultPa(submitDto.getDefaultPa());
        if(StringUtils.isEmpty(response.getDefaultPb())) response.setDefaultPb(submitDto.getDefaultPb());
        if(StringUtils.isEmpty(response.getDefaultPc())) response.setDefaultPc(submitDto.getDefaultPc());
        if(StringUtils.isEmpty(response.getOpenid())) response.setOpenid(submitDto.getOpenid());
        response.setTotalScore(submitDto.getTotalScore());

//        if (CollectionUtils.isNotEmpty(submitDto.getTags())) {
//            response.setTags(submitDto.getTags());
//        }

        if (submitDto.getAdditionData() != null) {
            var mapData = JsonHelper.toMap(submitDto.getAdditionData());
            mapData.values().removeIf(Objects::isNull);
            if (!mapData.isEmpty()) {
                response.setAdditionData(JsonHelper.toJson(mapData));
            }
        }

        return response;
    }
    public SurveyResponse decryptField(ApiKey apiKey) {
        return decryptField(apiKey.getApiSecret());
    }

    public SurveyResponse decryptField(String secret) {
        SurveyResponse response = this;
        Arrays.stream(this.getClass().getDeclaredFields()).filter(field -> field.getAnnotation(AESDecryptField.class) != null).forEach(field -> {
            try {
                // @加密数据@
                String value = Objects.toString(field.get(response), null);
                if (value != null && value.startsWith("@") && value.endsWith("@")) {
                    PropertyUtils.setProperty(response, field.getName(), AESUtils.decrypt(secret, value.substring(1, value.length() - 1)));
                }
                // 单独处理自定义参数
                if ("parameters".equals(field.getName())) {
                    response.getParameters().forEach((key, v) -> {
                        String valueP = Objects.toString(v, null);
                        if (valueP != null && valueP.startsWith("@") && valueP.endsWith("@")) {
                            try {
                                PropertyUtils.setProperty(response.getParameters(), key, AESUtils.decrypt(secret, valueP.substring(1, valueP.length() - 1)));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return response;
    }
}
