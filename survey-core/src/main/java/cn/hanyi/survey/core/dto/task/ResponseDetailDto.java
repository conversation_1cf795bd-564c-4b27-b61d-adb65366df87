package cn.hanyi.survey.core.dto.task;

import cn.hanyi.survey.core.constant.ResponseStatus;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ResponseDetailDto extends BaseDTO {
    private Long surveyId;
    private Long orgId;
    private String surveyName;
    private String surveyCode;
    private Long responseId;
    private String clientId;
    private String externalUserId;
    private Long customerId;
    private Long departmentId;
    private Long channelId;
    private String departmentName;
    private String customerName;
    private String customerGender;
    private String departmentCode;
    private String externalCompanyId;
    private List<ResponseDetailDataDto> data = new ArrayList<>();
    private Long startTime;
    private Long finishTime;
    private Integer durationInSeconds;
    private Map<String, Object> parameters = new HashMap<>();
    private ResponseStatus status = ResponseStatus.INIT;
    private Integer totalScore;
}
