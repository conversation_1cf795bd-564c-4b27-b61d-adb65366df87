package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.dto.ext.SurveyExtDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerAware;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;
import org.hibernate.annotations.*;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.*;
import java.util.*;

import static org.befun.core.constant.EntityScopeStrategyType.OWNER_GROUP_CORPORATION;

@Entity
@Getter
@Setter
@Table(name = "survey")
@AllArgsConstructor
@NoArgsConstructor
@SQLDelete(sql = "UPDATE survey SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
//@EntityListeners({EntityChangeListener.class})
@EntityScopeStrategy(value = OWNER_GROUP_CORPORATION, resource = "SURVEY", groupResource = "SURVEY_GROUP")
@FilterDef(
        name = "surveyVerifyFilter",
        parameters = {@ParamDef(
                name = "orgId",
                type = "long"
        ), @ParamDef(
                name = "userId",
                type = "long"
        )}, defaultCondition = "org_id=:orgId and status=2 and id in (select sv.s_id from survey_verify sv where sv.user_id=:userId and sv.version=verify_version )")
@Filter(name = "surveyVerifyFilter")
@DtoClass(includeAllFields = true, superClass = SurveyExtDto.class)
public class Survey extends BaseSurvey implements EnterpriseOwnerAware {

    @Schema(description = "分组id")
    @Column(name = "group_id")
    private Long groupId;

    @Schema(description = "编辑者id")
    @Column(name = "editor_id")
    protected Long editorId;

    @Column(name = "verify_version")
    @Schema(description = "审核版本号")
    private Integer verifyVersion = 0;

    @Schema(description = "问卷编号")
    @Column(name = "survey_code")
    @DtoProperty(jsonView = ResourceViews.Detail.class)
    protected String surveyCode;

    @Column(name = "link_id")
    private Long linkId;

    @Column(name = "response_finish_num")
    @Schema(description = "有效答卷数量")
    private Integer responseFinishNum = 0;

    @Column(name = "enable_date_limit", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启调查日期限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableDateLimit = false;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "问卷开始时间")
    @Column(name = "start_time")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Temporal(TemporalType.TIMESTAMP)
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "问卷结束时间")
    @Column(name = "end_time")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @OrderBy("sequence")
    @DtoProperty(jsonView = ResourceViews.Detail.class, type = SurveyQuestionDto.class)
    private List<SurveyQuestion> questions = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @DtoProperty(jsonView = ResourceViews.Detail.class, type = SurveyLogicDto.class)
    @OrderBy("id")
    private List<SurveyLogic> logics = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @DtoProperty(jsonView = ResourceViews.Detail.class, type = SurveyQuotaDto.class)
    @OrderBy("id")
    private List<SurveyQuota> quotas = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @DtoProperty(jsonView = ResourceViews.Detail.class, type = SurveyQuestionRandomDto.class)
    @OrderBy("id")
    private List<SurveyQuestionRandom> randoms = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @DtoProperty(jsonView = ResourceViews.Detail.class, type = SurveyTagDto.class)
    @OrderBy("id")
    private List<SurveyTag> tags = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @DtoProperty(type = SurveyLanguageDto.class)
    @OrderBy("id")
    private List<SurveyLanguage> languages = new ArrayList<>();

    @OneToMany(mappedBy = "survey", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
    @DtoProperty(type = SurveyPersonalizedRemarkDto.class)
    @OrderBy("id")
    private List<SurveyPersonalizedRemark> personalizedRemarks = new ArrayList<>();


    @Override
    public SurveyEmbed getEmbed() {
        SurveyEmbed embed = super.getEmbed();
        if (embed == null) {
            embed = new SurveyEmbed();
            setEmbed(embed);
        }
        return embed;
    }

    @PreUpdate
    public void preUpdate() {
        Optional.ofNullable(TenantContext.getCurrentUserId()).ifPresentOrElse(this::setEditorId, () -> {
            if (getEditorId() == null) {
                setEditorId(userId);
            }
        });
    }

    @Override
    public void setConcludingRemark(String concludingRemark) {
        Map<String, Object> map = JsonHelper.toMap(concludingRemark);
        if (Objects.isNull(map)) {
            throw new BadRequestException("结束语格式不正确");
        }
        super.setConcludingRemark(concludingRemark);
    }

    @Override
    public void setAbnormalConcludingRemark(String abnormalConcludingRemark) {
        Map<String, Object> map = JsonHelper.toMap(abnormalConcludingRemark);
        if (Objects.isNull(map)) {
            throw new BadRequestException("结束语格式不正确");
        }
        super.setAbnormalConcludingRemark(abnormalConcludingRemark);
    }

}
