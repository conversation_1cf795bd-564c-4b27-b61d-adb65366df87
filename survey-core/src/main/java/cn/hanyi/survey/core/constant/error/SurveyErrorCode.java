package cn.hanyi.survey.core.constant.error;

import lombok.Getter;

@Getter
public enum SurveyErrorCode {
    OVER_LIMIT(30000, "您的设备已填答过该问卷"),
    ALREADY_SUBMIT(30001, "抱歉，您已填答过该问卷"),
    QUOTA_OVER_LIMIT(30002, "不好意思，该问卷配额已满"),
    CHANNEL_STATUS_ERROR(30003, "问卷回收渠道状态错误"),
    CHANNEL_TYPE_ERROR(30004, "当前问卷的收集渠道类型错误"),
    CHANNEL_TYPE_COMPLETE(30005, "抱歉，该问卷的收集已结束"),
    CHANNEL_TYPE_PAUSE(30006, "当前问卷的收集暂停中，您可以稍后再试"),
    CHANNEL_WHITE_BLACK_LIST_ERROR(30006, "抱歉，不符合黑白名单"),
    SURVEY_ERROR(30007, "当前问卷收集的问卷不匹配"),
    SURVEY_CODE_REPEAT(30008, "存在重复的问卷编号，请重新输入！"),
    SURVEY_CODE_OR_ID(30009, "答题有效性验证失败，缺少必要参数问卷id或问卷编号"),
    SURVEY_NOT_FOUND(30009, "答题有效性验证失败，问卷不存在"),
    SURVEY_RESPONSE_NOT_FOUND(30009, "答题有效性验证失败，答卷不存在"),
    SURVEY_NOT_BEGIN(300030, "该问卷的答题时间还未开始，请您晚点再来"),
    SURVEY_IS_END(300031, "您来晚啦，您访问的问卷已停止收集"),
    SURVEY_ANALYSIS_LIMIT(300032, "您当前使用的免费版已收集%s条数据，超出%s条/年，请升级。"),

    //TODO 智能验证专属错误码
    SMART_VERIFY_FAIL(30010, "问卷智能验证失败"),
    SURVEY_RESPONSE_DELETE(30011, "答卷数据已删除"),
    SURVEY_DELETE(30012, "问卷数据已删除"),
    IP_ALREADY_SUBMIT(30013, "您的IP地址已填答过该问卷"),
    IP_DAY_SUBMIT(30014, "您的IP地址每天只能填答一次"),
    IP_WEEK_SUBMIT(30015, "您的IP地址每周只能填答一次"),
    IP_MONTH_SUBMIT(30016, "您的IP地址每月只能填答一次"),
    OPENID_NOT_EXIST(30017, "微信授权登录失败，请重新登录"),
    CREATE_QRCODE_FAIL(30018, "生成二维码失败"),

    RESPONSE_SHARED_VALID_TOKEN(30019, "用户授权失效。请联系管理员"),
    RESPONSE_SHARED_TOKEN_DISABLE(30020, "答卷分享token未开启。请联系管理员"),
    DO_NOT_REPEAT_SUBMIT(30021, "请求中，请勿重复提交"),
    QUESTION_NOT_EXIST(30022, "问题数据已删除"),
    QUESTION_TYPE_ERROR(30023, "问题类型错误"),
    QUESTION_MOBILE_EXIST_SUBMIT(30024, "同一个手机号只能提交一次"),
    QUESTION_MOBILE_NO_BALANCE(30025, "短信余额不足"),
    QUESTION_MOBILE_NOT_OPEN_VERIFY(30026, "问题未开启手机号验证"),
    QUESTION_MOBILE_NOT_OPEN_DEDUPLICATION(30027, "问题未开启手机号去重"),
    SURVEY_CONTENT_AUDITING(30028, "内容审核中"),
    SURVEY_CONTENT_AUDIT_NO_PASS(30029, "内容审核失败"),
    SURVEY_DISABLED(30030, "问卷已禁用"),
    SURVEY_CAN_NOT_PUBLISH(30031, "问卷状态已变更，不能发布"),
    SURVEY_CAN_NOT_AUDITING(30032, "问卷状态已变更，不能提交审核"),
    SURVEY_CAN_NOT_STOPPED(30033, "问卷状态已变更，不能停用"),
    SURVEY_CAN_NOT_APPROVAL(30034, "问卷状态已变更，不能通过审核"),
    SURVEY_CAN_NOT_REJECTED(30035, "问卷状态已变更，不能驳回审核"),
    RESPONSE_VALIDATOR_ERROR(30036, "答题数据校验失败"),
    CREATE_SHORT_LINK_MISSION_DYNAMIC_ITEM_ERROR(30037, "动态选项题缺少选项"),
    PARAMS_DECRYPT_FAILED(30038, "参数解密失败"),
    ALREADY_WECHAT_SUBMIT(30039, "您的微信已填答过该问卷!"),


    LOGIN_REQUIRE(401, "请重新登录"),
    ;

    private int value;
    private String message;

    SurveyErrorCode(int value, String message) {
        this.value = value;
        this.message = message;
    }

    public SurveyErrorCode formatMsg(Object... args) {
        message = String.format(this.message, args);
        return this;
    }
}
