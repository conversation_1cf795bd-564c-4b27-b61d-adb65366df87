package cn.hanyi.survey.core.dto;

import com.fasterxml.jackson.annotation.JsonAlias;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class SubmitAdditionDataDto extends BaseDTO {

    @Schema(description = "客户中心分组code")
    private String groupCode;

    @Schema(description = "答题随机数")
    private Long randomSeedRoot;

    // 1.9.7
    @Schema(description = "需要关联的客户手机号")
    private String linkedCustomerMobile;

    @Schema(description = "发送token")
    @JsonAlias("token")
    private String sendToken;

    @Schema(description = "答题邀请人cuid")
    private String invite;

    public SubmitAdditionDataDto(String sendToken) {
        this.sendToken = sendToken;
    }

}
