package cn.hanyi.survey.core.dto.lottery;

import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.Getter;
import lombok.Setter;

import java.lang.reflect.Field;
import java.util.Date;


@Setter
@Getter
public class SurveyLotteryWinnerDownloadDto {
    @ExcelProperty(value = "答卷ID")
    private String responseId;
    @ExcelProperty(value = "答卷编号")
    private Long sequence;
    @ExcelProperty(value = "客户ID")
    private Long customerId;
    @ExcelProperty(value = "外部用户ID")
    private String externalUserId;
    @ExcelProperty(value = "层级ID")
    private Long departmentId;
    @ExcelProperty(value = "部门名称")
    private String departmentName;
    @ExcelProperty(value = "客户名称")
    private String customerName;
    @ExcelProperty(value = "客户性别")
    private String customerGender;
    @ExcelProperty(value = "外部组织编号")
    private String departmentCode;
    @ExcelProperty(value = "外部企业ID")
    private String externalCompanyId;
    @ExcelProperty(value = "默认参数a")
    private String defaultPa;
    @ExcelProperty(value = "默认参数b")
    private String defaultPb;
    @ExcelProperty(value = "默认参数c")
    private String defaultPc;
    @ExcelProperty(value = "外部参数")
    private String parameters;
    @ExcelProperty(value = "奖品类型", converter = LotteryPrizeTypeConverter.class)
    private LotteryPrizeType type;
    @ExcelProperty(value = "奖品名称")
    private String prizeName;
    @ExcelProperty(value = "发放状态", converter = PrizeSendStatusConverter.class)
    private PrizeSendStatus status;
    @ExcelProperty(value = "奖品信息")
    private String winnerInfo;
    @ExcelIgnore
    private String prizeCode;
    @ExcelIgnore
    private String name;
    @ExcelIgnore
    private String phone;
    @ExcelIgnore
    private String province;
    @ExcelIgnore
    private String city;
    @ExcelIgnore
    private String district;
    @ExcelIgnore
    private String address;
    @ExcelProperty(value = "奖励渠道名称")
    private String lotteryName;
    @ExcelProperty(value = "中奖时间")
    private Date createTime;
    @ExcelProperty(value = "发放时间")
    private Date sendTime;
    @ExcelProperty(value = "发放人")
    private String sendName;
    @ExcelProperty(value = "未领取退回时间")
    private Date refundTime;
    @ExcelIgnore
    private Integer amount;


    public static int excelPropertySize() {
        Field[] fields = SurveyLotteryWinnerDownloadDto.class.getDeclaredFields();
        int size = 0;
        for (int i = 0; i < fields.length; i++) {
            if (fields[i].getAnnotation(ExcelProperty.class) != null) {
                size++;
            }
        }
        return size;
    }


    public static class PrizeSendStatusConverter implements Converter<PrizeSendStatus> {


        @Override
        public Class<PrizeSendStatus> supportJavaTypeKey() {
            return PrizeSendStatus.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public PrizeSendStatus convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

            return Converter.super.convertToJavaData(cellData, contentProperty, globalConfiguration);
        }

        @Override
        public PrizeSendStatus convertToJavaData(ReadConverterContext<?> context) throws Exception {
            return Converter.super.convertToJavaData(context);
        }

        @Override
        public WriteCellData<?> convertToExcelData(PrizeSendStatus status, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            return new WriteCellData<>(status.getText());

        }
    }

    public static class LotteryPrizeTypeConverter implements Converter<LotteryPrizeType> {

        @Override
        public Class<LotteryPrizeType> supportJavaTypeKey() {
            return LotteryPrizeType.class;
        }

        @Override
        public CellDataTypeEnum supportExcelTypeKey() {
            return CellDataTypeEnum.STRING;
        }

        @Override
        public LotteryPrizeType convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {

            return Converter.super.convertToJavaData(cellData, contentProperty, globalConfiguration);
        }

        @Override
        public LotteryPrizeType convertToJavaData(ReadConverterContext<?> context) throws Exception {
            return Converter.super.convertToJavaData(context);
        }

        @Override
        public WriteCellData<?> convertToExcelData(LotteryPrizeType status, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
            return new WriteCellData<>(status.getText());

        }
    }
}

