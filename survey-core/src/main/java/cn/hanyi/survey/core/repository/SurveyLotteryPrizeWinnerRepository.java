package cn.hanyi.survey.core.repository;

import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.entity.SurveyLotteryPrizeWinner;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/9/28 09:46:38
 */
@Repository
public interface SurveyLotteryPrizeWinnerRepository extends ResourceRepository<SurveyLotteryPrizeWinner, Long> {

    List<SurveyLotteryPrizeWinner> findByLotteryId(Long lotteryId);

    @Query(nativeQuery = true, value = "select prize_code from survey_lottery_prize_winner where prize_id = ?1")
    List<String> findPrizeCodeByPrizeId(Long prizeId);

    @Query(nativeQuery = true, value = "select * from survey_lottery_prize_winner where survey_id=?1 and status in (?2)")
    List<SurveyLotteryPrizeWinner> findAllBySidAndStatus(Long surveyId, List<PrizeSendStatus> status);

    @Query(nativeQuery = true, value = "SELECT w.* FROM survey_lottery_prize_winner w " +
            "WHERE w.lottery_id = ?1 AND w.status = ?2 AND w.is_received= 0 " +
            "AND NOT EXISTS (SELECT 1 FROM survey_lottery_redpack_send_record r WHERE r.winner_id = w.id)")
    List<SurveyLotteryPrizeWinner> findAllByLotteryIdAndStatus(Long lotteryId, int status);
    
}
