package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.DeviceLimitType;
import cn.hanyi.survey.core.constant.IpLimitType;
import cn.hanyi.survey.core.constant.question.QuestionNumberMode;
import cn.hanyi.survey.core.constant.survey.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.StringListConverter;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.annotation.ResourceFieldCloneRule;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class BaseSurvey extends EnterpriseEntity {
    @Schema(description = "用户id")
    @Column(name = "user_id")
    protected Long userId;

    @JsonIgnore
    @Schema(description = "问卷是否删除")
    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;

    @Size(max = 200, message = "标题内容长度超过限制")
    @Schema(description = "问卷别名，问卷列表title，数据下载等")
    @ResourceFieldCloneRule("title.concat('_复制')")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(jsonView = ResourceViews.Basic.class, queryable = true)
    private String title = "";

    @Size(max = 200, message = "标题内容长度超过限制")
    @Schema(description = "问卷真实标题：答题端标题显示和问卷内标题")
    //@ResourceFieldCloneRule("title.concat('_复制')")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String realTitle = "";

    @Schema(description = "欢迎语")
    @Column(name = "welcoming_remark")
    @JsonView(ResourceViews.Detail.class)
    private String welcomingRemark = "感谢您能抽出几分钟时间来参加本次答题，我们马上开始吧！";

    @Schema(description = "正常结束语")
    @Column(name = "normal_finish_remark", length = 65535)
    @JsonView(ResourceViews.Detail.class)
    private String concludingRemark = "{\"text\":\"<p class=\\\"ql-align-center\\\" style=\\\"text-align:center;\\\"><img src=\\\"https://assets.surveyplus.cn/surveylite/static/empty-end.svg\\\" /></p><p class=\\\"ql-align-center\\\" style=\\\"color:#999;padding-top:5px;text-align:center;\\\">问卷到此结束，感谢您的参与!</p>\",\"link\":\" \",\"type\":\"text\"}";

    @Schema(description = "非正常结束语")
    @Column(name = "abnormal_finish_remark", length = 65535)
    @JsonView(ResourceViews.Detail.class)
    private String abnormalConcludingRemark = "{\"text\":\"<p class=\\\"ql-align-center\\\"  style=\\\"text-align:center;\\\"><img src=\\\"https://assets.surveyplus.cn/surveylite/static/empty-end.svg\\\" /></p><p class=\\\"ql-align-center\\\" style=\\\"color:#999;padding-top:5px;text-align:center;\\\">非常抱歉，你不适合本次调查的人群条件，感谢您的参与!</p>\",\"link\":\" \",\"type\":\"text\"}";

/*
    @Schema(description = "正常结束语")
    @Column(name = "concluding_remark")
    @JsonView(ResourceViews.Detail.class)
    private String concludingRemark = "问卷到此结束，感谢您的参与!";

    @Schema(description = "非正常结束语")
    @Column(name = "abnormal_concluding_remark")
    @JsonView(ResourceViews.Detail.class)
    private String abnormalConcludingRemark = "非常抱歉，你不适合本次调查的人群条件，感谢您的参与!";
*/

    @Column(name = "status")
    @Enumerated
    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "问卷状态 0已停用")
    @JsonView(ResourceViews.Basic.class)
    private SurveyStatus status = SurveyStatus.STOPPED;

    @Size(max = 20, message = "问卷语言长度超过限制")
    @Schema(description = "问卷语言")
    @JsonView(ResourceViews.Basic.class)
    private String language = "zh-cn";

    @Schema(description = "多语言设置")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "language_setting")
    @Convert(converter = StringListConverter.class)
    private List<String> languageSetting = new ArrayList<>();

    @Embedded
    @JsonView(ResourceViews.Detail.class)
    private SurveyStyle style = new SurveyStyle();

    @Embedded
    @JsonIgnore
    private SurveyEmbed embed = new SurveyEmbed();

    @Enumerated
    @Column(name = "page_mode")
    @Schema(description = "分页模式", example = "STANDARD")
    @JsonView(ResourceViews.Detail.class)
    private PageMode pageMode = PageMode.STANDARD;

    @Enumerated
    @Column(name = "question_number_mode")
    @Schema(description = "问题编码模式", example = "AUTO")
    @JsonView(ResourceViews.Detail.class)
    private QuestionNumberMode questionNumberMode = QuestionNumberMode.AUTO;

    @Column(name = "enable_quota", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启配额")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableQuota = false;

    @Column(name = "enable_adminx_quota", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启社区配额")
    @JsonView(ResourceViews.Detail.class)
    @DtoProperty(description = "是否开启社区配额", example = "", jsonView = ResourceViews.Basic.class)
    private Boolean enableAdminxQuota = false;


    @Column(name = "enable_tag", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启答卷编号")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableTag = false;

    @Column(name = "automatic_page", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否自动翻页")
    @JsonView(ResourceViews.Detail.class)
    private Boolean goNextPageAutomatic = false;

    @Column(name = "quota_status")
    @Enumerated
    @ResourceFieldCloneRule(ignore = true)
    @Schema(description = "问卷状态 0初始状态 1配额计算中 2配额计算完成")
    @JsonView(ResourceViews.Basic.class)
    private SurveyQuotaStatus quotaStatus = SurveyQuotaStatus.CALCULATE_INIT;

    @Column(name = "adminx_quota_status")
    @Enumerated
    @Schema(description = "社区问卷配额状态 0初始状态 1配额计算中 2配额计算完成")
    @JsonView(ResourceViews.Basic.class)
    private SurveyQuotaStatus adminxQuotaStatus = SurveyQuotaStatus.CALCULATE_INIT;

/*
    @Column(name = "enable_redirect", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启重定向")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableRedirect = false;

    @Size(max = 100, message = "跳转地址长度超过限制")
    @Column(name = "redirect_url")
    @Schema(description = "问卷结束跳转地址")
    @JsonView(ResourceViews.Detail.class)
    private String redirectUrl;
*/

    @Column(name = "show_header", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示表头")
    @JsonView(ResourceViews.Basic.class)
    private Boolean showHeader = false;

    @Column(name = "show_title")
    @Schema(description = "是否显示标题")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showTitle = true;

    @Column(name = "show_background", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示背景", example = "true")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showBackground = false;

    @Column(name = "show_logo", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示Logo")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showLogo = false;

    @Column(name = "show_welcoming", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示欢迎语")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showWelcoming = true;

    @Column(name = "show_concluding", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示结束语")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showConcluding = true;

    @Column(name = "show_previous_button", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示上一页")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showPreviousButton = true;

    @Column(name = "show_progress_bar", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示进度条")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showProgressBar = true;

    @Column(name = "show_page_numbers", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示页码")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showPageNumbers = true;

    @Column(name = "show_question_numbers", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示问题编码")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showQuestionNumbers = true;

    @Column(name = "show_brand", columnDefinition = "bit(1) default 1")
    @Schema(description = "是否显示品牌")
    @JsonView(ResourceViews.Detail.class)
    private Boolean showBrand = true;

    @Column(name = "brand_logo")
    @Schema(description = "底部logo图片", example = "http://xxx")
    @JsonView(ResourceViews.Detail.class)
    private String brandLogo;

    @Size(max = 200, message = "logo地址长度超过限制")
    @Column(name = "logo")
    @Schema(description = "logo地址")
    @JsonView(ResourceViews.Detail.class)
    private String logo = "";

    @Size(max = 200, message = "表头图片地址长度超过限制")
    @Column(name = "header_image_pc")
    @Schema(description = "表头图片PC版本")
    @JsonView(ResourceViews.Basic.class)
    private String headerImagePc = "";

    @Size(max = 200, message = "表头图片地址长度超过限制")
    @Column(name = "header_image_mobile")
    @Schema(description = "表头图片mobile版本")
    @JsonView(ResourceViews.Basic.class)
    private String headerImageMobile = "";

    @Size(max = 200, message = "背景图片地址长度超过限制")
    @Column(name = "background_image")
    @Schema(description = "背景图片", example = "http://xxx")
    @JsonView(ResourceViews.Detail.class)
    private String backgroundImage = "";

    @Column(name = "logo_fit")
    @Schema(description = "logoFit")
    @JsonView(ResourceViews.Detail.class)
    @Enumerated
    private LogoFit logoFit = LogoFit.CONTAIN;

    @Column(name = "logo_position")
    @Schema(description = "logo位置")
    @Enumerated
    @JsonView(ResourceViews.Detail.class)
    private LogoPosition logoPosition = LogoPosition.LEFT;

    @Column(name = "enable_ip_limit", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启IP限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableIpLimit = false;

    @Column(name = "ip_limit")
    @Schema(description = "ip限制范围 只能、每天、每周、每月一次")
    @Enumerated
    @JsonView(ResourceViews.Detail.class)
    private IpLimitType ipLimit = IpLimitType.ONCE;

    @Column(name = "enable_device_limit", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启设备限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableDeviceLimit = false;

    @Column(name = "device_limit")
    @Schema(description = "设备限制范围 只能、每天、每周、每月一次")
    @Enumerated
    @JsonView(ResourceViews.Detail.class)
    private DeviceLimitType deviceLimit = DeviceLimitType.ONCE;

    @Column(name = "enable_smart_verify", columnDefinition = "bit(1) default 0")
    @Schema(description = "是否开启智能验证")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableSmartVerify = false;

    @Column(name = "enable_response_limit", columnDefinition = "tinyint(1) default 0")
    @Schema(description = "是否开启答卷回收限制")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableResponseLimit = false;

    @Column(name = "response_amount")
    @Schema(description = "回收数量")
    @JsonView(ResourceViews.Detail.class)
    private Integer responseAmount = 0;

    @Column(name = "enable_behavior", columnDefinition = "tinyint(1) default 0")
    @Schema(description = "是否开启行为记录 0未开启 1开启")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableBehavior = false;

    @Column(name = "embed_brand_show", columnDefinition = "tinyint(1) default 1")
    @Schema(description = "体验家品牌展示, 关闭false,启用true")
    @JsonView(ResourceViews.Detail.class)
    private Boolean embedBrandShow = true;

    @Column(name = "enable_response_import", columnDefinition = "tinyint(1) default 0")
    @Schema(description = "是否开启答卷导入功能")
    @JsonView(ResourceViews.Detail.class)
    private Boolean enableResponseImport = false;

}
