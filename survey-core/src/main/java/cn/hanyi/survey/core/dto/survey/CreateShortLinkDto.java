package cn.hanyi.survey.core.dto.survey;

import cn.hanyi.survey.core.dto.question.DynamicQuestionDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.function.Supplier;

@Getter
@Setter
public class CreateShortLinkDto {

    @Schema(description = "问卷id，问卷id和问卷编号必须要有一个，问卷id优先")
    private Long surveyId;

    @Schema(description = "问卷编号，问卷id和问卷编号必须要有一个，问卷id优先")
    private String surveyCode;

    @Schema(description = "答卷唯一编号，没有此参数，则系统会自动生成一个")
    private String clientId;

    @Schema(description = "渠道ID")
    private Long channelId;

    @Schema(description = "客户中心客户ID")
    private Long customerId;

    @Schema(description = "外部客户编号")
    private String externalUserId;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "客户性别")
    private String customerGender;

    @Schema(description = "组织ID")
    private Long departmentId;

    @Schema(description = "组织名称")
    private String departmentName;

    @Schema(description = "外部组织编号")
    private String departmentCode;

    @Schema(description = "外部企业ID")
    private String externalCompanyId;

    @Schema(description = "默认参数a")
    private String defaultPa;

    @Schema(description = "默认参数b")
    private String defaultPb;

    @Schema(description = "默认参数c")
    private String defaultPc;

    @Schema(description = "预填答题数据")
    private Map<String, Object> surveyValues = new HashMap<>();

    @Schema(description = "自定义参数信息")
    private Map<String, Object> parameters = new HashMap<>();

    @Schema(description = "附加数据")
    private String additionData;

    @Valid
    @Schema(description = "动态选项")
    private List<DynamicQuestionDto> dynamicQuestionItems = new ArrayList<>();

    public Map<String, Object> buildMap() {
        Map<String, Object> map = new HashMap<>();
        putIfExist(map, "clientId", this::getClientId);
        putIfExist(map, "channelId", this::getChannelId);
        putIfExist(map, "customerId", this::getCustomerId);
        putIfExist(map, "externalUserId", this::getExternalUserId, StringUtils::isNotEmpty);
        putIfExist(map, "customerName", this::getCustomerName, StringUtils::isNotEmpty);
        putIfExist(map, "customerGender", this::getCustomerGender, StringUtils::isNotEmpty);
        putIfExist(map, "departmentId", this::getDepartmentId);
        putIfExist(map, "departmentName", this::getDepartmentName, StringUtils::isNotEmpty);
        putIfExist(map, "departmentCode", this::getDepartmentCode, StringUtils::isNotEmpty);
        putIfExist(map, "externalCompanyId", this::getExternalCompanyId, StringUtils::isNotEmpty);
        putIfExist(map, "defaultPa", this::getDefaultPa, StringUtils::isNotEmpty);
        putIfExist(map, "defaultPb", this::getDefaultPb, StringUtils::isNotEmpty);
        putIfExist(map, "defaultPc", this::getDefaultPc, StringUtils::isNotEmpty);
        putIfExist(map, "surveyValues", this::getSurveyValues, i -> !i.isEmpty());
        putIfExist(map, "parameters", this::getParameters, i -> !i.isEmpty());
        putIfExist(map, "additionData", this::getAdditionData, StringUtils::isNotEmpty);
        putIfExist(map, "dynamicQuestionItems", this::getDynamicQuestionItems, CollectionUtils::isNotEmpty);
        return map;
    }

    private <T> void putIfExist(Map<String, Object> map, String name, Supplier<T> get) {
        putIfExist(map, name, get, null);
    }

    private <T> void putIfExist(Map<String, Object> map, String name, Supplier<T> get, Predicate<T> test) {
        T value = get.get();
        if (value != null && (test == null || test.test(value))) {
            map.put(name, value);
        }
    }
}
