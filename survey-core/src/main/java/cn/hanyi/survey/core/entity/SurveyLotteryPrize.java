package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.lottery.LotteryPrizeType;
import cn.hanyi.survey.core.dto.lottery.SurveyLotteryPrizeExtDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.SetConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2022/9/20 11:05:45
 */
@Entity
@Setter
@Getter
@DtoClass(includeAllFields = true, superClass = SurveyLotteryPrizeExtDto.class)
@Table(name = "survey_lottery_prize")
public class SurveyLotteryPrize extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lottery_id")
    @JsonView(ResourceViews.Basic.class)
    @DtoProperty(ignore = true)
    private SurveyLottery lottery;

    @Schema(description = "奖品名称")
    @Column(name = "prize_name")
    @Size(max = 50, message = "长度限制100")
    @JsonView(ResourceViews.Basic.class)
    private String prizeName;

    @Schema(description = "顺序")
    @JsonView(ResourceViews.Basic.class)
    private Integer sequence = 0;

    @Schema(description = "奖品类型")
    @JsonView(ResourceViews.Basic.class)
    private LotteryPrizeType type = LotteryPrizeType.PHYSICAL_PRIZE;

    @Schema(description = "是否是奖品")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "is_prize")
    private Boolean isPrize = false;

    @Schema(description = "奖品图片")
    @JsonView(ResourceViews.Basic.class)
    private String image;

    @Min(0)
    @Schema(description = "奖品数量、红包个数")
    @JsonView(ResourceViews.Basic.class)
    private Integer number;

    @Schema(description = "单个红包最小金额:单位分")
    @JsonView(ResourceViews.Basic.class)
    @Min(100)
    private Integer minMoney;

    @Schema(description = "单个红包最大金额:单位分")
    @JsonView(ResourceViews.Basic.class)
    @Max(20000)
    private Integer maxMoney;

    @Schema(description = "单个红包金额:单位分")
    @JsonView(ResourceViews.Basic.class)
    private Integer money;

    @Schema(description = "当前奖品已中奖数量")
    @Column(name = "winner_num")
    @JsonView(ResourceViews.Basic.class)
    private Integer winnerNum = 0;

    @Schema(description = "已中奖金额：单位分")
    @Column(name = "winner_money")
    @JsonView(ResourceViews.Basic.class)
    private Integer winnerMoney = 0;

    @Schema(description = "红包总金额:单位分")
    @Column(name = "total_money")
    @JsonView(ResourceViews.Basic.class)
    private Integer totalMoney;

    @Min(value = 0, message = "概率不能小于0")
    @Schema(description = "中奖概率")
    @JsonView(ResourceViews.Basic.class)
    private Float percent;

    @Schema(description = "兑换码")
    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = SetConverter.class)
    @Size(max = 1000)
    private Set<String> codes;

    @Schema(description = "兑换地址开关")
    @Column(name = "is_address", columnDefinition = "bit(1) default 0")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isAddress = false;

    @Schema(description = "兑奖地址")
    @JsonView(ResourceViews.Basic.class)
    private String address;

    @Schema(description = "领奖信息")
    @Column(name = "is_imporve_award_info", columnDefinition = "bit(1) default 0")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isImporveAwardInfo = true;


    public Boolean getImporveAwardInfo() {
        return isImporveAwardInfo == null || isImporveAwardInfo;
    }

    @PostLoad
    public void postLoad() {
        if (LotteryPrizeType.RANDOM_RED_PACK.equals(type) && (minMoney == null || maxMoney == null)) {
            minMoney = 100;
            maxMoney = 2000;
        }
    }
}
















