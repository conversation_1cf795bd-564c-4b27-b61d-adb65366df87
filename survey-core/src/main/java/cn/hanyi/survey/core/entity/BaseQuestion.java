package cn.hanyi.survey.core.entity;

import cn.hanyi.survey.core.constant.AreaCodeEnum;
import cn.hanyi.survey.core.constant.question.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.StringListConverter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class BaseQuestion extends BaseEntity {

    @Schema(description = "题组code值")
    @Column(name = "group_code")
    @JsonView(ResourceViews.Basic.class)
    private String groupCode;

    @Enumerated
    @Schema(description = "题型", required = true)
    @JsonView(ResourceViews.Basic.class)
    private QuestionType type;

    @Schema(description = "顺序")
    @JsonView(ResourceViews.Basic.class)
    private BigDecimal sequence = BigDecimal.ZERO;

    //@Size(max = 2000, message = "标题长度超多限制")
    @Schema(description = "标题", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String title = "";

    @NotBlank(message = "name不能为空")
    @NotEmpty(message = "name不能为空")
    @Size(max = 100, message = "name长度超多限制")
    @Schema(description = "名称: SEL问题ID来源, 需要保证唯一性", required = true)
    @JsonView(ResourceViews.Basic.class)
    private String name = "";


    @Size(max = 100, message = "自定义编码长度超多限制")
    @Schema(description = "自定义题型编码", required = false)
    @JsonView(ResourceViews.Basic.class)
    private String code = "";

    @Enumerated
    @Schema(description = "输入类型")
    @Column(name = "input_type")
    @JsonView(ResourceViews.Basic.class)
    private InputType inputType;

    @Enumerated
    @Schema(description = "格式化类型(地区题和日期题)")
    @Column(name = "area_type")
    @JsonView(ResourceViews.Basic.class)
    private FormatType areaType;


    @Schema(description = "最小值")
    @JsonView(ResourceViews.Basic.class)
    private Integer min;

    @Schema(description = "最大值")
    @JsonView(ResourceViews.Basic.class)
    private Integer max;

    @Schema(description = "标签")
    @Column(name = "labels")
    @Convert(converter = StringListConverter.class)
    @JsonView(ResourceViews.Basic.class)
    private List<String> labels = new ArrayList<>();

    @Schema(description = "最小长度")
    @Column(name = "min_length")
    @JsonView(ResourceViews.Basic.class)
    private Integer minLength;

    @Schema(description = "最大长度")
    @Column(name = "max_length")
    @JsonView(ResourceViews.Basic.class)
    private Integer maxLength;

    @Schema(description = "刻度步长")
    @Column(name = "step_length")
    @JsonView(ResourceViews.Basic.class)
    private Integer stepLength;

    @Schema(description = "小数位数")
    @Column(name = "decimal_places")
    @JsonView(ResourceViews.Basic.class)
    private Integer decimalPlaces;


    @Column(name = "is_required")
    @Schema(description = "是否选项必答", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isRequired = false;

    @Column(name = "is_hide")
    @Schema(description = "是否隐藏题目", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isHide = false;

    @Column(name = "is_nps")
    @Schema(description = "是否NPS值 (仅限score题型)", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isNps = false;

    @Column(name = "show_label")
    @Schema(description = "是否显示标签", example = "true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean showLabel = true;

    @Column(name = "inapplicable")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否不适用", example = "false")
    private Boolean inapplicable = false;

    @Size(max = 15, message = "是否不使用标签长度超多限制")
    @Column(name = "inapplicable_label")
    @Schema(description = "是否不适用标签", example = "不适用")
    @JsonView(ResourceViews.Basic.class)
    private String inapplicableLabel;

    @Size(max = 2000, message = "SEL表达式长度超多限制")
    @Column(name = "visible_if")
    @Schema(description = "显示条件 SEL表达式", example = "q1 > 3")
    @JsonView(ResourceViews.Basic.class)
    private String visibleIf;

    @Column(name = "enable_validator")
    @Schema(description = "是否开启验证", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableValidator = false;

    @Column(name = "enable_total")
    @Schema(description = "滑动条加总值", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableTotal = false;

    @Enumerated
    @Column(name = "total_type")
    @Schema(description = "滑动条加总值枚举", example = "EQUAL")
    @JsonView(ResourceViews.Basic.class)
    private TotalType totalType = TotalType.EQUAL;

    @Column(name = "total_value")
    @Schema(description = "滑动条加总值条件数值")
    @JsonView(ResourceViews.Basic.class)
    private Double totalValue;

    @Enumerated
    @Column(name = "items_order")
    @Schema(description = "选项排序模式", example = "NONE")
    @JsonView(ResourceViews.Basic.class)
    private ItemsOrderType itemsOrder = ItemsOrderType.NONE;

    @Column(name = "random_limit")
    @Schema(description = "随机选项数量")
    @JsonView(ResourceViews.Basic.class)
    private Integer randomLimit;

    @Column(name = "has_other")
    @Schema(description = "是否有其他选项", example = "true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean hasOther = false;

    @Column(name = "exclude_other")
    @Schema(description = "是否排除其他", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean excludeOther = false;

    @Column(name = "exclude_other_label")
    @Schema(description = "是否排除其他标签", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean excludeOtherLabel = false;

    @Size(max = 500, message = "其他文本长度超多限制")
    @Column(name = "other_label")
    @Schema(description = "其他文本内容", example = "其他")
    @JsonView(ResourceViews.Basic.class)
    private String otherLabel = "其他";

    @Column(name = "show_type")
    @Schema(description = "是否显示题型标签", example = "true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean showType = true;

    @Schema(description = "备注信息", example = "...")
    @JsonView(ResourceViews.Basic.class)
    private String remark;

    @Schema(description = "日期题验证开始时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "start_time")
    @JsonView(ResourceViews.Basic.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "日期题验证结束时间")
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "end_time")
    @JsonView(ResourceViews.Basic.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @Column(name = "adapted_mobile")
    @Schema(description = "是否适配移动设备", example = "true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean adaptedMobile = false;

    @Schema(description = "问卷题型配置项", example = "[]")
    @JsonView(ResourceViews.Basic.class)
    @Size(max = 60000, message = "字数超过上限")
    private String configure = "";

    @Schema(description = "联合实验禁止组合", example = "[]")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "no_combination")
    @Size(max = 60000, message = "字数超过上限")
    private String noCombination = "";

    @Column(name = "is_score")
    @Schema(description = "是否打开选项分值(仅支持单选、多选、矩阵单选)", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isScore = false;

    @Size(max = 255, message = "超多限制")
    @Schema(description = "文本题提示输入", example = "")
    @JsonView(ResourceViews.Basic.class)
    private String placeHolder;

    @Enumerated
    @Column(name = "item_layout")
    @Schema(description = "选项布局")
    @JsonView({ResourceViews.Basic.class})
    private ItemLayoutType itemLayout = ItemLayoutType.LEFTRIGTH;

    @Enumerated
    @Column(name = "label_layout")
    @Schema(description = "标签布局")
    @JsonView({ResourceViews.Basic.class})
    private LabelLayoutType labelLayout = LabelLayoutType.FIXED_WIDTH;

    @Column(name = "is_default_value")
    @Schema(description = "是否打开默认答案", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isDefaultValue = false;

    @Column(name = "default_value")
    @Size(max = 20, message = "超多限制")
    @Schema(description = "默认答案", example = "")
    @JsonView(ResourceViews.Basic.class)
    private String evaluationValue;

    @Column(name = "is_modify")
    @Schema(description = "是否允许受访者修改", example = "true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isModify = true;

    @Column(name = "alias")
    @Size(max = 5000, message = "超多限制")
    @Schema(description = "选项别名", example = "")
    @JsonView(ResourceViews.Basic.class)
    private String alias;

    @Column(name = "is_verify_mobile")
    @Schema(description = "手机题配置项-短信验证")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isVerifyMobile;

    @Column(name = "is_link_customer")
    @Schema(description = "手机题配置项-客户中心关联")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isLinkCustomer;

    @Column(name = "is_deduplication")
    @Schema(description = "手机题配置项-手机号去重")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isDeduplication;

    @Column(name = "is_dynamic_item")
    @Schema(description = "是否启用动态选项")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isDynamicItem;

    @Column(name = "show_tags")
    @Schema(description = "是否显示选项标签", example = "true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean showTags = false;

    @Column(name = "other_country_number")
    @Schema(description = "是否开启国际/港澳台号码", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean otherCountryNumber = false;

    @Column(name = "area_code")
    @Schema(description = "默认区号", example = "")
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private AreaCodeEnum areaCode = AreaCodeEnum.CN;

    @Column(name = "is_media_random")
    @Schema(description = "多媒体情景随机开关", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isMediaRandom = false;


    @Column(name = "title_show")
    @Schema(description = "矩阵题度量标题重复显示")
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private MeasureTitleRepeatType titleShow = MeasureTitleRepeatType.NOREPEAT;


    @Column(name = "is_items_group")
    @Schema(description = "选项分组", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isItemsGroup = false;

    @Column(name = "item_column_exchange")
    @Schema(description = "是否开启行列转换")
    @JsonView(ResourceViews.Basic.class)
    private Boolean itemColumnExchange = false;


    @Enumerated
    @Schema(description = "选项样式")
    @Column(name = "selection_style")
    @JsonView(ResourceViews.Basic.class)
    private SelectionStyle selectionStyle;

    @Column(name = "enable_quote")
    @Schema(description = "开启题目引用", example = "false")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableQuote = false;

    @Column(name = "quote")
    @Schema(description = "题目引用")
    @JsonView(ResourceViews.Basic.class)
    private String quote;
}
