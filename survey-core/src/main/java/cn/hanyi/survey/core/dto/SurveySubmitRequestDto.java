package cn.hanyi.survey.core.dto;

import cn.hanyi.survey.core.annotation.AESDecryptField;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.dto.question.DynamicQuestionDto;
import cn.hanyi.survey.core.dto.question.DynamicQuestionItemDto;
import cn.hanyi.survey.core.utilis.AESUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.dto.BaseDTO;
import org.befun.core.utils.EnumHelper;
import org.befun.extension.dto.SmartVerifyRequestDto;

import javax.validation.constraints.Size;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class SurveySubmitRequestDto extends BaseDTO {

    @Schema(hidden = true, description = "对称秘钥")
    private String base64Key;
    @Schema(hidden = true, description = "key的加密方式：rsa/aes(默认)，sm2/sm4")
    private String encryptType = "rsa/aes";

    @Schema(description = "已经用公钥加密过的key")
    private String encryptedKey;

    @Schema(description = "已经用key加密过的答卷数据")
    private String encryptedData;

    @AESDecryptField
    @Schema(description = "客户端id", required = false)
    private String clientId;

    @Schema(description = "responseId", required = false)
    private Long responseId;

    @Schema(description = "是否提前结束，需要配合isCompleted")
    private Boolean isEarlyCompleted = false;

    @Schema(description = "是否完成")
    private Boolean isCompleted = false;

    // 下面的是答题数据

    @Schema(description = "答题持续时间(秒单位)", required = true)
    private Integer durationSeconds;

    @Schema(description = "总得分", required = false)
    private Integer totalScore;

    @Schema(description = "问卷提交数据", required = true)
    private Map<String, Object> data = new LinkedHashMap<>();

    @Schema(description = "问题得分")
    private Map<String, Integer> cellScore = new HashMap<>();

    @Deprecated(since = "1.9.9 后端判定")
    @Schema(description = "答卷标签")
    private List<String> tags = new ArrayList<>();

    // 下面的都是参数

    @AESDecryptField
    @Size(max = 100, message = "外部用户ID长度超个100个字符限制")
    @Schema(description = "外部用户id", required = false)
    private String externalUserId;

    @Schema(description = "客户id", required = false)
    private Long customerId;

    @Schema(description = "场景ID", required = false)
    private Long sceneId;

    @Schema(description = "部门ID", required = false)
    private Long departmentId;

    @AESDecryptField
    @Schema(description = "外参", required = false)
    private Map<String, Object> parameters = new HashMap<>();

    @AESDecryptField
    @Schema(description = "跟踪id，内部业务使用", required = false)
    private String trackId;

    @AESDecryptField
    @Size(max = 100, message = "部门名称长度超个100个字符限制")
    @Schema(description = "部门名称", required = false)
    private String departmentName;

    @AESDecryptField
    @Size(max = 30, message = "客户名称长度超个30个字符限制")
    @Schema(description = "客户名称", required = false)
    private String customerName;

    @AESDecryptField
    @Size(max = 30, message = "客户性别长度超个30个字符限制")
    @Schema(description = "客户性别", required = false)
    private String customerGender;

    @AESDecryptField
    @Size(max = 100, message = "外部组织编号长度超个100个字符限制")
    @Schema(description = "外部组织编号")
    private String departmentCode;

    @AESDecryptField
    @Size(max = 100, message = "外部企业ID长度超个100个字符限制")
    @Schema(description = "外部企业ID")
    private String externalCompanyId;

    @Schema(description = "收集方式")
    private SurveyCollectorMethod collectorMethod = SurveyCollectorMethod.LINK;

    @Schema(description = "问卷收集渠道")
    private Long channelId;

    @AESDecryptField
    @Size(max = 500, message = "默认参数a长度超个500个字符限制")
    @Schema(description = "默认参数")
    private String defaultPa;

    @AESDecryptField
    @Size(max = 500, message = "默认参数b长度超个500个字符限制")
    @Schema(description = "默认参数")
    private String defaultPb;

    @AESDecryptField
    @Size(max = 500, message = "默认参数c长度超个500个字符限制")
    @Schema(description = "默认参数")
    private String defaultPc;

    @AESDecryptField
    @Schema(description = "微信openid")
    private String openid;

    @Schema(description = "答题时不太重要的数据, SubmitAdditionDataDto类型的，需要使用时自行转换")
    private SubmitAdditionDataDto additionData;

    @Schema(description = "智能验证参数", required = false)
    private SmartVerifyRequestDto smartVerify;

    @Schema(description = "动态选项")
    private List<DynamicQuestionDto> dynamicQuestionItems = new ArrayList<>();
    
    private String sendToken;
    private Long surveyId;

    public Map<String, DynamicQuestionDto> convertToDynamicQuestionMap() {
        Map<String, DynamicQuestionDto> map = new HashMap<>();
        for (DynamicQuestionDto item : dynamicQuestionItems) {
            map.put(item.getCode(), item);
        }
        return map;
    }

    /**
     * 判断是否提交合理
     *
     * @return
     */
    @JsonIgnore
    public boolean isValid() {
        if (!isCompleted && isEarlyCompleted) {
            return false;
        }
        return true;
    }

    /**
     * 合并参数：从短链编码中提取的参数（高优先级）+ 前端提交的参数
     * Since: 1.9.0
     */
    public void mergeParams(Map<String, Object> linkParams) {
        if (linkParams == null || linkParams.isEmpty()) {
            return;
        }
        mergeParam(linkParams.get("clientId"), Object::toString, this::setClientId);
        mergeParam(linkParams.get("externalUserId"), Object::toString, this::setExternalUserId);
        mergeParam(linkParams.get("customerId"), this::castLong, this::setCustomerId);
        mergeParam(linkParams.get("sceneId"), this::castLong, this::setSceneId);
        mergeParam(linkParams.get("departmentId"), this::castLong, this::setDepartmentId);
        mergeParam(linkParams.get("parameters"), this::castMap, this::getParameters, this::setParameters);
        mergeParam(linkParams.get("trackId"), Object::toString, this::setTrackId);
        mergeParam(linkParams.get("departmentName"), Object::toString, this::setDepartmentName);
        mergeParam(linkParams.get("customerName"), Object::toString, this::setCustomerName);
        mergeParam(linkParams.get("customerGender"), Object::toString, this::setCustomerGender);
        mergeParam(linkParams.get("departmentCode"), Object::toString, this::setDepartmentCode);
        mergeParam(linkParams.get("externalCompanyId"), Object::toString, this::setExternalCompanyId);
        mergeParam(linkParams.get("collectorMethod"), this::castCollectorMethod, this::setCollectorMethod);
        mergeParam(linkParams.get("channelId"), this::castLong, this::setChannelId);
        mergeParam(linkParams.get("defaultPa"), Object::toString, this::setDefaultPa);
        mergeParam(linkParams.get("defaultPb"), Object::toString, this::setDefaultPb);
        mergeParam(linkParams.get("defaultPc"), Object::toString, this::setDefaultPc);
        mergeParam(linkParams.get("openid"), Object::toString, this::setOpenid);
        mergeParam(linkParams.get("additionData"), this::castAdditionData, this::getAdditionData, this::setAdditionData);
        mergeParam(linkParams.get("dynamicQuestionItems"), this::castDynamicQuestionItems, this::setDynamicQuestionItems);
    }

    public Long castLong(Object param) {
        if (param instanceof Long) {
            return (Long) param;
        } else {
            String p = param.toString();
            if (NumberUtils.isDigits(p)) {
                return Long.parseLong(p);
            }
        }
        return null;
    }

    public SurveyCollectorMethod castCollectorMethod(Object param) {
        return EnumHelper.parse(SurveyCollectorMethod.values(), param.toString(), SurveyCollectorMethod.LINK);
    }

    @SuppressWarnings("unchecked")
    public Map<String, Object> castMap(Object param, Map<String, Object> old) {
        if (param instanceof Map) {
            Map<String, Object> p = (Map<String, Object>) param;
            old = old != null ? old : new HashMap<>();
            old.putAll(p);
        }
        return old;
    }

    @SuppressWarnings("unchecked")
    public SubmitAdditionDataDto castAdditionData(Object param, SubmitAdditionDataDto old) {
        if (param instanceof Map) {
            Map<String, Object> p = (Map<String, Object>) param;

            for (Field field : SubmitAdditionDataDto.class.getDeclaredFields()) {
                try {
                    Object value = p.get(field.getName());
                    if (value != null) {
                        PropertyUtils.setProperty(old, field.getName(), value);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        return old;
    }

    public List<DynamicQuestionDto> castDynamicQuestionItems(Object param) {
        List<DynamicQuestionDto> dynamicQuestions = new ArrayList<>();
        if (param instanceof List) {
            List<?> questionListMap = (List<?>) param;
            for (Object questionMapObject : questionListMap) {
                if (questionMapObject instanceof Map) {
                    Map<?, ?> questionMap = (Map<?, ?>) questionMapObject;
                    Object code = questionMap.get("code");
                    Object items = questionMap.get("items");
                    DynamicQuestionDto dynamicQuestion = new DynamicQuestionDto();
                    if (code instanceof String && items instanceof List){
                        dynamicQuestions.add(dynamicQuestion);
                        dynamicQuestion.setCode(code.toString());
                        List<?> itemListMap = (List<?>) items;
                        for (Object itemMapObject : itemListMap) {
                            if (itemMapObject instanceof Map) {
                                Map<?, ?> itemMap = (Map<?, ?>) itemMapObject;
                                Object value = itemMap.get("value");
                                Object text = itemMap.get("text");
                                if (value instanceof String && text instanceof String) {
                                    DynamicQuestionItemDto questionItem = new DynamicQuestionItemDto();
                                    questionItem.setValue(value.toString());
                                    questionItem.setText(text.toString());
                                    dynamicQuestion.getItems().add(questionItem);
                                }
                            }
                        }

                    }
                }
            }
        }
        return dynamicQuestions;
    }

    public <T> void mergeParam(Object param, BiFunction<Object, T, T> cast, Supplier<T> get, Consumer<T> set) {
        if (param != null) {
            T castParam = cast.apply(param, get.get());
            if (castParam != null) {
                set.accept(castParam);
            }
        }
    }

    public <T> void mergeParam(Object param, Function<Object, T> cast, Consumer<T> set) {
        if (param != null) {
            T castParam = cast.apply(param);
            if (castParam != null) {
                set.accept(castParam);
            }
        }
    }

    public SurveySubmitRequestDto decryptField(String secret) {
        SurveySubmitRequestDto requestDto = this;
        Arrays.stream(SurveySubmitRequestDto.class.getDeclaredFields()).filter(field -> field.getAnnotation(AESDecryptField.class) != null).forEach(field -> {
            try {
                // @加密数据@
                String value = Objects.toString(field.get(requestDto), null);
                if (value != null && value.startsWith("@") && value.endsWith("@")) {
                    PropertyUtils.setProperty(requestDto, field.getName(), AESUtils.decrypt(secret, value.substring(1, value.length() - 1)));
                }
                // 单独处理自定义参数
                if ("parameters".equals(field.getName())) {
                    requestDto.getParameters().forEach((key, v) -> {
                        String valueP = Objects.toString(v, null);
                        if (valueP != null && valueP.startsWith("@") && valueP.endsWith("@")) {
                            try {
                                PropertyUtils.setProperty(requestDto.getParameters(), key, AESUtils.decrypt(secret, valueP.substring(1, valueP.length() - 1)));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        return requestDto;
    }
}