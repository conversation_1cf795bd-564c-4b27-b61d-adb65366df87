package cn.hanyi.survey.core.constant.question;

import lombok.Getter;

@Getter
public enum QuestionType {
    EMPTY(0,"空"),
    SEPARATOR(1,"分隔符"),
    TEXT(2,"文本题"),
    SINGLE_CHOICE(3,"单选题"),
    MULTIPLE_CHOICES(4,"多选题"),
    MARK(5,"备注题"),
    SCORE(6,"量表题"),
    NUMBER(7,"数字题"),
    MOBILE(8,"手机题"),
    EMAIL(9,"邮箱题"),
    MATRIX(10,""),
    MATRIX_SCORE(11,"矩阵量表题"),
    AREA(12,"地区题"),
    DATE(13,"日期题"),
    MATRIX_CHOICE(14,"矩阵单选题"),
    DROP_DOWN(15,"逐级下拉题"),
    MATRIX_SLIDER(16,"滑动条"),
    FILE(17,"文件上传"),
    MEDIA(18,"多媒体"),
    EVALUATION(19,"表情-评价题"),
    ORGANIZE(20,"组织架构题"),
    EXPERIMENT(21,"联合实验题"),
    GROUP(22,"题组"),
    RANKING(23,"排序题"),
    COMBOBOX(24,"下拉框"),
    SCORE_EVALUATION(25,"打分-评价题"),
    LOCATION(26,"地理位置题"),
    NPS(27,"NPS题"),
    BLANK(28, "横向填空题"),
    MULTIPLE_BLANK(29, "多项填空题"),
    SIGNATURE(30, "签名题"),
    ;

    private final int index;
    private final String text;

    QuestionType(int index,String text) {
        this.index=index;
        this.text = text;
    }
}
