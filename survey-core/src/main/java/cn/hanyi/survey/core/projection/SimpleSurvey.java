package cn.hanyi.survey.core.projection;

import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Value;
import org.befun.core.rest.view.ResourceViews;

@Value
public class SimpleSurvey {
    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String title;
    @JsonView(ResourceViews.Basic.class)
    SurveyStatus status;
}
