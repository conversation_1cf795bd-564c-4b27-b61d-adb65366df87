kind: pipeline
type: docker
name: default

platform:
  os: linux
  arch: amd64

clone:
  depth: 1

steps:
#  - name: restore-cache-with-filesystem
  #    image: meltwater/drone-cache:v1.1.0
  #    pull: if-not-exists
  #    settings:
  #      backend: "filesystem"
  #      restore: true
  #      cache_key: "{{ .Commit.Branch }}"
  #      archive_format: "gzip"
  #      filesystem_cache_root: "/cache"
  #      mount:
  #        - './.m2'
  #    volumes:
  #      - name: cache
  #        path: /cache
  #    when:
  #      ref:
  #        - refs/heads/**

  #  - name: restore-cache-with-filesystem-tags
  #    image: meltwater/drone-cache:v1.1.0
  #    pull: if-not-exists
  #    settings:
  #      backend: "filesystem"
  #      restore: true
  #      cache_key: "master"
  #      archive_format: "gzip"
  #      filesystem_cache_root: "/cache"
  #      mount:
  #        - './.m2'
  #    volumes:
  #      - name: cache
  #        path: /cache
  #    when:
  #      ref:
  #        - refs/tags/**

  - name: branch-build
    image: registry-vpc.cn-shenzhen.aliyuncs.com/hanyi-public/maven:3.6.3-openjdk-14
    pull: if-not-exists
    volumes:
      - name: m2-global-settings
        path: /root/.m2/settings.xml
      - name: backend-lite
        path: /tmp/repository
    environment:
      MAVEN_CONFIG: /drone/src/.m2
    commands:
      - mvn clean install -U -s /root/.m2/settings.xml -Dtest=cn.hanyi.survey.StartUpTest -DfailIfNoTests=false -Dmaven.repo.local=/tmp/repository
      - echo -n "${DRONE_BRANCH}" > .tags
    when:
      ref:
        - refs/heads/**

  - name: tag-build
    image: registry-vpc.cn-shenzhen.aliyuncs.com/hanyi-public/maven:3.6.3-openjdk-14
    pull: if-not-exists
    volumes:
      - name: m2-global-settings
        path: /root/.m2/settings.xml
      - name: backend-lite
        path: /tmp/repository
    commands:
      - mvn clean install -s /root/.m2/settings.xml -Dtest=cn.hanyi.survey.StartUpTest -DfailIfNoTests=false -Dmaven.repo.local=/tmp/repository
      - echo -n "${DRONE_TAG}" > .tags
    when:
      ref:
        - refs/tags/**
          #
          #  - name: rebuild-cache-with-filesystem
          #    image: meltwater/drone-cache:v1.1.0
          #    pull: if-not-exists
          #    settings:
          #      backend: "filesystem"
          #      rebuild: true
          #      cache_key: "{{ .Commit.Branch }}"
          #      archive_format: "gzip"
          #      filesystem_cache_root: "/cache"
          #      mount:
          #        - './.m2'
          #    volumes:
          #      - name: cache
          #        path: /cache
          #    when:
          #      ref:
          #        - refs/heads/**

          #- name: code-analysis
          #  image: aosapps/drone-sonar-plugin
          #  settings:
          #    sonar_host:
          #      from_secret: sonar_host
        #    sonar_token:
        #      from_secret: sonar_token

  - name: publish
    image: plugins/docker
    pull: if-not-exists
    volumes:
      - name: docker-auths
        path: /root/.docker/config.json
    settings:
      repo: registry-vpc.cn-shenzhen.aliyuncs.com/surveyplus/surveylite-backend
      registry: registry-vpc.cn-shenzhen.aliyuncs.com

  - name: publish-survey-link
    image: plugins/docker
    pull: if-not-exists
    volumes:
      - name: docker-auths
        path: /root/.docker/config.json
    settings:
      repo: registry-vpc.cn-shenzhen.aliyuncs.com/surveyplus/surveylite-backend-link
      registry: registry-vpc.cn-shenzhen.aliyuncs.com
      dockerfile: Dockerfile_survey_link

  - name: ssh commands
    image: appleboy/drone-ssh
    pull: if-not-exists
    volumes:
      - name: ssh_rsa
        path: /root/.ssh/id_rsa
    settings:
      key_path: /root/.ssh/id_rsa
      host: **********
      username: root
      script:
        - source /etc/profile
        - ifconfig ens192
        - kubectl -n survey-dev rollout restart deployment surveylite-backend
#        - kubectl -n survey-dev rollout restart deployment surveylite-backend-link
    when:
      branch:
        - develop

  - name: test trigger
    image: plugins/webhook
    pull: if-not-exists
    settings:
      mecthod: POST
      urls:
        - https://cs.console.aliyun.com/hook/trigger?token=*****************************************************************************************************************************************************************************************************************************************************************************************************
        - https://cs.console.aliyun.com/hook/trigger?token=*****************************************************************************************************************************************************************************************************************************************************************************************************
    when:
      branch:
        - test

  - name: notify
    image: fifsky/drone-wechat-work
    pull: if-not-exists
    settings:
      url: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0accac60-9d20-446f-8e8a-4f758da26b57
      msgtype: markdown
      content: |
        {{if eq .Status "success" }}
        #### 🎉 ${DRONE_REPO} 构建成功
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > [点击查看](${DRONE_BUILD_LINK})
        {{else}}
        #### ❌ ${DRONE_REPO} 构建失败
        > Commit: [${DRONE_COMMIT_MESSAGE}](${DRONE_COMMIT_LINK})
        > Author: ${DRONE_COMMIT_AUTHOR}
        > Branch: ${DRONE_BRANCH}
        > Tag: ${DRONE_TAG}
        > 请立即修复!!!
        > [点击查看](${DRONE_BUILD_LINK})
        {{end}}
    when:
      status:
        - failure
        - success

volumes:
  - name: cache
    host:
      path: /data/drone/cache
  - name: docker-auths
    host:
      path: /root/.docker/config.json
  - name: m2-global-settings
    host:
      path: /data/drone/settings.xml
  - name: ssh_rsa
    host:
      path: /root/.ssh/id_rsa
  - name: backend-lite
    host:
      path: /data/drone/cache/backend-lite

trigger:
  ref:
 #   - refs/heads/master
    - refs/heads/test
    - refs/heads/develop
    - refs/tags/**

image_pull_secrets:
  - dockerconfig
