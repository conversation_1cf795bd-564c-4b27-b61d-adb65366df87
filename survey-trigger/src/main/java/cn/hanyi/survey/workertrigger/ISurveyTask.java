package cn.hanyi.survey.workertrigger;

import cn.hanyi.survey.workertrigger.dto.*;

import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

public interface ISurveyTask {

    default List<ISurveyTaskConsumer> getConsumers() {
        return null;
    }

    private void foreachConsumers(Consumer<ISurveyTaskConsumer> consumer) {
        Optional.ofNullable(getConsumers()).ifPresent(list -> list.forEach(consumer));
    }

    default void sendChannelSurvey(SendChannelSurveyDto dto) {
        foreachConsumers(c -> c.sendChannelSurvey(dto));
    }

    default void resendChannelSurveyDelay(Long orgId, Long userId, Long surveyId, Long channelId, String jobId, long delaySeconds) {
        foreachConsumers(c -> c.resendChannelSurveyDelay(orgId, userId, surveyId, channelId, jobId, delaySeconds));
    }

    default void customerSendSms(CustomerSendSmsDto dto) {
        foreachConsumers(c -> c.customerSendSms(dto));
    }

    default void customerSendWechat(CustomerSendWechatDto dto) {
        foreachConsumers(c -> c.customerSendWechat(dto));
    }

    default void customerSendEmail(CustomerSendEmailDto dto) {
        foreachConsumers(c -> c.customerSendEmail(dto));
    }

    default void responseDownload(Long orgId, Long userId, Long taskProgressId, Long surveyId, String data) {
        foreachConsumers(c -> c.responseDownload(orgId, userId, taskProgressId, surveyId, data));
    }

    default void responseDownloadAttachment(Long orgId, Long userId, Long taskProgressId, Long surveyId, String data) {
        foreachConsumers(c -> c.responseDownloadAttachment(orgId, userId, taskProgressId, surveyId, data));
    }

    default void responseImport(Long orgId, Long userId, Long taskProgressId, Long surveyId, String data) {
        foreachConsumers(c -> c.responseImport(orgId, userId, taskProgressId, surveyId, data));
    }

    default void quotaSync(Long orgId, Long userId, TaskQuotaSyncDto data) {
        foreachConsumers(c -> c.quotaSync(orgId, userId, data));
    }

    default void translateSurvey(Long orgId,Long userId, Long surveyId, String data) {
        foreachConsumers(c -> c.translateSurvey(orgId, userId, surveyId, data));
    }

}
