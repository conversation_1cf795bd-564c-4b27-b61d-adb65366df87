package cn.hanyi.survey.workertrigger;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

public interface ISurveyEvent {

    default List<ISurveyEventConsumer> getConsumers() {
        return null;
    }

    default void foreachConsumers(Consumer<ISurveyEventConsumer> consumer) {
        Optional.ofNullable(getConsumers()).ifPresent(list -> list.forEach(consumer));
    }

    default void surveyUpdate(Long surveyId) {
        foreachConsumers(c -> c.surveyUpdate(surveyId));
    }

    default void surveyStop(Long surveyId) {
        foreachConsumers(c -> c.surveyStop(surveyId));
    }

    default void surveyPublish(Long surveyId) {
        foreachConsumers(c -> c.surveyPublish(surveyId));
    }

    default void surveyDelete(Long surveyId) {
        foreachConsumers(c -> c.surveyDelete(surveyId));
    }

    default void questionDelete(Long surveyId, Long questionId) {
        foreachConsumers(c -> c.questionDelete(surveyId, questionId));
    }

    default void questionUpdate(Long surveyId, Long questionId) {
        foreachConsumers(c -> c.questionUpdate(surveyId, questionId));
    }

    default void questionTypeUpdate(Long surveyId, Long questionId, String type) {
        foreachConsumers(c -> c.questionTypeUpdate(surveyId, questionId, type));
    }

    default void questionItemDelete(Long surveyId, Long questionId, Long itemId, String type) {
        foreachConsumers(c -> c.questionItemDelete(surveyId, questionId, itemId, type));
    }

    default void responseCreate(Long orgId, Long surveyId, Long responseId) {
        foreachConsumers(c -> c.responseCreate(orgId, surveyId, responseId));
    }

    default void responseView(Long orgId, Long surveyId, Long responseId) {
        foreachConsumers(c -> c.responseView(orgId, surveyId, responseId));
    }

    default void responseSubmit(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        foreachConsumers(c -> c.responseSubmit(orgId, surveyId, responseId, finalSubmit));
    }

    default void responseSubmitFinal(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        foreachConsumers(c -> c.responseSubmitFinal(orgId, surveyId, responseId, finalSubmit));
    }
    default void responseImport(Long orgId, Long surveyId, Long responseId, boolean finalSubmit) {
        foreachConsumers(c -> c.responseImport(orgId, surveyId, responseId, finalSubmit));
    }

    default void responseSyncDynamicItem(Long orgId, Long surveyId, Long responseId, String dynamicQuestions) {
        foreachConsumers(c -> c.responseSyncDynamicItem(orgId, surveyId, responseId, dynamicQuestions));
    }

    default void responseInvalid(Long surveyId, Long responseId) {
        foreachConsumers(c -> c.responseInvalid(surveyId, responseId));
    }

    default void responseRecover(Long surveyId, Long responseId) {
        foreachConsumers(c -> c.responseRecover(surveyId, responseId));
    }

    default void responseDelete(Long surveyId, Long responseId) {
        foreachConsumers(c -> c.responseDelete(surveyId, responseId));
    }

    default void responseDeleteBySurvey(Long surveyId) {
        foreachConsumers(c -> c.responseDeleteBySurvey(surveyId));
    }

    default void responseDeleteByChannel(Long surveyId, Long channelId) {
        foreachConsumers(c -> c.responseDeleteByChannel(surveyId, channelId));
    }

    default void channelCreate(Long surveyId, Long channelId) {
        foreachConsumers(c -> c.channelCreate(surveyId, channelId));
    }

    default void channelPause(Long surveyId, Long channelId) {
        foreachConsumers(c -> c.channelPause(surveyId, channelId));
    }

    default void channelClose(Long surveyId, Long channelId) {
        foreachConsumers(c -> c.channelClose(surveyId, channelId));
    }

    default void refundRedPack(Long orgId, Long surveyId, Long lotteryId, Long orderId, Long winnerId, String mchBillno, Duration duration) {
        foreachConsumers(c -> c.refundRedPack(orgId, surveyId, lotteryId, orderId, winnerId, mchBillno, duration));
    }

    default void sendRedPackDelay(Long orgid, Long winnerId, String openid, Duration delay) {
        foreachConsumers(c -> c.sendRedPackDelay(orgid, winnerId, openid, delay));
    }

    default void lotteryClose(Long orgId, Long userId, Long surveyId, Long lotteryId, String lotteryType) {
        foreachConsumers(c -> c.lotteryClose(orgId, userId, surveyId, lotteryId, lotteryType));
    }

    default void lotteryDelete(Long orgId, Long userId, Long surveyId, Long lotteryId, String lotteryType) {
        foreachConsumers(c -> c.lotteryDelete(orgId, userId, surveyId, lotteryId, lotteryType));
    }

    default void adminxChannelOrderRequest(Long orgId, Long userId, Long surveyId, Long channelId, String contacts, String requestTime) {
        foreachConsumers(c -> c.adminxChannelOrderRequest(orgId, userId, surveyId, channelId, contacts, requestTime));
    }

    default void surveyContentAudit(Long orgId, Long userId, Long surveyId, Long auditId) {
        foreachConsumers(c -> c.surveyContentAudit(orgId, userId, surveyId, auditId));
    }
}
