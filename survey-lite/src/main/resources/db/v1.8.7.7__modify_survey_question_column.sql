alter table cem_platform.survey_question modify column `visible_if` varchar(2000) DEFAULT NULL;
alter table cem_platform.template_survey_question modify column `visible_if` varchar(2000) DEFAULT NULL;
alter table cem_platform.survey_logic modify column `expression` varchar(2000) NOT NULL DEFAULT '';
alter table cem_platform.template_survey_logic modify column `expression` varchar(2000) NOT NULL DEFAULT '';
alter table cem_platform.survey_quota modify column `expression` varchar(2000) NOT NULL DEFAULT '' COMMENT '配额表达式';
alter table cem_platform.survey_tag modify column `expression` varchar(2000) NOT NULL COMMENT '表达式';
