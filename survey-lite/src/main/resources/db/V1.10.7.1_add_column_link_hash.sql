CREATE TABLE `link_exist` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `link_id` bigint DEFAULT NULL COMMENT '短链id',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `hash` varchar(500) DEFAULT NULL COMMENT '参数hash 参数相同就不在创建',
  PRIMARY KEY (`id`),
  KEY `link_exist_link_id_IDX` (`link_id`) USING BTREE,
  KEY `link_exist_hash_IDX` (`hash`) USING BTREE
) ENGINE=InnoDB COMMENT='短链存在表';