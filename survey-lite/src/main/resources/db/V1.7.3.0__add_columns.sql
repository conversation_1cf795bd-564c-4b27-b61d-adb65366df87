ALTER TABLE `survey`.`survey_response` ADD COLUMN `sequence` INT(10) UNSIGNED DEFAULT 0 COMMENT '答卷序号';
ALTER TABLE `survey`.survey_response_cell ADD COLUMN `t_val` datetime DEFAULT NULL COMMENT '日期格式的答案数据' AFTER `comment_val`;
ALTER TABLE `survey`.`survey_question` ADD  COLUMN `start_time` datetime  DEFAULT NULL  COMMENT '日期题验证开始时间';
ALTER TABLE `survey`.`survey_question` ADD COLUMN  `end_time` datetime DEFAULT NULL  COMMENT '日期题验证结束时间';
ALTER TABLE `survey`.`survey_question` ADD COLUMN  `adapted_mobile` tinyint NOT NULL DEFAULT '0' COMMENT '是否适配移动设备';
ALTER TABLE `survey`.`survey` ADD COLUMN enable_smart_verify TINYINT(2) default 0 COMMENT '是否开启智能验证';



CREATE TABLE `survey`.`survey_question_column` (
                       `id` bigint NOT NULL AUTO_INCREMENT,
                       `q_id` bigint DEFAULT NULL COMMENT 'question id',
                       `value` varchar(50)  NOT NULL DEFAULT '' COMMENT '数值',
                       `text` varchar(2000)  NOT NULL DEFAULT '' COMMENT '标题',
                       `visible_if` varchar(200)  DEFAULT NULL COMMENT '显示逻辑',
                       `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                       `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                       `sequence` int DEFAULT '0' COMMENT '序号',
                       PRIMARY KEY (`id`),
                       KEY `q_id` (`q_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT '问卷题型列选项属性';
