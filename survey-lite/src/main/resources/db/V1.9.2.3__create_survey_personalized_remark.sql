CREATE TABLE `cem_platform`.`survey_personalized_remark`
(
    `id`            bigint       NOT NULL AUTO_INCREMENT,
    `s_id`          bigint       NOT NULL COMMENT '问卷id',
    `normal_finish` bit          NOT NULL COMMENT '是否正常完成',
    `title`         varchar(200) NOT NULL COMMENT '个性化标题',
    `expression`    varchar(1000) NULL COMMENT '表达式',
    `type`          varchar(20)  NOT NULL COMMENT '个性化类型',
    `text`          text NULL COMMENT '图文样式',
    `link`          varchar(255) NULL COMMENT '跳转链接',
    `name`          varchar(20)  NOT NULL COMMENT '唯一值',
    `create_time`   timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`   timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
);