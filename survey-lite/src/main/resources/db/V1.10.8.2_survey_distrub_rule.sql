
DROP PROCEDURE IF EXISTS update_survey_titles;
DELIMITER //
CREATE PROCEDURE update_survey_titles()
BEGIN
    DECLARE done INT DEFAULT 0;
    DECLARE rule_id BIGINT;
    DECLARE sids_str TEXT;
    DECLARE titles_str TEXT;
    DECLARE sid_str VARCHAR(20);
    DECLARE sid_pos INT;
    DECLARE method_str VARCHAR(20);
    DECLARE days INT;

    DECLARE rule_cursor CURSOR FOR SELECT id,
                                          sids,
                                          case
                                              when disturb_method = 1 then 'OPEN'
                                              when disturb_method = 2 then 'SUBMIT'
                                              when disturb_method = 3 then 'CLOSE'
                                              else 'VISIT'
                                              END AS method_str,
                                          disturb_days  FROM survey_disturb_rule;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;

    OPEN rule_cursor;

    read_loop: LOOP
        FETCH rule_cursor INTO rule_id, sids_str, method_str, days;
        IF done THEN
            LEAVE read_loop;
        END IF;

        SET titles_str = '';
        SET sid_pos = LOCATE(';', sids_str);

        WHILE sid_pos > 0 DO
                SET sid_str = SUBSTRING(sids_str, 1, sid_pos - 1);
                SET sids_str = SUBSTRING(sids_str, sid_pos + 1);
                CALL add_title_to_titles_str(sid_str, titles_str);
                SET sid_pos = LOCATE(';', sids_str);
            END WHILE;

        -- 处理最后一个ID
        CALL add_title_to_titles_str(sids_str, titles_str);

        UPDATE survey_disturb_rule SET titles = titles_str, disturb_filter = CONCAT('[{"disturbMethod":"',method_str, '","disturbDays":', days , '}]')  WHERE id = rule_id;
    END LOOP;

    CLOSE rule_cursor;
END //
DELIMITER ;

-- 辅助存储过程，用于添加标题到 titles_str
DROP PROCEDURE IF EXISTS add_title_to_titles_str;
DELIMITER //
CREATE PROCEDURE add_title_to_titles_str(IN sid_str VARCHAR(20), INOUT titles_str TEXT)
BEGIN
    DECLARE title_str VARCHAR(255);
    DECLARE sid BIGINT;

    SET @sid = CAST(sid_str AS UNSIGNED);
    SELECT title INTO title_str FROM survey WHERE id = @sid;

    IF title_str IS NOT NULL THEN
        IF titles_str = '' THEN
            SET titles_str = title_str;
        ELSE
            SET titles_str = CONCAT(titles_str, ';', title_str);
        END IF;
    END IF;
END //
DELIMITER ;

-- 调用存储过程
CALL update_survey_titles();
