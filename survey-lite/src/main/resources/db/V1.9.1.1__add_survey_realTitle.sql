ALTER TABLE `cem_platform`.`survey`
    MODIFY COLUMN `title` varchar (200) NULL DEFAULT NULL COMMENT '问卷别名：编辑端列表显示' AFTER `status`,
    ADD COLUMN `real_title` varchar (200) NULL DEFAULT NULL COMMENT '问卷标题：答题端显示' AFTER `title`;

ALTER TABLE `cem_platform`.`template_survey`
    MODIFY COLUMN `title` varchar (200) NULL DEFAULT NULL COMMENT '问卷别名：编辑端列表显示' AFTER `status`,
    ADD COLUMN `real_title` varchar (200) NULL DEFAULT NULL COMMENT '问卷标题：答题端显示' AFTER `title`;