CREATE TABLE `cem_platform`.`survey_lottery`
(
    `id`                   bigint NOT NULL AUTO_INCREMENT,
    `s_id`                 bigint NOT NULL COMMENT '问卷id',
    `lottery_name`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抽奖渠道名称',
    `lottery_type`         int NULL DEFAULT 0 COMMENT '抽奖渠道类型',
    `status`               int NULL DEFAULT 0 COMMENT '抽奖渠道状态',
    `creator`              bigint NULL DEFAULT NULL COMMENT '抽奖渠道创建人',
    `is_time_limit`        bit(1) NULL DEFAULT b'0' COMMENT '抽奖期限',
    `start_time`           datetime NULL DEFAULT NULL COMMENT '抽奖期限开始时间',
    `end_time`             datetime NULL DEFAULT NULL COMMENT '抽奖期限结束时间',
    `is_complete`          bit(1) NULL DEFAULT b'1' COMMENT '抽奖条件-正常结束后可参与',
    `is_limit`             bit(1) NULL DEFAULT b'0' COMMENT '是否打开每日奖品限额',
    `prize_limit`          int NULL DEFAULT NULL COMMENT '每日奖品限额',
    `is_lottery_desc`      bit(1) NULL DEFAULT b'1' COMMENT '兑奖说明开关',
    `send_lottery_name`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发奖人姓名',
    `send_lottery_contact` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发奖人联系方式',
    `lottery_desc`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抽奖说明',
    `create_time`          TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`          TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
);


CREATE TABLE `cem_platform`.`survey_lottery_prize`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `lottery_id`  bigint NOT NULL,
    `prize_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '奖品名称',
    `type`        int NULL DEFAULT 0 COMMENT '奖品类型',
    `is_prize`    bit(1) NULL DEFAULT b'0' COMMENT '是否是奖品',
    `sequence`    int NULL DEFAULT NULL COMMENT '奖品排序',
    `image`       varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
    `number`      int NULL DEFAULT NULL COMMENT '奖品数量',
    `winner_num`  int NULL DEFAULT 0 COMMENT '已中奖数量',
    `percent`     int NULL DEFAULT NULL COMMENT '中奖概率',
    `codes`       text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '兑换码',
    `is_address`  bit(1) NULL DEFAULT b'0' COMMENT '兑换地址开关',
    `address`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '兑奖地址',
    `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX         `FK_SURVEYLOTTERYPRIZE_ON_LOTTERY`(`lottery_id`) USING BTREE
);

CREATE TABLE `cem_platform`.`survey_lottery_prize_winner`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `lottery_id`  bigint NOT NULL COMMENT '抽奖活动渠道id',
    `prize_id`    bigint NOT NULL COMMENT '奖品id',
    `response_id` bigint NOT NULL COMMENT '答卷id',
    `type`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中奖类型：实物，虚拟',
    `prize_name`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '奖品名称',
    `name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人',
    `phone`       varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人号码',
    `province`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收获地址：省',
    `city`        varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收获地址：市',
    `district`    varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货地址：区/县',
    `address`     varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '实物收货详细地址',
    `prize_code`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '奖品兑换码',
    `status`      varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '奖品发放状态',
    `send_time`   timestamp NULL DEFAULT NULL COMMENT '奖品发放时间',
    `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
);
