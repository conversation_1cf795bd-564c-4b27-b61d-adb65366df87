create table survey_disturb_rule(
                    `id`       BIGINT NOT NULL AUTO_INCREMENT,
                    `org_id`   BIGINT DEFAULT NULL comment '企业orgId',
                    `user_id`  BIGINT DEFAULT NULL comment 'userId',
                    `name`     varchar(20) NOT NULL COMMENT '规则名',
                    `sids`    varchar(1000) NOT NULL COMMENT '免打扰问卷id',
                    `channels` varchar(100) NOT NULL COMMENT '免打扰渠道',
                    `disturb_type` TINYINT(2) NOT NULL COMMENT '免打扰类型，0按时间 1按次数',
                    `disturb_method` TINYINT(2) NOT NULL COMMENT '免打扰方式，0弹出 1提交',
                    `disturb_days` int(10) NOT NULL DEFAULT 0 COMMENT '天数',
                    `status` int NOT NULL DEFAULT '0' COMMENT '状态 0停用 1启用',
                    `editor_id`  BIGINT DEFAULT NULL comment '编辑者id',
                    `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                    `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                    PRIMARY KEY (`id`),
                    key idx_org_id_status(`org_id`,`status`)
)ENGINE = innodb default charset = utf8mb4 COMMENT '免打扰规则';