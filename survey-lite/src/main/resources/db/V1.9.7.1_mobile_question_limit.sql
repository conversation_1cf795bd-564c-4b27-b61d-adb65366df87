ALTER TABLE `cem_platform`.`survey_question`
ADD COLUMN `is_verify_mobile` TINYINT NULL COMMENT '手机题配置项-短信验证' AFTER `alias`,
ADD COLUMN `is_link_customer` TINYINT NULL COMMENT '手机题配置项-客户中心关联' AFTER `is_verify_mobile`,
ADD COLUMN `is_deduplication` TINYINT NULL COMMENT '手机题配置项-手机号去重' AFTER `is_link_customer`;

ALTER TABLE `cem_platform`.`template_survey_question`
ADD COLUMN `is_verify_mobile` TINYINT NULL COMMENT '手机题配置项-短信验证' AFTER `alias`,
ADD COLUMN `is_link_customer` TINYINT NULL COMMENT '手机题配置项-客户中心关联' AFTER `is_verify_mobile`,
ADD COLUMN `is_deduplication` TINYINT NULL COMMENT '手机题配置项-手机号去重' AFTER `is_link_customer`;