CREATE TABLE `response_shared` (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `org_id` bigint DEFAULT NULL COMMENT '机构id',
                                   `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                   `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                   `s_id` bigint NOT NULL COMMENT '问卷id',
                                   `r_id` bigint DEFAULT NULL COMMENT '答卷id',
                                   `token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '鉴权token',
                                   `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
                                   `config_id` bigint NOT NULL,
                                   PRIMARY KEY (`id`) USING BTREE,
                                   KEY `s_r_id` (`s_id`,`r_id`),
                                   KEY `org_id` (`org_id`),
                                   <PERSON><PERSON>Y `token` (`token`)
) ;

CREATE TABLE `response_shared_config` (
                                          `id` bigint NOT NULL AUTO_INCREMENT,
                                          `org_id` bigint DEFAULT NULL COMMENT '机构id',
                                          `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                          `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                                          `s_id` bigint NOT NULL COMMENT '问卷id',
                                          `enable` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启共享',
                                          `with_token` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启鉴权token',
                                          `hide_qid` tinyint NOT NULL DEFAULT '0' COMMENT '是否隐藏题目id',
                                          `qids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '隐藏题目id 1;2;3',
                                          PRIMARY KEY (`id`) USING BTREE,
                                          KEY `org_id` (`org_id`),
                                          KEY `s_id` (`s_id`)
) ;

