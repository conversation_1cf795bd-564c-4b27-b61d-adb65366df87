ALTER TABLE `cem_platform`.`survey_channel`
ADD COLUMN `order_refund` TEXT NULL COMMENT '退款明细' AFTER `reject_message`;

ALTER TABLE `adminx`.`sample_order`
ADD COLUMN `recycle` INT NULL COMMENT '回收数量' AFTER `quantity`;

ALTER TABLE `adminx`.`sample_order`
ADD COLUMN `refund_price` VARCHAR(45) NULL COMMENT '退款金额' AFTER `total_price`;

update adminx.sample_order so set so.recycle =
ifnull((select dt.audit_count from adminx.delivery_task dt where dt.type=0 and dt.s_id=so.s_id and dt.channel_id=so.channel_id limit 1),0)
where so.channel_status = 3 and so.id > 0;