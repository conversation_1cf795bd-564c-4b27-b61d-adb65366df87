ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `button_border_color` varchar(20) NOT NULL DEFAULT '#234154' COMMENT '默认按钮描边 ' AFTER `tag_color`;

ALTER TABLE `cem_platform`.`template_survey`
    ADD COLUMN `button_border_color` varchar(20) NOT NULL DEFAULT '#234154' COMMENT '默认按钮描边 ' AFTER `tag_color`;


ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `brand_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '问卷底部logo' AFTER `embed_back_ground`;

ALTER TABLE `cem_platform`.`template_survey`
    ADD COLUMN `brand_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '问卷底部logo' AFTER `embed_back_ground`;