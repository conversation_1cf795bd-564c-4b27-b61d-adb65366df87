ALTER TABLE `cem_platform`.`survey_question`
ADD COLUMN `is_dynamic_item` TINYINT NULL COMMENT '是否启用动态选项' AFTER `is_deduplication`;
ALTER TABLE `cem_platform`.`survey_question_item`
ADD COLUMN `is_dynamic` TINYINT NULL DEFAULT 0 COMMENT '是否为动态选项' AFTER `is_required`;

ALTER TABLE `cem_platform`.`template_survey_question`
ADD COLUMN `is_dynamic_item` TINYINT NULL COMMENT '是否启用动态选项' AFTER `is_deduplication`;
ALTER TABLE `cem_platform`.`template_survey_question_item`
ADD COLUMN `is_dynamic` TINYINT NULL DEFAULT 0 COMMENT '是否为动态选项' AFTER `is_required`;

ALTER TABLE `cem_platform`.`survey_question_item`
CHANGE COLUMN `create_time` `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间' ,
CHANGE COLUMN `modify_time` `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间' ;

ALTER TABLE `cem_platform`.`link`
ADD COLUMN `source` INT NULL DEFAULT 1 COMMENT '短链来源：1 server 2 api' AFTER `params`;
ALTER TABLE `cem_platform`.`link`
ADD INDEX `idx_source` (`source` ASC, `id` ASC);