CREATE TABLE `template_group`
(
  `id` bigint NOT NULL AUTO_INCREMENT,
`title` varchar(200) CHARACTER
SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '组名',
`org_id` bigint DEFAULT NULL COMMENT '组织id',
`editable` tinyint(1)DEFAULT '1' COMMENT '可否编辑 0：不能 1：可以',
`create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
`type` tinyint DEFAULT '0' COMMENT '模板类型0：survey 1：question',
`sequence` int DEFAULT '0' COMMENT '排序',
PRIMARY KEY(`id`)
)ENGINE = InnoDB AUTO_INCREMENT=1689236842097665 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='问卷库分组表';

CREATE TABLE `template_survey`
(
  `id`      bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL COMMENT '用户id',
  `version` bigint NOT NULL DEFAULT '0' COMMENT '版本号',
  `status`  int NOT NULL DEFAULT '0' COMMENT '问卷状态 0已停用 1收集中 2统计分析中 3统计完成',
`title` varchar(200) CHARACTER
SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标题',
`create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
`modify_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
`org_id` bigint DEFAULT NULL COMMENT '组织id',
`main_color` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#234154' COMMENT '主色',
`show_title` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示标题',
`show_logo` tinyint NOT NULL DEFAULT '0' COMMENT '是否显示logo',
`logo_fit` int NOT NULL DEFAULT '0' COMMENT 'logo对齐',
`logo_position` int NOT NULL DEFAULT '0' COMMENT 'logo位置',
`logo` varchar(100)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'logo地址',
`font_size` varchar(10)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'medium' COMMENT '字体颜色',
`title_color` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#333333' COMMENT '标题颜色',
`welcoming_color` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#595959' COMMENT '欢迎语色',
`question_color` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#333333' COMMENT '问题颜色',
`item_color` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#4D4D4D' COMMENT '选项颜色',
`button_color` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#234154' COMMENT '按钮颜色',
`show_previous_button` tinyint NOT NULL DEFAULT '0' COMMENT '是否显示上一题',
`show_progress_bar` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示进度条',
`language` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'zh-cn' COMMENT '语言',
`redirect_url` varchar(100)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '重定向路径',
`show_brand` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示品牌',
`page_mode` int NOT NULL DEFAULT '0' COMMENT '分页模式',
`show_page_numbers` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示页码',
`show_question_numbers` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示问题编码',
`question_number_mode` int NOT NULL DEFAULT '0' COMMENT '问题编码方式',
`show_header` tinyint NOT NULL DEFAULT '0' COMMENT '是否显示表头',
`header_image_pc` varchar(100)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表头图片',
`header_image_mobile` varchar(100)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '表头图片',
`background_image` varchar(100)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '背景图片',
`background_color` varchar(20)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#FFFFFF' COMMENT
'背景颜色',
`background_opacity` double NOT NULL DEFAULT '1' COMMENT '背景透明度',
`enable_quota` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启配额',
`quota_status` tinyint NOT NULL DEFAULT '0' COMMENT '配额计算状态 1计算中 2计算完成',
`enable_redirect` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启重定向',
`show_background` tinyint NOT NULL DEFAULT '0' COMMENT '是否显示背景',
`deleted` bit(1)NOT NULL DEFAULT b'0' COMMENT '是否删除',
`welcoming_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '欢迎语',
`concluding_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '结束语',
`show_welcoming` bit(1)NOT NULL DEFAULT b'1' COMMENT '是否显示欢迎语',
`show_concluding` bit(1)NOT NULL DEFAULT b'1' COMMENT '是否显示结束语',
`abnormal_concluding_remark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '非正常结束语',
`enable_ip_limit` bit(1)NOT NULL DEFAULT b'0' COMMENT '是否开启IP限制',
`enable_device_limit` bit(1)NOT NULL DEFAULT b'0' COMMENT '是否开启设备限制',
`inject_type` varchar(2)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '嵌入类型',
`embed_padding_width` varchar(10)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '0px' COMMENT '嵌入手机',
`embed_height` varchar(10)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '100%' COMMENT '嵌入高度',
`embed_height_mode` tinyint DEFAULT '0' COMMENT '嵌入高度模式',
`embed_align` tinyint DEFAULT '3' COMMENT '嵌入位置',
`embed_vertical_align` tinyint DEFAULT '4' COMMENT '嵌入顶部位置',
`embed_type` varchar(2)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '嵌入类型',
`embed_delay` int DEFAULT '10' COMMENT '嵌入延迟显示等待时长',
`gap_time` int DEFAULT '1' COMMENT '嵌入每 xx 时间显示一次',
`gap_type` tinyint DEFAULT '0' COMMENT '嵌入间隔时间',
`gap_check` tinyint(1)DEFAULT '0' COMMENT '嵌入是否选择关联设置',
`gap_first_check` tinyint(1)DEFAULT '0' COMMENT '嵌入是否距首次访问',
`gap_day` int DEFAULT '1' COMMENT '距首次访问超过',
`gap_custom_check` tinyint(1)DEFAULT '0' COMMENT '嵌入是否选择关联条件',
`gap_custom_time` int DEFAULT '1' COMMENT '嵌入每 xx 时间显示一次',
`gap_custom_type` tinyint DEFAULT '0' COMMENT '嵌入时间类型',
`frequency` tinyint DEFAULT '0' COMMENT '显示频率',
`repeat_action` tinyint DEFAULT '0' COMMENT '重复周期行为',
`once_action` tinyint DEFAULT '0' COMMENT '单次周期行为',
`enable_smart_verify` tinyint DEFAULT '0' COMMENT '是否开启智能验证',
`enable_response_limit` tinyint(1)DEFAULT '0' COMMENT '是否开启回收限制',
`response_amount` int DEFAULT NULL COMMENT '回收限制数量',
`group_id` bigint DEFAULT NULL COMMENT '模板库id',
`editable` tinyint DEFAULT '1' COMMENT '可否编辑 0：不能 1：可以',
PRIMARY KEY(`id`)
USING BTREE,
KEY `id`(`id`),
KEY `org_user_id`(`org_id`, `user_id`),
KEY `org_id`(`id`, `org_id`),
KEY `id_user_org_id`(`id`, `user_id`, `org_id`),
KEY `modify_time`(`id`, `user_id`, `org_id`, `modify_time`),
KEY `id_deleted`(`id`, `deleted`),
KEY `org_deleted_id`(`id`, `org_id`, `deleted`),
KEY `org_user_deleted_id`(`org_id`, `user_id`, `deleted`),
KEY `id_user_org_deleted_id`(`id`, `user_id`, `org_id`, `deleted`),
KEY `modify_time_deleted`(`id`, `user_id`, `org_id`, `modify_time`, `deleted`)
)ENGINE = InnoDB AUTO_INCREMENT=1689129547837441 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `template_survey_logic`
(
  `id`   bigint NOT NULL AUTO_INCREMENT,
  `s_id` bigint DEFAULT NULL,
`q_names` varchar(200) CHARACTER
SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
`expression` varchar(500)CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
`type` int NOT NULL DEFAULT '0',
`target` varchar(100)CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
`create_time` timestamp NULL DEFAULT NULL,
`modify_time` timestamp NULL DEFAULT NULL,
PRIMARY KEY(`id`),
KEY `id`(`id`),
KEY `s_id`(`s_id`),
KEY `id_s_id`(`id`, `s_id`)
)ENGINE = InnoDB AUTO_INCREMENT=1649676586907650 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `template_survey_question`
(
  `id`   bigint NOT NULL AUTO_INCREMENT,
  `s_id` bigint DEFAULT NULL COMMENT 'survey id',
`name` varchar(100) CHARACTER
SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '名称',
`title` varchar(2000)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标题',
`type` int DEFAULT '0' COMMENT '类型',
`is_required` tinyint NOT NULL DEFAULT '0' COMMENT '是否必填',
`create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
`modify_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
`visible_if` varchar(500)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '显示逻辑表达式',
`min` int DEFAULT NULL COMMENT '最小值',
`max` int DEFAULT NULL COMMENT '最大值',
`labels` varchar(1000)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '标签',
`min_length` int DEFAULT NULL COMMENT '最小长度',
`max_length` int DEFAULT NULL COMMENT '最大长度',
`decimal_places` int DEFAULT NULL,
`show_label` tinyint NOT NULL DEFAULT '1' COMMENT '是否显示标签',
`inapplicable` tinyint NOT NULL DEFAULT '0' COMMENT '是否不适用',
`inapplicable_label` varchar(10)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '不适用标签',
`input_type` int DEFAULT NULL COMMENT '输入类型',
`is_nps` tinyint NOT NULL DEFAULT '0' COMMENT '是否NPS',
`validator_type` int DEFAULT NULL,
`sequence` int DEFAULT '0' COMMENT '序号',
`enable_validator` bit(1)NOT NULL DEFAULT b'0',
`remark` varchar(300)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
`random_limit` int DEFAULT NULL COMMENT '随机限制',
`items_order` int NOT NULL DEFAULT '0' COMMENT '选项顺序',
`has_other` bit(1)NOT NULL DEFAULT b'0' COMMENT '是否有其他选项',
`other_label` varchar(500)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '其他选项标签',
`exclude_other` bit(1)NOT NULL DEFAULT b'0',
`exclude_other_label` bit(1)DEFAULT NULL,
`show_type` bit(1)NOT NULL DEFAULT b'0',
`code` varchar(100)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '问题编码',
`area_type` int DEFAULT NULL COMMENT '地区类型',
`start_time` datetime DEFAULT NULL COMMENT '日期题验证开始时间',
`end_time` datetime DEFAULT NULL COMMENT '日期题验证结束时间',
`adapted_mobile` tinyint NOT NULL DEFAULT '0' COMMENT '是否适配移动设备',
`step_length` int DEFAULT NULL COMMENT '刻度步长',
`group_id` bigint DEFAULT NULL COMMENT '模板库id',
`editable` tinyint DEFAULT '1' COMMENT '可否编辑 0：不能 1：可以',
`user_id` bigint DEFAULT NULL,
`org_id` bigint DEFAULT NULL,
`configure` varchar(1000)COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '问卷题型配置项',
PRIMARY KEY(`id`)
USING BTREE,
KEY `s_id`(`s_id`)
)ENGINE = InnoDB AUTO_INCREMENT=1689129549541378 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

CREATE TABLE `template_survey_question_column`
(
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `q_id`        bigint DEFAULT NULL COMMENT 'question id',
  `value`       varchar(50) NOT NULL DEFAULT '' COMMENT '数值',
  `text`        varchar(2000) NOT NULL DEFAULT '' COMMENT '标题',
  `visible_if`  varchar(200) DEFAULT NULL COMMENT '显示逻辑',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NULL DEFAULT NULL ON
UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
`sequence` int DEFAULT '0' COMMENT '序号',
PRIMARY KEY(`id`),
KEY `q_id`(`q_id`)
)ENGINE = InnoDB AUTO_INCREMENT = 1649674205853698 DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT =
'问卷题型列选项属性';

CREATE TABLE `template_survey_question_item`
(
  `id`   bigint NOT NULL AUTO_INCREMENT,
  `q_id` bigint NOT NULL COMMENT 'question id',
`value` varchar(50) CHARACTER
SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '数值',
`text` varchar(2000)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标题',
`visible_if` varchar(200)CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '显示逻辑',
`create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
`modify_time` timestamp NULL DEFAULT NULL COMMENT '修改时间',
`sequence` int DEFAULT '0' COMMENT '序号',
`exclude_other` bit(1)NOT NULL DEFAULT b'0' COMMENT '是否排他',
PRIMARY KEY(`id`),
KEY `id`(`id`),
KEY `q_id`(`q_id`),
KEY `id_q_id`(`id`, `q_id`)
)ENGINE = InnoDB AUTO_INCREMENT=1689129549475853 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;