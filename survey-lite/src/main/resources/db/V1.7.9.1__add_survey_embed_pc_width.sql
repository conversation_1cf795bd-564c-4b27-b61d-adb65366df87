ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `embed_pc_width` int NULL COMMENT '嵌入pc' AFTER `embed_padding_width`;

ALTER TABLE `cem_platform`.`survey_question`
    ADD COLUMN `place_holder` varchar(255) NULL COMMENT '文本题提示输入' AFTER `is_score`;

ALTER TABLE `cem_platform`.`survey_question`
    ADD COLUMN `item_layout` int NULL DEFAULT NULL COMMENT '选项布局' AFTER `place_holder`;


ALTER TABLE `cem_platform`.`template_survey`
    ADD COLUMN `embed_pc_width` int NULL COMMENT '嵌入pc' AFTER `embed_padding_width`;

ALTER TABLE `cem_platform`.`template_survey_question`
    ADD COLUMN `place_holder` varchar(255) NULL COMMENT '文本题提示输入' AFTER `is_score`;

ALTER TABLE `cem_platform`.`template_survey_question`
    ADD COLUMN `item_layout` int NULL DEFAULT NULL COMMENT '选项布局' AFTER `place_holder`;
