ALTER TABLE `cem_platform`.`survey_lottery`
    ADD COLUMN `is_verify` bit(1) NULL DEFAULT 1 COMMENT '是否审核后发放' AFTER `lottery_desc`;

ALTER TABLE `cem_platform`.`survey_lottery_prize`
    MODIFY COLUMN `number` int NULL DEFAULT NULL COMMENT '奖品数量、红包个数' AFTER `image`,
    ADD COLUMN `money` int NULL COMMENT '单个红包金额' AFTER `number`,
    ADD COLUMN `winner_money` int NULL DEFAULT 0 COMMENT '已中奖红包金额' AFTER `money`,
    ADD COLUMN `total_money` int NULL COMMENT '红包总金额' AFTER `winner_money`,
    MODIFY COLUMN `percent` int NULL COMMENT '中奖概率' AFTER `winner_num`;

ALTER TABLE `cem_platform`.`survey_lottery`
    ADD COLUMN `is_pay` bit(1) NULL DEFAULT 0 COMMENT '红包渠道是否支付' AFTER `lottery_desc`;