create table survey_disturb_record(
                  `id`    BIGINT NOT NULL AUTO_INCREMENT,
                  `org_id`  BIGINT DEFAULT NULL COMMENT '企业id',
                  `s_id`  BIGINT NOT NULL COMMENT '问卷id',
                  `euid`  varchar(50) NOT NULL comment 'externalUserId',
                  `disturb_method`  TINYINT(2) NOT NULL COMMENT '类型，0弹出 1提交',
                  `channel_id`  BIGINT DEFAULT NULL COMMENT '渠道id',
                  `client_id`  varchar(50) DEFAULT NULL COMMENT 'clientId',
                  `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                  `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                  PRIMARY KEY (`id`),
                  key idx_org_euid_sid(`org_id`,`euid`,`s_id`)
)ENGINE = innodb default charset = utf8mb4 COMMENT '免打扰问卷请求记录';