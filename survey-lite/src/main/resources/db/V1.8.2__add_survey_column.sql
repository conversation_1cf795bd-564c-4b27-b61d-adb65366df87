ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `tag_color` varchar(20) NOT NULL DEFAULT '#666666' COMMENT '标签颜色' AFTER `item_color`,
    ADD COLUMN `default_tag_padding` varchar(20) NOT NULL DEFAULT '#F8F9FB' COMMENT '默认标签填充' AFTER `tag_color`,
    ADD COLUMN `default_tag_stroke` varchar(20) NOT NULL DEFAULT '#E8EEF4' COMMENT '默认标签描边' AFTER `default_tag_padding`,
    ADD COLUMN `selected_tag_padding` varchar(20) NOT NULL DEFAULT '#F5F9FF' COMMENT '选中标签填充' AFTER `default_tag_stroke`,
    ADD COLUMN `selected_tag_stroke` varchar(20) NOT NULL DEFAULT '#E1EDFE' COMMENT '选中标签描边' AFTER `selected_tag_padding`;

ALTER TABLE `cem_platform`.`template_survey`
    ADD COLUMN `tag_color` varchar(20) NOT NULL DEFAULT '#666666' COMMENT '标签颜色' AFTER `item_color`,
    ADD COLUMN `default_tag_padding` varchar(20) NOT NULL DEFAULT '#F8F9FB' COMMENT '默认标签填充' AFTER `tag_color`,
    ADD COLUMN `default_tag_stroke` varchar(20) NOT NULL DEFAULT '#E8EEF4' COMMENT '默认标签描边' AFTER `default_tag_padding`,
    ADD COLUMN `selected_tag_padding` varchar(20) NOT NULL DEFAULT '#F5F9FF' COMMENT '选中标签填充' AFTER `default_tag_stroke`,
    ADD COLUMN `selected_tag_stroke` varchar(20) NOT NULL DEFAULT '#E1EDFE' COMMENT '选中标签描边' AFTER `selected_tag_padding`;