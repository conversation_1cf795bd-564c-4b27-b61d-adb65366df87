ALTER table cem_platform.survey ADD COLUMN enable_behavior TINYINT(1) default '0' COMMENT '是否开启行为记录 0未开启 1开启';
ALTER table cem_platform.template_survey ADD COLUMN enable_behavior TINYINT(1) default '0' COMMENT '是否开启行为记录 0未开启 1开启';

CREATE TABLE cem_platform.`survey_behavior_record` (
                           `id` bigint NOT NULL AUTO_INCREMENT,
                           `r_id` bigint NOT NULL COMMENT '答卷id',
                           `page` int NOT NULL DEFAULT '0' COMMENT '问卷页码序号',
                           `s_id` bigint DEFAULT NULL COMMENT '问卷id',
                           `q_codes` varchar(200) DEFAULT NULL COMMENT '每页的question code',
                           `duration` int NOT NULL DEFAULT '0' COMMENT '每页停留的时间（毫秒）',
                           `return_times` int NOT NULL DEFAULT '0' COMMENT '返回次数',
                           `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                           `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                           PRIMARY KEY (`id`) USING BTREE,
                           UNIQUE `r_id_page` (`r_id`,`page`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci comment '行为记录';