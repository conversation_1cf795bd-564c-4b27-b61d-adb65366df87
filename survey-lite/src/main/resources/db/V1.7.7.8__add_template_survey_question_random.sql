CREATE TABLE cem_platform.template_survey_question_random (
                             `id` bigint NOT NULL AUTO_INCREMENT,
                             `s_id` bigint NOT NULL COMMENT '问卷id',
                             `q_names` varchar(500) NOT NULL default '' COMMENT 'question name List',
                             `num` int(10) NOT NULL default '0' COMMENT '随机显示的问题个数',
                             `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                             `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
                             PRIMARY KEY (`id`),
                             KEY `idx_sid` (`s_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1978517702440961 DEFAULT CHARSET=utf8mb4 Comment '问题随机';