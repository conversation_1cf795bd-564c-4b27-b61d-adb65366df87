CREATE TABLE `cem_platform`.`survey_tag`
(
    `id`          bigint       NOT NULL AUTO_INCREMENT,
    `name`        varchar(10)  NOT NULL COMMENT '标签名称',
    `expression`  varchar(500) NOT NULL COMMENT '表达式',
    `s_id`        bigint       NOT NULL COMMENT '问卷id',
    `create_time` timestamp    NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp    NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
);


ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `enable_tag` tinyint NOT NULL DEFAULT 0 COMMENT '是否开启答卷标签' AFTER `enable_quota`;

ALTER TABLE `cem_platform`.`template_survey`
    ADD COLUMN `enable_tag` tinyint NOT NULL DEFAULT 0 COMMENT '是否开启答卷标签' AFTER `enable_quota`;


ALTER TABLE `cem_platform`.`survey_response`
    ADD COLUMN `tags` varchar(500) NULL COMMENT '答卷标签';