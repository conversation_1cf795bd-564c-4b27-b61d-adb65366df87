CREATE TABLE `cem_platform`.`survey_verify`
(
    `id`          BIGINT NOT NULL AUTO_INCREMENT,
    `s_id`        BIGINT NOT NULL COMMENT '问卷id',
    `user_id`     BIGINT NOT NULL COMMENT '用户id',
    `version`     INT DEFAULT 0 COMMENT '问卷审核版本号',
    `status`      TINYINT NULL COMMENT '操作: 驳回、通过',
    `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
);