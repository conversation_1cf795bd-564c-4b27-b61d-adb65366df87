----------------滑动条加总值提前上线-------------------------
ALTER TABLE `cem_platform`.`survey_question`
    ADD COLUMN `enable_total` bit(1) NOT NULL DEFAULT 0 COMMENT '是否打开加总值' AFTER `enable_validator`,
    ADD COLUMN `total_type` tinyint NULL DEFAULT 0 COMMENT '加总值枚举' AFTER `enable_total`,
    ADD COLUMN `total_value` double (20, 10) NULL DEFAULT NULL COMMENT '加总值数值' AFTER `total_type`;


ALTER TABLE `cem_platform`.`template_survey_question`
    ADD COLUMN `enable_total` bit(1) NOT NULL DEFAULT 0 COMMENT '是否打开加总值' AFTER `enable_validator`,
    ADD COLUMN `total_type` tinyint NULL DEFAULT 0 COMMENT '加总值枚举' AFTER `enable_total`,
    ADD COLUMN `total_value` double (20, 10) NULL DEFAULT NULL COMMENT '加总值数值' AFTER `total_type`;
--------------------------------------------------------