ALTER TABLE `cem_platform`.`survey_question`
    ADD COLUMN `other_country_number` bit(1) NULL default 0 COMMENT '是否开启国际/港澳台号码' AFTER `show_tags`,
    ADD COLUMN `area_code` varchar(16) NULL DEFAULT 'CN'  COMMENT '默认区号' AFTER `other_country_number`;


ALTER TABLE `cem_platform`.`template_survey_question`
    ADD COLUMN `other_country_number` bit(1) NULL default 0 COMMENT '是否开启国际/港澳台号码' AFTER `show_tags`,
    ADD COLUMN `area_code` varchar(16) NULL DEFAULT 'CN' COMMENT '默认区号' AFTER `other_country_number`;

alter table cem_platform.survey_response modify column ip varchar (100) default null comment 'ip' after `euid`;


ALTER TABLE `cem_platform`.`survey`
    MODIFY COLUMN `logo` varchar (200) NOT NULL DEFAULT '' AFTER `logo_position`,
    MODIFY COLUMN `header_image_pc` varchar (200) NULL DEFAULT NULL AFTER `show_header`,
    MODIFY COLUMN `header_image_mobile` varchar (200) NULL DEFAULT NULL AFTER `header_image_pc`,
    MODIFY COLUMN `background_image` varchar (200) NULL DEFAULT NULL AFTER `header_image_mobile`;

ALTER TABLE `cem_platform`.`template_survey`
    MODIFY COLUMN `logo` varchar (200) NOT NULL DEFAULT '' COMMENT 'logo地址' AFTER `logo_position`,
    MODIFY COLUMN `header_image_pc` varchar (200) NULL DEFAULT NULL COMMENT '表头图片' AFTER `show_header`,
    MODIFY COLUMN `header_image_mobile` varchar (200) NULL DEFAULT NULL COMMENT '表头图片' AFTER `header_image_pc`,
    MODIFY COLUMN `background_image` varchar (200) NULL DEFAULT NULL COMMENT '背景图片' AFTER `header_image_mobile`;