ALTER TABLE survey_lottery_prize ADD is_imporve_award_info bit(1) DEFAULT b'1' NULL COMMENT '领奖信息';
ALTER TABLE survey_question_column ADD configure varchar(1000) NULL COMMENT '配置';
ALTER TABLE template_survey_question_column ADD configure varchar(1000) NULL COMMENT '配置';
ALTER TABLE survey_question_item ADD group_code varchar(10) NULL COMMENT '题组code';
ALTER TABLE template_survey_question_item ADD group_code varchar(10) NULL COMMENT '题组code';
ALTER TABLE survey_question ADD is_items_group bit(1) DEFAULT b'0' NULL COMMENT '选项分组';
ALTER TABLE template_survey_question ADD is_items_group bit(1) DEFAULT b'0' NULL COMMENT '选项分组';
ALTER TABLE survey_question_column DROP FOREIGN KEY FKqoxgfjaic4619s5vylmlc1hep;

