ALTER TABLE `survey_channel` CHANGE enable_data_limit  `enable_date_limit` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启调查日期限制';
ALTER TABLE `survey_channel` ADD COLUMN  `enable_wechat_reply_only` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启每个微信号只填答一次限制' AFTER `enable_date_limit`;
ALTER TABLE `survey_channel` ADD COLUMN  `enable_wechat_authorization` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否开启获取微信授权方式' AFTER `enable_wechat_reply_only`;
ALTER TABLE `survey_channel` ADD COLUMN  `wechat_provider` TINYINT(1) NOT NULL DEFAULT '1' COMMENT '授权微信选择 0绑定的微信服务号 1体验家官方服务号' AFTER `enable_wechat_authorization`;
ALTER TABLE `survey_channel` ADD COLUMN  `wechat_authorize_type` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '微信授权方式 0静默授权 1用户必需授权' AFTER `wechat_provider`;
ALTER TABLE `survey_question` ADD COLUMN `configure` VARCHAR(1000) DEFAULT NULL COMMENT '问卷题型配置项';


