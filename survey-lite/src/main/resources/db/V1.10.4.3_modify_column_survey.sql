alter table cem_platform.survey
    modify column background_color varchar(50) not null default '#FFFFFF' comment '背景颜色',
    modify column title_color varchar(50) not null default '#333333' comment '标题文本颜色',
    modify column welcoming_color varchar(50) not null default '#595959' comment '欢迎文本颜色',
    modify column question_color varchar(50) not null default '#333333' comment '题干文本颜色',
    modify column item_color varchar(50) not null default '#4D4D4D' comment '选项颜色',
    modify column tag_color varchar(50) not null default '#666666' comment '标签颜色',
    modify column default_tag_padding varchar(50) not null default '#F8F9FB' comment '默认标签填充',
    modify column default_tag_stroke varchar(50) not null default '#E8EEF4' comment '默认标签描边',
    modify column selected_tag_padding varchar(50) not null default 'rgba(49,129,246,0.1)' comment '选中标签填充',
    modify column selected_tag_stroke varchar(50) not null default '#3181F6' comment '选中标签描边',
    modify column button_color varchar(50) not null default '#234154' comment '按钮颜色',
    modify column main_color varchar(50)  not null default '#234154' comment '主色';