CREATE TABLE `cem_platform`.`survey_random_result`
(
    `id`          bigint NOT NULL,
    `survey_id`   bigint NOT NULL,
    `response_id` bigint NOT NULL,
    `question_id` bigint NOT NULL,
    `type`        int NULL,
    `is_show`     bit NULL,
    `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
);

ALTER TABLE `cem_platform`.`survey_behavior_record`
ADD INDEX `sid_rid_page` (`s_id` ASC, `r_id` ASC, `page` ASC);

ALTER TABLE `cem_platform`.`survey_random_result`
ADD INDEX `sid_rid` (`survey_id` ASC, `response_id` ASC);

ALTER TABLE `cem_platform`.`survey_send_record`
<PERSON><PERSON><PERSON> COLUMN `content` `content` TEXT NULL COMMENT '推送内容' ;