ALTER TABLE cem_platform.survey ADD COLUMN embed_back_ground TINYINT(1) DEFAULT '0' COMMENT '弹窗背景, 默认false,启用true';

CREATE TABLE cem_platform.survey_question_random (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `s_id` bigint NOT NULL COMMENT '问卷id',
  `q_ids` varchar(500) NOT NULL default '' COMMENT '问题列表 qid List',
  `num` int(10) NOT NULL default '0' COMMENT '随机显示的问题个数',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_sid` (`s_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1978517702440961 DEFAULT CHARSET=utf8mb4 Comment '问题随机';