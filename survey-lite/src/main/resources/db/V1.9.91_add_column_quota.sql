ALTER TABLE `cem_platform`.`survey_quota`
    ADD COLUMN `channel_type` int NULL DEFAULT 0 COMMENT '配额渠道类型，0：体验家配额，1，调研家社区配额' AFTER `expression_hash`;

ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `enable_adminx_quota` tinyint NOT NULL DEFAULT 0 COMMENT 'adminx社区配额开关' AFTER `enable_quota`;

ALTER TABLE `cem_platform`.`template_survey`
    ADD COLUMN `enable_adminx_quota` tinyint NOT NULL DEFAULT 0 COMMENT 'adminx社区配额开关' AFTER `enable_quota`;


ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `adminx_quota_status` tinyint NOT NULL DEFAULT 0 COMMENT 'adminx社区配额状态  1计算中 2计算完成' AFTER `quota_status`;

ALTER TABLE `cem_platform`.`template_survey`
    ADD COLUMN `adminx_quota_status` tinyint NOT NULL DEFAULT 0 COMMENT 'adminx社区配额状态  1计算中 2计算完成' AFTER `quota_status`;
