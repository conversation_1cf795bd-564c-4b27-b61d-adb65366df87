CREATE TABLE `cem_platform`.`survey_verify_record`
(
    `id`          bigint NOT NULL AUTO_INCREMENT,
    `s_id`        bigint NOT NULL COMMENT '问卷id',
    `user_id`     bigint NOT NULL COMMENT '用户id',
    `operation`   tinyint NULL COMMENT '操作: 提交、审核、驳回、撤销、通过',
    `comment`     varchar(500) NULL COMMENT '问卷审核原因',
    `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
);