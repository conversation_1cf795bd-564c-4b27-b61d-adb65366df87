ALTER TABLE `cem_platform`.`survey`
    ADD COLUMN `normal_finish_remark` text NULL COMMENT '正常结束语' AFTER `concluding_remark`,
    ADD COLUMN `abnormal_finish_remark` text NULL COMMENT '非正常结束语' AFTER `abnormal_concluding_remark`;

ALTER TABLE `cem_platform`.`survey`
    MODIFY COLUMN `concluding_remark` text NULL COMMENT '结束语，v1.9.2废弃' AFTER `welcoming_remark`,
    MODIFY COLUMN `abnormal_concluding_remark` text NULL COMMENT '非正常结束语，v1.9.2废弃' AFTER `show_concluding`;

update survey
set normal_finish_remark=JSON_OBJECT('type', CASE WHEN enable_redirect = 1 THEN 'link' else 'text' end,
                                     'text', concluding_remark,
                                     'link', redirect_url
    )
where normal_finish_remark is null;

update survey
set abnormal_finish_remark=JSON_OBJECT('type', CASE WHEN enable_redirect = 1 THEN 'link' else 'text' end,
                                       'text', concluding_remark,
                                       'link', redirect_url
    )
where abnormal_finish_remark is null;


ALTER TABLE `cem_platform`.`template_survey`
    MODIFY COLUMN `concluding_remark` text NULL COMMENT '结束语，v1.9.2废弃' AFTER `welcoming_remark`,
    ADD COLUMN `normal_finish_remark` text NULL COMMENT '结束语' AFTER `concluding_remark`,
    MODIFY COLUMN `abnormal_concluding_remark` text NULL COMMENT '非正常结束语，v1.9.2废弃' AFTER `show_concluding`,
    ADD COLUMN `abnormal_finish_remark` text NULL COMMENT '非正常结束语' AFTER `abnormal_concluding_remark`;

update template_survey
set normal_finish_remark=JSON_OBJECT('type', 'text',
                                     'text', concluding_remark,
                                     'link', null
    )
where normal_finish_remark is null;

update template_survey
set abnormal_finish_remark=JSON_OBJECT('type', 'text',
                                       'text', concluding_remark,
                                       'link', null
    )
where abnormal_finish_remark is null;