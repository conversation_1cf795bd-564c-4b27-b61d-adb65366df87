CREATE TABLE `cem_platform`.`survey_cross_analysis`
(
    `id`                bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
    `s_id`              bigint NULL COMMENT '问卷id',
    `select_rows`       varchar(128) NULL COMMENT '定义行: [{"name":"NN5lo","matrixValue":"NblSM"},{"name":"T05Ph"}]',
    `select_columns`    varchar(128) NULL COMMENT '定义列: [{"name":"NN5lo","matrixValue":"NblSM"}]',
    `filter`            varchar(500) NULL COMMENT '数据范围',
    `create_time`       timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`       timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
);