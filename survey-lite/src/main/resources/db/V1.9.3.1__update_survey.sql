update survey
set normal_finish_remark=JSON_OBJECT('type', CASE WHEN enable_redirect = 1 THEN 'link' else 'text' end,
                                     'text', concluding_remark,
                                     'link', redirect_url
    )
where normal_finish_remark is null;

update survey
set abnormal_finish_remark=JSON_OBJECT('type', CASE WHEN enable_redirect = 1 THEN 'link' else 'text' end,
                                       'text', abnormal_concluding_remark,
                                       'link', redirect_url
    );


update template_survey
set normal_finish_remark=JSON_OBJECT('type', 'text',
                                     'text', concluding_remark,
                                     'link', null
    )
where normal_finish_remark is null;

update template_survey
set abnormal_finish_remark=JSON_OBJECT('type', 'text',
                                       'text', abnormal_concluding_remark,
                                       'link', null
    );
