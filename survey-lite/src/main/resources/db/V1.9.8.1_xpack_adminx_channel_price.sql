INSERT INTO `cem_platform`.`xpack_config` (`enabled`, `create_time`, `modify_time`, `app`, `type`, `config`) VALUES ('1', now(), now(), 'cem', 'ADMINX_CHANNEL_PRICE', '{\"maxAmount\":10000000,\"quantityMin\":30,\"quantityMax\":20000,\"questionMin\":1,\"questionMax\":100,\"countQuestion\":[{\"min\":1,\"max\":10,\"price\":100},{\"min\":11,\"max\":20,\"price\":200},{\"min\":21,\"max\":30,\"price\":300},{\"min\":31,\"max\":40,\"price\":400},{\"min\":41,\"max\":50,\"price\":500},{\"min\":51,\"max\":60,\"price\":600},{\"min\":61,\"max\":70,\"price\":700},{\"min\":71,\"max\":80,\"price\":800},{\"min\":81,\"max\":90,\"price\":900},{\"min\":91,\"max\":100,\"price\":1000}],\"genderMatch\":[{\"name\":\"不限\",\"price\":0},{\"name\":\"男性\",\"price\":100},{\"name\":\"女性\",\"price\":100}],\"ageMatch\":[{\"name\":\"不限\",\"price\":0},{\"name\":\"18-25岁\",\"price\":200},{\"name\":\"26-29岁\",\"price\":200},{\"name\":\"30-39岁\",\"price\":400},{\"name\":\"40-49岁\",\"price\":600},{\"name\":\"50岁以上\",\"price\":1500}],\"educationMatch\":[{\"name\":\"不限\",\"price\":0},{\"name\":\"高中及以下\",\"price\":800},{\"name\":\"大专\",\"price\":300},{\"name\":\"本科\",\"price\":100},{\"name\":\"硕士及以上\",\"price\":800}],\"locationMatch\":[{\"name\":\"不限\",\"price\":0},{\"name\":\"北京\",\"price\":200},{\"name\":\"上海\",\"price\":200},{\"name\":\"广东\",\"price\":200},{\"name\":\"河北\",\"price\":300},{\"name\":\"江苏\",\"price\":300},{\"name\":\"山东\",\"price\":300},{\"name\":\"河南\",\"price\":300},{\"name\":\"湖北\",\"price\":300},{\"name\":\"山西\",\"price\":400},{\"name\":\"浙江\",\"price\":400},{\"name\":\"江西\",\"price\":400},{\"name\":\"湖南\",\"price\":400},{\"name\":\"广西\",\"price\":400},{\"name\":\"四川\",\"price\":400},{\"name\":\"辽宁\",\"price\":600},{\"name\":\"吉林\",\"price\":600},{\"name\":\"黑龙江\",\"price\":600},{\"name\":\"安徽\",\"price\":600},{\"name\":\"福建\",\"price\":600},{\"name\":\"重庆\",\"price\":600},{\"name\":\"陕西\",\"price\":600},{\"name\":\"天津\",\"price\":1000},{\"name\":\"内蒙古\",\"price\":1000},{\"name\":\"贵州\",\"price\":1000},{\"name\":\"云南\",\"price\":1000},{\"name\":\"甘肃\",\"price\":1000},{\"name\":\"海南\",\"price\":2100},{\"name\":\"西藏\",\"price\":2100},{\"name\":\"青海\",\"price\":2100},{\"name\":\"宁夏\",\"price\":2100},{\"name\":\"新疆\",\"price\":2100}]}');
ALTER TABLE `cem_platform`.`survey_channel`
ADD COLUMN `order_pay_type` VARCHAR(45) NULL COMMENT '订单报价方式：standard, manual' AFTER `resend_times`;
ALTER TABLE `cem_platform`.`survey_channel`
ADD COLUMN `order_id` BIGINT NULL COMMENT '订单id' AFTER `order_pay_type`;
ALTER TABLE `cem_platform`.`survey_channel`
ADD COLUMN `order_amount` INT NULL COMMENT '订单金额' AFTER `order_id`;
ALTER TABLE `cem_platform`.`survey_channel`
ADD COLUMN `user_id` BIGINT NULL AFTER `recovery_amount`;
ALTER TABLE `cem_platform`.`survey_response`
ADD INDEX `idx_org_sid` (`org_id` ASC, `s_id` ASC) ;