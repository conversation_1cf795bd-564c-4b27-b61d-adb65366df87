ALTER TABLE `cem_platform`.`survey_language`
    MODIFY COLUMN `title` varchar (200) NULL DEFAULT NULL COMMENT '问卷别名' AFTER `language`,
    ADD COLUMN `real_title` varchar (200) NULL COMMENT '问卷标题' AFTER `title`,
    MODIFY COLUMN `welcoming_remark` varchar (1000) NULL DEFAULT '' COMMENT '欢迎语' AFTER `title`,
    CHANGE COLUMN `concluding_remark` `normal_finish_remark` text NULL COMMENT '结束' AFTER `welcoming_remark`,
    CHANGE COLUMN `abnormal_concluding_remark` `abnormal_finish_remark` text NULL COMMENT '非正常结束' AFTER `normal_finish_remark`;

ALTER TABLE `cem_platform`.`survey_language`
    MODIFY COLUMN `questions` text NULL COMMENT '配置，json array 格式' AFTER `real_title`;

ALTER TABLE `cem_platform`.`survey_language`
    ADD COLUMN `personalized_remark` text NULL COMMENT '个性化结束页' AFTER `abnormal_concluding_remark`;