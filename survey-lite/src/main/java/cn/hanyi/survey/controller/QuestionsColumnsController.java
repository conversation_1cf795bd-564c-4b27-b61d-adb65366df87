package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionColumnDto;
import cn.hanyi.survey.service.QuestionsColumnService;
import cn.hanyi.survey.service.SurveyService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

@Tag(name = "问卷-问题-列")
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys/{id}/questions/{mid}/columns")
public class QuestionsColumnsController {

    @Autowired
    private QuestionsColumnService questionsColumnService;
    @Autowired
    private SurveyService surveyService;

    @DeleteMapping("/{cid}")
    @Operation(summary = "删除列")
    public BaseResponseDto<String> deleteOne(
            @PathVariable long id,
            @PathVariable long mid,
            @PathVariable long cid,
            @RequestBody Optional<List<Long>> logicIds) {
        questionsColumnService.deleteOneDeepEmbeddedMany(id, "survey", mid, "question", cid);
        logicIds.ifPresent(logicIds1 -> logicIds1.forEach(logicId -> surveyService.deleteLogic(logicId)));
        return new BaseResponseDto<>();
    }

    @PutMapping("")
    @Operation(summary = "批量创建问题选项")
    public ResourceResponseDto<List<SurveyQuestionColumnDto>> batchCreate(@PathVariable Long id, @PathVariable Long mid, @RequestBody List<SurveyQuestionColumnDto> columns) {
        Survey survey = surveyService.requireSurvey(id);
        List<SurveyQuestion> surveyQuestions = survey.getQuestions();
        SurveyQuestion question = surveyQuestions.stream().filter(q -> q.getId().equals(mid)).findFirst().orElseThrow(() -> new EntityNotFoundException(SurveyQuestion.class));
        return new ResourceResponseDto<>(questionsColumnService.insertQuestionColumns(question, columns));
    }

    @PostMapping("batch")
    @Operation(
            summary = "批量修改列"
    )
    @Tag(
            name = "问卷-问题-列"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyQuestionColumnDto> batchUpdateSurveyQuestionSurveyQuestionColumn(
            @PathVariable long id, @PathVariable long mid,
            @RequestBody ResourceBatchUpdateRequestDto<SurveyQuestionColumnDto> changes) {
        return new ResourceResponseDto(questionsColumnService.batchUpdate(id, "questions", mid, "columns", changes));
    }
}
