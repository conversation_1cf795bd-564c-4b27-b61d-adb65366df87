package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.entity.SurveyGroup;
import cn.hanyi.survey.core.repository.SurveyGroupRepository;
import cn.hanyi.survey.dto.survey.SurveyGroupChangeDto;
import cn.hanyi.survey.service.SurveyGroupService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "问卷-分组")
@Validated
@RestController
@RequestMapping("/survey-groups")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = SurveyGroup.class,
        repositoryClass = SurveyGroupRepository.class,
        serviceClass = SurveyGroupService.class,
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        permission = "isAuthenticated()",
        excludeActions = {BATCH_UPDATE},
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = SurveyGroupChangeDto.class),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = SurveyGroupChangeDto.class),
        },
        docCrud = "分组"
)
public class SurveyGroupController {
}