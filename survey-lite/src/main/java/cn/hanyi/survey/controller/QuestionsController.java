package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionDto;
import cn.hanyi.survey.dto.question.QuestionGroupDto;
import cn.hanyi.survey.dto.question.QuestionSequenceDto;
import cn.hanyi.survey.service.QuestionService;
import cn.hanyi.survey.service.QuestionsDynamicItemService;
import cn.hanyi.survey.service.QuestionsItemService;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.service.MapperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Tag(name = "问卷-问题")
@Validated
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/surveys/{id}/questions")
public class QuestionsController extends BaseController<QuestionService> {

    @Autowired
    private SurveyService surveyService;

    @Autowired
    private QuestionService questionService;

    @Autowired
    private QuestionsItemService questionsItemService;

    @Autowired
    private QuestionsDynamicItemService questionsDynamicItemService;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Autowired
    private OrganizationConfigService organizationConfigService;

    @PostMapping("")
    @Operation(summary = "新增问题")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<SurveyQuestionDto> addQuestion(@PathVariable long id, @RequestBody SurveyQuestionDto data) {
        Boolean surveyVerify = organizationConfigService.getOrDefaultConfig(OrganizationConfigType.surveyVerify).getSurveyVerify();
        Survey survey = surveyService.requireSurvey(id);
        //问卷审核开启，问卷状态只有在停止状态和已驳回状态才能添加问题
        if ((surveyVerify && !SurveyStatus.STOPPED.equals(survey.getStatus()))) {
            if (!SurveyStatus.REJECTED.equals(survey.getStatus())) {
                throw new BadRequestException("问卷状态已变更");
            }
        }
        //问卷审核关闭，问卷状态启用中不能添加问题，其他状态可以
        if ((!surveyVerify && SurveyStatus.COLLECTING.equals(survey.getStatus()))) {
            throw new BadRequestException("问卷状态已变更");
        }
        SurveyQuestionDto questionDto = surveyService.createEmbeddedMany(id, "questions", SurveyQuestion.class, SurveyQuestionDto.class, data);
        return new ResourceResponseDto<>(questionDto);
    }

    @PostMapping("batch")
    @Operation(summary = "批量修改问题")
    @Tag(name = "问卷-问题")
    @JsonView(ResourceViews.Basic.class)
    public ResourceListResponseDto<SurveyQuestionDto> batchUpdateSurveyQuestion(@PathVariable long id,
                                                                                @RequestBody ResourceBatchUpdateRequestDto<SurveyQuestionDto> changes) {
        return new ResourceListResponseDto<>(questionService.batchUpdateEmbeddedMany(id, "survey", changes));
    }

    @DeleteMapping("/{eid}")
    @Operation(summary = "删除问题")
    public BaseResponseDto<String> deleteOne(
            @PathVariable long id,
            @PathVariable long eid,
            @RequestBody Optional<List<Long>> logicIds) {
        // 删除题目的时候删除logic
        questionService.deleteOneEmbeddedMany(id, "survey", eid);
        logicIds.ifPresent(logicIds1 -> logicIds1.forEach(logicId -> surveyService.deleteLogic(logicId)));
        return new BaseResponseDto<>();
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除")
    @Tag(
            name = "问卷-题组"
    )
    public BaseResponseDto<Boolean> batchDeleteQuestion(
            @PathVariable long id,
            @RequestBody QuestionGroupDto dto) {
        return new BaseResponseDto<>(questionService.batchDelQuestionAndLogic(id,dto));
    }

    @PutMapping("/{eid}")
    @Operation(summary = "修改问题")
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyQuestionDto> updateOneSurveyQuestion(@PathVariable long id,
                                                                          @PathVariable long eid, @RequestBody Map<String, Object> data) {
        //限制逐级下拉
        if (data.get("configure") != null && data.get("configure").toString().length() > 60000) {
            throw new BadRequestException("字数超过上限");
        }

        SurveyQuestionDto dto = mapperService.map(data, SurveyQuestionDto.class);
        SurveyQuestionDto surveyQuestionDto = service.updateOneEmbeddedMany(id, "survey", eid, dto);

        // null值处理
        if (data.containsKey("visibleIf") || data.containsKey("startTime") || data.containsKey("endTime")
                || data.containsKey("maxLength") || data.containsKey("minLength") || data.containsKey("randomLimit")
                || data.containsKey("min") || data.containsKey("max")) {

            SurveyQuestion surveyQuestion = surveyQuestionDto.getEntity();
            if (data.containsKey("visibleIf") && data.get("visibleIf") == null) surveyQuestion.setVisibleIf(null);
            if (data.containsKey("startTime") && data.get("startTime") == null) surveyQuestion.setStartTime(null);
            if (data.containsKey("endTime") && data.get("endTime") == null) surveyQuestion.setEndTime(null);
            if (data.containsKey("minLength") && data.get("minLength") == null) surveyQuestion.setMinLength(null);
            if (data.containsKey("maxLength") && data.get("maxLength") == null) surveyQuestion.setMaxLength(null);
            if (data.containsKey("randomLimit") && data.get("randomLimit") == null) surveyQuestion.setRandomLimit(null);
            if (surveyQuestionDto.getType() == QuestionType.NUMBER && data.containsKey("min") && data.get("min") == null) surveyQuestion.setMin(null);
            if (surveyQuestionDto.getType() == QuestionType.NUMBER && data.containsKey("max") && data.get("max") == null) surveyQuestion.setMax(null);
            surveyQuestionDto = service.mapToDto(questionService.save(surveyQuestion));
        }
//        surveyEventTrigger.questionUpdate(id, eid);
        if (data.containsKey("type")) {
            // 题型切换
            surveyEventTrigger.questionTypeUpdate(id, eid, Objects.toString(data.get("type")));
        }
        return new ResourceResponseDto(surveyQuestionDto);
    }

    @PutMapping("/{qId}/dynamicItem")
    @Operation(summary = "修改问题-设置是否启用动态选项")
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyQuestionDto> updateQuestionDynamicItem(
            @PathVariable long id,
            @PathVariable long qId,
            @RequestBody Map<String, Object> data) {
        Object value = data.get("isDynamicItem");
        if (value == null) {
            throw new BadRequestException("缺少参数 isDynamicItem");
        }
        Boolean isDynamicItem = null;
        if (value instanceof Boolean) {
            isDynamicItem = (boolean) value;
        } else if (value instanceof String) {
            if (value.equals("true") || value.equals("false")) {
                isDynamicItem = Boolean.parseBoolean(value.toString());
            }
        }
        if (isDynamicItem == null) {
            throw new BadRequestException("参数 isDynamicItem 只能为 true | false");
        }
        questionsDynamicItemService.deleteAll(qId);
        SurveyQuestion question = questionService.get(qId);
        question.setIsDynamicItem(isDynamicItem);
        questionService.save(question);
        return new ResourceResponseDto<>(service.mapToDto(question));
    }

    /**
     * {
     * "isDynamicItem": true
     * }
     *
     * @param id
     * @param qId
     * @param data
     * @return
     */

    @PutMapping("/{qId}/mediaRandom")
    @Operation(summary = "修改问题-设置是否启用情景随机")
    @JsonView(ResourceViews.Detail.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SurveyQuestionDto> updateMediaRandom(
            @PathVariable long id,
            @PathVariable long qId,
            @RequestBody Map<String, Object> data) {
        Object value = data.get("isMediaRandom");
        if (value == null) {
            throw new BadRequestException("缺少参数 isMediaRandom");
        }
        Boolean isMediaRandom = null;
        if (value instanceof Boolean) {
            isMediaRandom = (boolean) value;
        } else if (value instanceof String) {
            if (value.equals("true") || value.equals("false")) {
                isMediaRandom = Boolean.parseBoolean(value.toString());
            }
        }
        if (isMediaRandom == null) {
            throw new BadRequestException("参数 isMediaRandom 只能为 true | false");
        }
        SurveyQuestion question = questionService.get(qId);
        questionsItemService.deleteAll(question);
        question.setIsMediaRandom(isMediaRandom);
        question.setConfigure("");
        questionService.save(question);
        return new ResourceResponseDto<>(service.mapToDto(question));
    }


    @DeleteMapping("/group")
    @Operation(summary = "删除题组")
    @Tag(
            name = "问卷-题组"
    )
    public BaseResponseDto<Boolean> deleteQuestionGroup(
            @PathVariable long id,
            @RequestBody QuestionGroupDto dto) {
        return new BaseResponseDto<>(questionService.batchDelQuestionAndLogic(id,dto));
    }


    @PostMapping("/group/copy")
    @Operation(summary = "复制题组")
    @Tag(
            name = "问卷-题组"
    )
    public BaseResponseDto<List<SurveyQuestionDto>> copy(@PathVariable long id, @RequestBody List<SurveyQuestionDto> data) {
        List<SurveyQuestionDto> list = new ArrayList<>();
        for (SurveyQuestionDto dto : data) {
            SurveyQuestionDto question = surveyService.createEmbeddedMany(id, "questions", SurveyQuestion.class, SurveyQuestionDto.class, dto);
            list.add(question);
        }
        return new BaseResponseDto<>(list);
    }

    @PostMapping("/updateSequence")
    @Operation(summary = "更新题目的排序号")
    public BaseResponseDto<Boolean> updateSequence(@PathVariable long id, @RequestBody List<QuestionSequenceDto> data) {
        return new BaseResponseDto<>(questionService.updateSequence(id, data));
    }

}
