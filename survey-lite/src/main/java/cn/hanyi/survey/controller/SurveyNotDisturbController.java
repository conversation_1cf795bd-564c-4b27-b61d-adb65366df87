package cn.hanyi.survey.controller;

import cn.hanyi.survey.client.dto.SurveyDisturbQueryDto;
import cn.hanyi.survey.client.service.SurveyDisturbRuleService;
import cn.hanyi.survey.core.entity.SurveyDisturbRule;
import cn.hanyi.survey.core.entity.SurveyDisturbRuleDto;
import cn.hanyi.survey.core.repository.SurveyDisturbRuleRepository;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourcePermission;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.COUNT;
import static org.befun.core.rest.annotation.processor.ResourceMethod.FIND_ALL;

/**
 * <AUTHOR>
 * @date 2022/12/30 11:25:07
 */
@Tag(name = "问卷-免打扰")
@ResourceController(
        entityClass = SurveyDisturbRule.class,
        repositoryClass = SurveyDisturbRuleRepository.class,
        serviceClass = SurveyDisturbRuleService.class,
        docTag = "问卷-免打扰",
        docCrud = "免打扰",
        excludeActions = {COUNT,FIND_ALL},
        permission = "isAuthenticated()",
        permissions = {
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "batchUpdate", permission = "isAuthenticated()")
        }
)
@RestController
@RequestMapping("/surveys/disturb")
@PreAuthorize("isAuthenticated()")
public class SurveyNotDisturbController extends BaseController<SurveyDisturbRuleService> {
        @GetMapping("")
        @Operation(
                summary = "全部免打扰"
        )
        @Tag(
                name = "问卷-免打扰"
        )
        @JsonView(ResourceViews.Basic.class)
        public ResourcePageResponseDto<SurveyDisturbRuleDto> findAll(
                @ResourceQueryCustom SurveyDisturbQueryDto params) {
                return new ResourcePageResponseDto(service.findAll(params));
        }
}





















