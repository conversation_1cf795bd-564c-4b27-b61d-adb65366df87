package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.entity.SurveyLanguageDto;
import cn.hanyi.survey.dto.SurveyLanguageTranslateRequestDto;
import cn.hanyi.survey.service.SurveyLanguageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2023/4/6 14:53:11
 */
@Tag(name = "问卷-语言")
@RestController
@PreAuthorize("isAuthenticated()")
public class SurveyLanguageController {

    @Autowired
    private SurveyLanguageService surveyLanguageService;

    @Operation(summary = "根据语言类型获取语言")
    @GetMapping("/surveys/{sid}/language/{type}")
    public ResourceResponseDto<SurveyLanguageDto> findBySurveyAndLanguage(@PathVariable Long sid, @PathVariable String type) {
        return new ResourceResponseDto(surveyLanguageService.findBySurveyAndLanguage(sid, type));
    }

    @Operation(summary = "一键翻译")
    @PostMapping("/surveys/{sid}/language/translate")
    public ResourceResponseDto<Boolean> translate(@PathVariable Long sid, @RequestBody SurveyLanguageTranslateRequestDto requestDto) {
        return new ResourceResponseDto<>(surveyLanguageService.translateAsync(sid, requestDto));
    }

}















