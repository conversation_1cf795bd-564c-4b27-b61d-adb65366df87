package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.projection.SimpleResponse4;
import cn.hanyi.survey.dto.open.ExternalUserResponseStatusDto;
import cn.hanyi.survey.dto.open.VerifyResponseDto;
import cn.hanyi.survey.dto.open.VerifyResponseResultDto;
import cn.hanyi.survey.service.ChannelRecordService;
import cn.hanyi.survey.service.OpenSurveyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.extension.sms.chuanglan.ChuanglanReceiveInfo;
import org.befun.extension.sms.chuanglan.ChuanglanSmsProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Tag(name = "开放接口-答题")
@RestController
@RequestMapping("/open")
public class OpenController {

    @Autowired
    private OpenSurveyService openService;
    @Autowired
    private ChannelRecordService channelRecordService;
    @Autowired(required = false)
    private ChuanglanSmsProvider chuanglanSmsProvider;

    @PostMapping("/verify-response")
    @Operation(summary = "答题有效性验证")
    @PreAuthorize("isAuthenticated()")
    public VerifyResponseResultDto verifyResponse(@RequestBody VerifyResponseDto dto) {
        return openService.verifyResponse(dto);
    }

    @GetMapping("/sms/callback/chuanglan")
    @Operation(summary = "短信(创蓝)发送回调")
    public Map<String, String> smsCallbackChuanglan(ChuanglanReceiveInfo receiveInfo) {
        if (chuanglanSmsProvider != null) {
            chuanglanSmsProvider.receiveSmsCallback(receiveInfo, i -> {
                channelRecordService.smsSendCallback(i);
            });
        }
        return Map.of("clcode", "000000");
    }

    @PostMapping("/verify-response-status")
    @Operation(summary = "答题状态验证")
    @PreAuthorize("isAuthenticated()")
    public ResourceListResponseDto<SimpleResponse4> verifyResponseStatus(@Valid @RequestBody ExternalUserResponseStatusDto dto) {
        return new ResourceListResponseDto<>(openService.verifyResponseStatus(dto));
    }
}