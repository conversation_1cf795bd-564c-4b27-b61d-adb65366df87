package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyDto;
import cn.hanyi.survey.dto.RollBackResponseMessageDto;
import cn.hanyi.survey.dto.survey.SurveyCopyDto;
import cn.hanyi.survey.service.ResponseService;
import cn.hanyi.survey.service.ResponseSharedService;
import cn.hanyi.survey.service.SurveyService;
import cn.hanyi.survey.service.template.TemplateSurveyService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.websocket.server.PathParam;


/**
 * 答题客户端 不需要鉴权
 *
 * <AUTHOR>
 */
@Tag(name = "问卷-客户端")
@RestController
@RequestMapping("/surveys")
public class ResponseClientController {
    @Autowired
    private SurveyService surveyService;

    @Autowired
    private TemplateSurveyService templateSurveyService;

    @Autowired
    private ResponseService responseService;

    @Autowired
    private ResponseSharedService responseSharedService;

    @Operation(summary = "获取单个问卷详情")
    @GetMapping("/{surveyId}")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<SurveyDto> findOne(@PathVariable long surveyId) {
        return new ResourceResponseDto(surveyService.findOne(surveyId));
    }

    @Operation(summary = "问卷模板预览")
    @GetMapping("/template/{templateId}")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<SurveyDto> viewSurveyTemplate(@PathVariable long templateId) {
        return new ResourceResponseDto(templateSurveyService.findOne(templateId));
    }

    @Operation(summary = "复制问卷到某个账号，复制别人问卷到自己账号（用于复现客户问卷出现的问题）")
    @PostMapping("/copy")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<Boolean> copySurvey(@RequestBody SurveyCopyDto dto) {
        if (!"5fa84f3f9648477387332cb1048d20ff".equals(dto.getCopyToken())) {
            return new ResourceResponseDto<>(false);
        }
        dto.getSourceSurveyIds().forEach(surveyId -> {
            Survey survey = surveyService.get(surveyId);
            surveyService.copy(survey, dto.getTargetUserId(), dto.getTargetOrgId());
        });
        return new ResourceResponseDto<>(true);
    }

    @GetMapping("/{surveyId}/responses/{responseId}/shared")
    @Operation(summary = "通过答卷分享token获取数据")
    public ResourceResponseDto getSurveyResponseSharedByToken(
            @PathVariable long surveyId,
            @PathVariable long responseId,
            @PathParam(value = "token") String token
    ) {
        return new ResourceResponseDto<>(responseSharedService.getSurveyResponseSharedByToken(surveyId, responseId, token));
    }

    @PostMapping("/quota-rollback")
    @Operation(summary = "回退答卷配额")
    public ResourceResponseDto rollBackSurveyResponseQuota(@RequestBody RollBackResponseMessageDto messageDto
    ) {
        return new ResourceResponseDto<>(responseService.rollBackSurveyResponseQuota(messageDto));
    }

    @GetMapping("/{surveyId}/responses/{responseId}/action-reply")
    @Operation(summary = "获取答卷行动回复")
    public ResourceResponseDto getSurveyResponseActionReply(@PathVariable long surveyId,
            @PathVariable long responseId) {
        return new ResourceResponseDto<>(responseService.getActionReply(surveyId, responseId));
    }
}