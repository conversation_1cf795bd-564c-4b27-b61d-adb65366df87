package cn.hanyi.survey.controller;

import cn.hanyi.survey.core.constant.DownloadType;
import cn.hanyi.survey.core.constant.survey.ResponseImportType;
import cn.hanyi.survey.dto.DownloadDto;
import cn.hanyi.survey.dto.DownloadListDto;
import cn.hanyi.survey.dto.SurveyResponseDetailDto;
import cn.hanyi.survey.dto.SurveyResponseIdsDto;
import cn.hanyi.survey.dto.open.ResponseSharedParamsDto;
import cn.hanyi.survey.dto.open.ResponseSharedTokenDto;
import cn.hanyi.survey.service.*;
import cn.hanyi.survey.service.download.ResponseDownloadHelper;
import cn.hanyi.survey.service.download.ResponseImportApiParser;
import cn.hanyi.survey.service.download.ResponseImportParser;
import cn.hanyi.survey.service.download.dto.ResponseImportApiListDto;
import cn.hanyi.survey.service.download.dto.ResponseImportContext;
import cn.hanyi.survey.service.response.ResponseDownloadService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.PermissionPath;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 管理后台使用获取答题数据，需要鉴权
 *
 * <AUTHOR>
 */
@Tag(name = "答卷")
@RestController
@RequestMapping("/surveys")
@PreAuthorize("isAuthenticated()")
@Slf4j
public class ResponseAdminController {

    @Autowired
    private ResponseService responseService;

    @Autowired
    private ResponseDownloadService responseDownloadService;

    @Autowired
    private DownloadService downloadService;
    @Autowired
    private ResponseDownloadHelper responseDownloadHelper;

    @Autowired
    private ResponseSharedConfigService responseSharedConfigService;

    @Autowired
    private ResponseSharedService responseSharedService;

    @Autowired
    private ResponseImportParser importResponseParser;

    @Autowired
    private ResponseImportApiParser responseImportApiParser;

    @Autowired
    private SurveyService surveyService;
    @Autowired
    private AnalyticService analyticService;

    @GetMapping("/{surveyId}/responses/{responseId}")
    @Operation(summary = "获取答卷详情 detail")
    public ResourceResponseDto<SurveyResponseDetailDto> getResponseDetail(@PathVariable long surveyId, @PathVariable long responseId) {
        return new ResourceResponseDto(responseService.getSurveyResponseDetail(surveyId, responseId));
    }

    @DeleteMapping("/{surveyId}/responses")
    @Operation(summary = "批量删除答卷")
    public ResourceResponseDto deleteBatch(@PathVariable long surveyId, @RequestBody SurveyResponseIdsDto responseIdsDto) {
        return responseService.deleteBatch(surveyId, responseIdsDto);
    }

    @PutMapping("/{surveyId}/responses")
    @Operation(summary = "批量修改答卷状态")
    public ResourceResponseDto updateBatch(@PathVariable long surveyId, @RequestBody SurveyResponseIdsDto responseIdsDto) {
        return responseService.updateBatch(surveyId, responseIdsDto);
    }

    @SneakyThrows
    @PostMapping("/{surveyId}/download")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "生成下载答题任务")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.TOUCH_MANAGE_SURVEY_DOWNLOAD)
    public ResourceResponseDto<Object> download(HttpServletResponse response, @PathVariable Long surveyId, @RequestBody(required = false) DownloadDto downloadDto) {
        // 同步下载直接给流
        // 批量下载生成任务
        // 查看是否拥有问卷权限
        surveyService.findOne(surveyId);
        analyticService.checkVersionLimit();
        if (downloadDto.getAsync()) {
            if (downloadDto.getDownloadType() == DownloadType.ATTACHMENT) {
                responseDownloadService.checkAttachmentLimit(surveyId, downloadDto);
                return new ResourceResponseDto<>(responseDownloadService.downloadTask(surveyId, downloadDto));
            }
            return new ResourceResponseDto<>(responseDownloadService.downloadTask(surveyId, downloadDto));
        } else {
            if (downloadDto.getDownloadType() == DownloadType.ATTACHMENT) {
                responseDownloadHelper.downloadToResponseFileZip(surveyId, downloadDto, response);
                return null;
            }
            if (downloadDto.getUseNewDownload()) {
                responseDownloadHelper.downloadToResponse(surveyId, downloadDto, response);
            } else {
                downloadService.download(response, surveyId, downloadDto);
            }
        }
        return null;

    }

    @GetMapping("/{surveyId}/download2")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "生成下载答题任务2")
    @PreAuthorize("isAuthenticated()")
    @RequirePermissions(PermissionPath.TOUCH_MANAGE_SURVEY_DOWNLOAD)
    public ResourceResponseDto<Object> download2(HttpServletResponse response, @PathVariable Long surveyId,
                                                 @RequestParam(required = false) Integer batchSize,
                                                 @RequestParam(required = false) String responseIds) {
        TenantContext.clear();
        List<Long> rIds = null;
        if (StringUtils.isNotEmpty(responseIds)) {
            rIds = Arrays.stream(responseIds.split(",")).map(Long::parseLong).collect(Collectors.toList());
        }
        responseDownloadHelper.downloadToResponse(surveyId, null, batchSize, DownloadType.EXCEL, rIds, response);
        return null;
    }

    @GetMapping("/{surveyId}/download-list")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "答题任务列表")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<List<DownloadListDto>> downloadHistory(@PathVariable Long surveyId, @RequestParam(required = false) Boolean pop) {
        return new ResourceResponseDto<>(responseDownloadService.getDownloadList(surveyId, pop));
    }

    @DeleteMapping("/{surveyId}/download-pop")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "删除答题弹窗")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto deletePop(@PathVariable Long surveyId) {
        responseDownloadService.deletePop(surveyId);
        return new ResourceResponseDto<>();
    }

    @DeleteMapping("/{surveyId}/download-list/{taskId}")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "删除答题任务")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> delHistory(@PathVariable Long surveyId, @PathVariable Long taskId) {
        return new ResourceResponseDto<>(responseDownloadService.deleteDownloadTask(taskId));
    }

    @PutMapping("/{surveyId}/token")
    @Operation(summary = "创建/修改答卷分享token")
    public ResourceResponseDto<ResponseSharedParamsDto> updateResponseSharedToken(
            @PathVariable long surveyId,
            @RequestBody ResponseSharedParamsDto responseSharedParamsDto) {
        return new ResourceResponseDto(responseSharedConfigService.updateOrCreateResponseSharedConfig(surveyId, responseSharedParamsDto));
    }

    @GetMapping("/{surveyId}/token")
    @Operation(summary = "获取答卷分享的token配置")
    public ResourceResponseDto<ResponseSharedParamsDto> getResponseSharedTokenConfig(
            @PathVariable long surveyId
    ) {
        return new ResourceResponseDto(responseSharedConfigService.getResponseSharedConfig(surveyId));
    }

    @GetMapping("/{surveyId}/responses/{responseId}/token")
    @Operation(summary = "获取答卷分享的token")
    public ResourceResponseDto<ResponseSharedTokenDto> getResponseSharedToken(
            @PathVariable long surveyId,
            @PathVariable long responseId,
            @PathParam("externalUserId") Optional<String> externalUserId
    ) {
        return new ResourceResponseDto<>(responseSharedService.getResponseSharedToken(surveyId, responseId, true, externalUserId));
    }

    @GetMapping("/{surveyId}/responses/exist")
    @Operation(summary = "问卷是否存在答卷（包含作废）")
    public ResourceResponseDto<Boolean> existResponse(@PathVariable long surveyId) {
        return new ResourceResponseDto<>(responseService.existResponse(surveyId));
    }

    @GetMapping("/{surveyId}/download-template")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "下载问卷答题模板")
    @PreAuthorize("isAuthenticated()")
    public void downloadTemplate(HttpServletResponse response, @PathVariable Long surveyId) throws IOException {
        responseDownloadHelper.downloadToResponseTemplate(surveyId, DownloadType.EXCEL, response);
    }

    @Operation(summary = "导入答卷(excel)")
    @PostMapping(value = "/{surveyId}/import-response-excel-file")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<ResponseImportContext> importResponseExcelFile(@RequestParam("file") MultipartFile file, @PathVariable Long surveyId) {
        return new ResourceResponseDto<>(importResponseParser.uploadResponse(surveyId, ResponseImportType.XM_PLUS, file));
    }

    @Operation(summary = "导入答卷(提交)")
    @PostMapping(value = "/{surveyId}/import-response")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> importResponseTask(@PathVariable@NotNull Long surveyId, @RequestBody ResponseImportContext context) {
        context.setSurveyId(surveyId);
        return new ResourceResponseDto<>(importResponseParser.importResponseTask(context));
    }

    @Operation(summary = "api导入答卷(提交)")
    @PostMapping(value = "/{surveyId}/import-response-api")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Boolean> importResponseApi(@PathVariable@NotNull Long surveyId, @RequestBody@NotNull ResponseImportApiListDto list) {
        return new ResourceResponseDto<>(responseImportApiParser.importResponseApi(surveyId, list));
    }
}

