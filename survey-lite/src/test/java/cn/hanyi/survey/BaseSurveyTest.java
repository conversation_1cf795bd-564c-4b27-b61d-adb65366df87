package cn.hanyi.survey;

import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.constant.survey.LogicType;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.befun.auth.dto.OrganizationOptionalLimitDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.core.dto.UserDto;
import org.befun.core.utils.JsonHelper;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.web.servlet.request.RequestPostProcessor;

import javax.sql.DataSource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * The class description
 *
 * <AUTHOR>
 */
public class BaseSurveyTest {
    protected static String TOPIC_SURVEY_RESPONSE = "survey_response";

    @Autowired
    private RedisTemplate<String, String> template;

    @Autowired
    protected DataSource dataSource;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected SurveyRepository surveyRepository;

    @Autowired
    protected SurveyQuestionRepository questionRepository;

    @Autowired
    protected SurveyQuestionItemRepository choiceRepository;

    @Autowired
    protected SurveyQuotaRepository quotaRepository;

    @Autowired
    protected SurveyLogicRepository logicRepository;

    @Autowired
    protected SurveyResponseRepository responseRepository;

    @Autowired
    protected SurveyResponseCellRepository cellRepository;

    @Autowired
    protected SurveyChannelRepository channelRepository;

    @Autowired
    protected OrganizationRepository organizationRepository;

    @Autowired
    protected UserRepository userRepository;

    protected Survey s1;
    protected Survey s2;
    protected Survey s3;
    protected Survey s4;
    protected Survey sf;

    protected SurveyStyle s1_style;

    protected SurveyQuestion q_empty;
    protected SurveyQuestion q_separator;
    protected SurveyQuestion q_text;
    protected SurveyQuestion q_single_choice;
    protected SurveyQuestion q_multiple_choice;
    protected SurveyQuestion q_mark;
    protected SurveyQuestion q_score;
    protected SurveyQuestion q_number_choice;
    protected SurveyQuestion q_mobile;
    protected SurveyQuestion q_email;
    protected SurveyQuestion q_matrix;
    protected SurveyQuestion q_matrix_score;


    protected SurveyQuestion sf_q1;
    protected SurveyQuestion sf_q2;
    protected SurveyQuestion sf_q3;
    protected SurveyQuestion sf_q4;
    protected SurveyQuestion sf_q5;
    protected SurveyQuestion sf_q6;
    protected SurveyQuestion sf_q7;
    protected SurveyQuestion sf_q8;
    protected SurveyQuestion sf_q9;
    protected SurveyQuestion sf_q10;
    protected SurveyQuestion sf_q11;
    protected SurveyQuestion sf_q12;

    protected SurveyQuestion s1_q1;
    protected SurveyQuestion s1_q2;
    protected SurveyQuestion s1_q3;

    protected SurveyQuestion s2_q1;
    protected SurveyQuestion s2_q2;
    protected SurveyQuestion s2_q3;

    protected SurveyQuestion s3_q1;
    protected SurveyQuestion s3_q2;
    protected SurveyQuestion s3_q3;
    protected SurveyQuestion s3_q4;

    protected Map<String, SurveyQuestion> sf_questions;
    protected Map<String, SurveyQuestion> s1_questions;
    protected Map<String, SurveyQuestion> s2_questions;
    protected Map<String, SurveyQuestion> s3_questions;


    protected Map<String, SurveyQuestionItem> sf_q1_items;
    protected Map<String, SurveyQuestionItem> sf_q2_items;
    protected Map<String, SurveyQuestionItem> sf_q3_items;
    protected Map<String, SurveyQuestionItem> sf_q4_items;
    protected Map<String, SurveyQuestionItem> sf_q5_items;
    protected Map<String, SurveyQuestionItem> sf_q7_items;
    protected Map<String, SurveyQuestionItem> sf_q8_items;
    protected Map<String, SurveyQuestionItem> sf_q9_items;
    protected Map<String, SurveyQuestionItem> sf_q10_items;
    protected Map<String, SurveyQuestionItem> sf_q11_items;
    protected Map<String, SurveyQuestionItem> sf_q12_items;

    protected Map<String, SurveyQuestionItem> s1_q1_items;
    protected Map<String, SurveyQuestionItem> s1_q2_items;
    protected Map<String, SurveyQuestionItem> s1_q3_items;

    protected Map<String, SurveyQuestionItem> s2_q2_items;
    protected Map<String, SurveyQuestionItem> s2_q3_items;

    protected SurveyQuestionItem sf_q1_c1;
    protected SurveyQuestionItem sf_q2_c1;
    protected SurveyQuestionItem sf_q3_c1;
    protected SurveyQuestionItem sf_q4_c1;
    protected SurveyQuestionItem sf_q4_c2;
    protected SurveyQuestionItem sf_q4_c3;
    protected SurveyQuestionItem sf_q5_c1;
    protected SurveyQuestionItem sf_q5_c2;
    protected SurveyQuestionItem sf_q5_c3;
    protected SurveyQuestionItem sf_q7_c1;
    protected SurveyQuestionItem sf_q8_c1;
    protected SurveyQuestionItem sf_q9_c1;
    protected SurveyQuestionItem sf_q10_c1;
    protected SurveyQuestionItem sf_q11_c1;
    protected SurveyQuestionItem sf_q11_c2;
    protected SurveyQuestionItem sf_q11_c3;

    protected SurveyQuestionItem s1_q2_c1;
    protected SurveyQuestionItem s1_q2_c2;
    protected SurveyQuestionItem s1_q2_c3;

    protected SurveyQuestionItem s2_q2_c1;
    protected SurveyQuestionItem s2_q2_c2;
    protected SurveyQuestionItem s2_q2_c3;

    protected SurveyResponse sf_r1;

    protected SurveyResponse s1_r1;
    protected SurveyResponse s1_r2;
    protected SurveyResponse s1_r3;

    protected SurveyResponse s2_r1;
    protected SurveyResponse s2_r2;
    protected SurveyResponse s2_r3;

    protected SurveyResponse s3_r1;
    protected SurveyResponse s3_r2;
    protected SurveyResponse s3_r3;
    protected SurveyResponse s3_r4;

    protected SurveyResponseCell s1_r1_q1;
    protected SurveyResponseCell s1_r1_q2;
    protected SurveyResponseCell s1_r1_q3;
    protected SurveyResponseCell s1_r3_q1;
    protected SurveyResponseCell s1_r3_q2;
    protected SurveyResponseCell s1_r3_q3;
    protected SurveyResponseCell s2_r1_q1;
    protected SurveyResponseCell s2_r1_q2;
    protected SurveyResponseCell s2_r1_q3;
    protected SurveyResponseCell s3_r1_q1;
    protected SurveyResponseCell s3_r1_q2;
    protected SurveyResponseCell s3_r1_q3;
    protected SurveyResponseCell s3_r1_q4;

    protected SurveyQuota s1_q1_quota;

    protected String anotherIp = "************";
    protected Long mock_org_1_id = 1L;
    protected Long mock_org_2_id = 2L;
    protected String mock_token_1 = "fake_token_1";
    protected String mock_token_2 = "fake_token_2";
    protected UserDto mock_user_1;
    protected UserDto mock_user_2;

    protected String client_id_1 = "abc-001";
    protected String client_id_2 = "abc-002";

    protected Date d11 = createLocalDateTime("2021-05-01");
    protected Date d12 = createLocalDateTime("2021-05-02");
    protected Date d13 = createLocalDateTime("2021-05-03");
    protected Date d21 = createLocalDateTime("2021-07-01");
    protected Date d22 = createLocalDateTime("2021-07-02");

    /**
     * mock with remoteAddr supported
     *
     * @param remoteAddr
     * @return
     */
    protected static RequestPostProcessor remoteAddr(final String remoteAddr) {
        return new RequestPostProcessor() {
            @Override
            public MockHttpServletRequest postProcessRequest(MockHttpServletRequest request) {
                request.setRemoteAddr(remoteAddr);
                return request;
            }
        };
    }

    @SneakyThrows
    @BeforeEach
    public void setup() {
        mock_user_1 = UserDto
                .builder()
                .orgId(mock_org_1_id)
                .id(1L)
                .username("u1")
                .build();
        User user1 = new User();
        user1.setId(mock_user_1.getId());
        user1.setOrgId(mock_user_1.getOrgId());
        user1.setUsername(mock_user_1.getUsername());
        user1.setTruename(mock_user_1.getUsername());

        mock_user_2 = UserDto
                .builder()
                .orgId(mock_org_2_id)
                .id(2L)
                .username("u2")
                .build();
        User user2 = new User();
        user2.setId(mock_user_2.getId());
        user2.setOrgId(mock_user_2.getOrgId());
        user2.setUsername(mock_user_2.getUsername());
        user2.setTruename(mock_user_2.getUsername());

        userRepository.saveAll(List.of(user1, user2));

        q_empty = SurveyTestUtility.buildQuestion(null, "q_empty", QuestionType.EMPTY);
        q_separator = SurveyTestUtility.buildQuestion(null, "q_separator", QuestionType.SEPARATOR);
        q_text = SurveyTestUtility.buildQuestion(null, "q_text", QuestionType.TEXT);
        q_single_choice = SurveyTestUtility.buildQuestion(null, "q_single_choice", QuestionType.SINGLE_CHOICE);
        q_multiple_choice = SurveyTestUtility.buildQuestion(null, "q_multiple_choice", QuestionType.MULTIPLE_CHOICES);
        q_mark = SurveyTestUtility.buildQuestion(null, "q_mark", QuestionType.MARK);
        q_score = SurveyTestUtility.buildQuestion(null, "q_score", QuestionType.MARK);
        q_number_choice = SurveyTestUtility.buildQuestion(null, "q_number_choice", QuestionType.NUMBER);
        q_mobile = SurveyTestUtility.buildQuestion(null, "q_mobile", QuestionType.MOBILE);
        q_email = SurveyTestUtility.buildQuestion(null, "q_email", QuestionType.EMAIL);
        q_matrix = SurveyTestUtility.buildQuestion(null, "q_matxix", QuestionType.MATRIX_CHOICE);
        q_matrix_score = SurveyTestUtility.buildQuestion(null, "q_matrix_score", QuestionType.MATRIX_SCORE);

        // build survey
        s1 = SurveyTestUtility.buildSurvey(1,
                new QuestionType[]{QuestionType.TEXT, QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICES},
                3, mock_user_1
        );

        s2 = SurveyTestUtility.buildSurvey(2,
                new QuestionType[]{QuestionType.TEXT, QuestionType.SINGLE_CHOICE, QuestionType.MULTIPLE_CHOICES},
                3, mock_user_1
        );

        s3 = SurveyTestUtility.buildSurvey(3,
                new QuestionType[]{QuestionType.TEXT, QuestionType.SINGLE_CHOICE, QuestionType.NUMBER, QuestionType.NUMBER},
                3, mock_user_2
        );
        s4 = SurveyTestUtility.buildSurvey(4,
                new QuestionType[]{},
                0, mock_user_1
        );

        sf = SurveyTestUtility.buildSurvey(5, QuestionType.values(), 3, mock_user_2);

        // build style
        s1_style = new SurveyStyle();
        s1_style.setFontSize("50px");
        s1_style.setMainColor("#1234567");
        s1_style.setTitleColor("#2234567");
        s1_style.setWelcomingColor("#3234567");
        s1_style.setQuestionColor("#4234567");
        s1_style.setItemColor("#5234567");
        s1_style.setButtonColor("#6234567");
        s1_style.setBackgroundColor("#7234567");
        s1_style.setBackgroundOpacity(0.5);

        s1.setStyle(s1_style);

        surveyRepository.save(sf);
        surveyRepository.save(s1);
        surveyRepository.save(s2);
        surveyRepository.save(s3);
        surveyRepository.save(s4);


        //
        sf_questions = sf.getQuestions().stream()
                .collect(Collectors.toMap(SurveyQuestion::getName, Function.identity()));
        s1_questions = s1.getQuestions().stream()
                .collect(Collectors.toMap(SurveyQuestion::getName, Function.identity()));
        s2_questions = s2.getQuestions().stream()
                .collect(Collectors.toMap(SurveyQuestion::getName, Function.identity()));
        s3_questions = s3.getQuestions().stream()
                .collect(Collectors.toMap(SurveyQuestion::getName, Function.identity()));

        sf_q1 = sf_questions.get("q1");
        sf_q2 = sf_questions.get("q2");
        sf_q3 = sf_questions.get("q3");
        sf_q4 = sf_questions.get("q4");
        sf_q5 = sf_questions.get("q5");
        sf_q6 = sf_questions.get("q6");
        sf_q7 = sf_questions.get("q7");
        sf_q8 = sf_questions.get("q8");
        sf_q9 = sf_questions.get("q9");
        sf_q10 = sf_questions.get("q10");
        sf_q11 = sf_questions.get("q11");

        s1_q1 = s1_questions.get("q1");
        s1_q2 = s1_questions.get("q2");
        s1_q3 = s1_questions.get("q3");

        // build logic
        SurveyLogic s1_logic = new SurveyLogic();
        ArrayList<String> s1_logic_ids = new ArrayList<>();
        ArrayList<SurveyLogic> s1_logics = new ArrayList<>();

        s1_logic_ids.add(s1_q1.getName());

        s1_logic.setSurvey(s1);
        s1_logic.setQuestionNames(s1_logic_ids);
        s1_logic.setExpression("q1.value == \"123\"");
        s1_logic.setType(LogicType.GOTO);
        s1_logic.setTarget(s1_q2.getName());

        s1_logics.add(s1_logic);
        s1.setLogics(s1_logics);
//        s1.setCreateTime(new SimpleDateFormat("yyyy-MM-dd").parse("2021-05-01"));
        surveyRepository.save(s1);

        s2_q1 = s2_questions.get("q1");
        s2_q2 = s2_questions.get("q2");
        s2_q3 = s2_questions.get("q3");

        s3_q1 = s3_questions.get("q1");
        s3_q2 = s3_questions.get("q2");
        s3_q3 = s3_questions.get("q3");
        s3_q4 = s3_questions.get("q4");

        // build quota
        s1_q1_quota = SurveyTestUtility.buildQuota(s1_q1, "q1 == 'hit_me'", 1);


        // build items
        s1_q1_items = s1_q1.getItems().stream()
                .collect(Collectors.toMap(SurveyQuestionItem::getValue, Function.identity()));
        s1_q2_items = s1_q2.getItems().stream()
                .collect(Collectors.toMap(SurveyQuestionItem::getValue, Function.identity()));
        s1_q3_items = s1_q3.getItems().stream()
                .collect(Collectors.toMap(SurveyQuestionItem::getValue, Function.identity()));

        s1_q2_c1 = s1_q2_items.get("c1");
        s1_q2_c2 = s1_q2_items.get("c2");
        s1_q2_c3 = s1_q2_items.get("c3");

        s2_q2_items = s2_q2.getItems().stream()
                .collect(Collectors.toMap(SurveyQuestionItem::getValue, Function.identity()));
        s2_q3_items = s2_q3.getItems().stream()
                .collect(Collectors.toMap(SurveyQuestionItem::getValue, Function.identity()));

        // build response
        sf_r1 = SurveyTestUtility.buildResponse(sf, "guangdong", true, ResponseStatus.FINAL_SUBMIT);
        s1_r1 = SurveyTestUtility.buildResponse(s1, "guangdong", false, ResponseStatus.INIT, d11);
        s1_r2 = SurveyTestUtility.buildResponse(s1, "guangdong", false, ResponseStatus.EARLY_COMPLETED, d11);
        s1_r3 = SurveyTestUtility.buildResponse(s1, "shanghai", true, ResponseStatus.FINAL_SUBMIT, d12);

        s1_r1.setFinishTime(d11);
        s1_r2.setFinishTime(d12);
        s1_r3.setFinishTime(new SimpleDateFormat("yyyy-MM-dd").parse("2021-05-03"));

        responseRepository.save(sf_r1);
        responseRepository.save(s1_r1);
        responseRepository.save(s1_r2);
        responseRepository.save(s1_r3);

        s2_r1 = SurveyTestUtility.buildResponse(s2, "sichuan", true, ResponseStatus.FINAL_SUBMIT, d21);
        s2_r2 = SurveyTestUtility.buildResponse(s2, "sichuan", false, ResponseStatus.INIT, d21);
        s2_r3 = SurveyTestUtility.buildResponse(s2, "beijing", true, ResponseStatus.FINAL_SUBMIT);
        responseRepository.save(s2_r1);
        responseRepository.save(s2_r2);
        responseRepository.save(s2_r3);

        s3_r1 = SurveyTestUtility.buildResponse(s3, "sichuan", true, ResponseStatus.FINAL_SUBMIT);
        s3_r2 = SurveyTestUtility.buildResponse(s3, "sichuan", false, ResponseStatus.INIT);
        s3_r3 = SurveyTestUtility.buildResponse(s3, "beijing", true, ResponseStatus.FINAL_SUBMIT);
        s3_r4 = SurveyTestUtility.buildResponse(s3, "beijing", true, ResponseStatus.FINAL_SUBMIT);
        responseRepository.save(s3_r1);
        responseRepository.save(s3_r2);
        responseRepository.save(s3_r3);
        responseRepository.save(s3_r4);

        s1_r1.setDurationSeconds(10);
        s1_r1.setClientId(client_id_1);
        s1_r2.setDurationSeconds(5);
        s1_r2.setClientId(client_id_2);
        s1_r3.setDurationSeconds(6);
        responseRepository.save(s1_r1);
        responseRepository.save(s1_r2);
        responseRepository.save(s1_r3);

        s1_r1.setCollectorMethod(SurveyCollectorMethod.LINK);
        s1_r2.setCollectorMethod(SurveyCollectorMethod.EMBEDDED);
        s1_r3.setCollectorMethod(SurveyCollectorMethod.EMBEDDED);

        responseRepository.save(s2_r1);
        responseRepository.save(s2_r2);
        responseRepository.save(s2_r3);

        // build response answer
        s1_r1_q1 = SurveyTestUtility.buildResponseCell(s1, s1_q1, s1_r1, "hello");
        s1_r1_q2 = SurveyTestUtility.buildResponseCell(s1, s1_q2, s1_r1, "c1");
        s1_r1_q3 = SurveyTestUtility.buildResponseCell(s1, s1_q3, s1_r1, new String[]{"c1"});

        s1_r3_q1 = SurveyTestUtility.buildResponseCell(s1, s1_q1, s1_r3, "hello");
        s1_r3_q2 = SurveyTestUtility.buildResponseCell(s1, s1_q2, s1_r3, "c3");
        s1_r3_q3 = SurveyTestUtility.buildResponseCell(s1, s1_q3, s1_r3, new String[]{"c3"});

        s1_r1.setCells(List.of(s1_r1_q1, s1_r1_q2, s1_r1_q3));
        s1_r3.setCells(List.of(s1_r3_q1, s1_r3_q2, s1_r3_q3));

        cellRepository.save(s1_r1_q1);
        cellRepository.save(s1_r1_q2);
        cellRepository.save(s1_r1_q3);

        cellRepository.save(s1_r3_q1);
        cellRepository.save(s1_r3_q2);
        cellRepository.save(s1_r3_q3);

        s2_r1_q1 = SurveyTestUtility.buildResponseCell(s2, s2_q1, s2_r1, "world");
        s2_r1_q2 = SurveyTestUtility.buildResponseCell(s2, s2_q2, s2_r1, "c2");
        s2_r1_q3 = SurveyTestUtility.buildResponseCell(s2, s2_q3, s2_r1, new String[]{"c2"});
        cellRepository.save(s2_r1_q1);
        cellRepository.save(s2_r1_q2);
        cellRepository.save(s2_r1_q3);

        s3_r1_q1 = SurveyTestUtility.buildResponseCell(s3, s3_q1, s3_r1, "foo");
        s3_r1_q2 = SurveyTestUtility.buildResponseCell(s3, s3_q2, s3_r1, "c3");
        s3_r1_q3 = SurveyTestUtility.buildResponseCell(s3, s3_q3, s3_r1, 1.0);
        s3_r1_q4 = SurveyTestUtility.buildResponseCell(s3, s3_q4, s3_r1, 1);
        cellRepository.save(s3_r1_q1);
        cellRepository.save(s3_r1_q2);
        cellRepository.save(s3_r1_q3);
        cellRepository.save(s3_r1_q4);

        saveToken(mock_token_1, mock_user_1);
        saveToken(mock_token_2, mock_user_2);

        saveOrg(mock_org_1_id);
        saveOrg(mock_org_2_id);

        cacheSurveyNumberLimit(10, mock_user_1);
        cacheSurveyNumberLimit(10, mock_user_2);
    }

    private void saveToken(String token, UserDto user) {
        template.opsForHash().put("session:" + token, "orgId", user.getOrgId().toString());
        template.opsForHash().put("session:" + token, "userId", user.getId().toString());
        template.opsForHash().put("session:" + token, "username", user.getUsername());
    }

    private void cacheSurveyNumberLimit(int num, UserDto user) {
        template.opsForValue().set(String.format("limiter.user.%s.%s", user.getOrgId(), user.getId()), String.valueOf(num));
    }

    private void saveOrg(long id) {
        Organization org = new Organization();
        org.setId(id);
        OrganizationOptionalLimitDto limit = new OrganizationOptionalLimitDto(9999, 99999, 99999, 10000, 10000, 100000, true, true, true, 10000);
        org.setOptionalLimit(JsonHelper.toJson(limit));
        organizationRepository.save(org);
    }


    @SneakyThrows
    private Date createLocalDateTime(String date) {
        return new SimpleDateFormat("yyyy-MM-dd").parse(date);
    }
}
