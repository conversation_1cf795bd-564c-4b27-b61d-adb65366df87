package cn.hanyi.survey.controller;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.constant.analysis.MeasureType;
import cn.hanyi.survey.core.constant.question.QuestionNumberMode;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.entity.SurveyQuestionDto;
import cn.hanyi.survey.core.entity.SurveyQuestionItem;
import cn.hanyi.survey.dto.analysis.CubeDto;
import cn.hanyi.survey.dto.analysis.MeasureDto;
import cn.hanyi.survey.dto.analysis.SingleAnalysisRequest;
import cn.hanyi.survey.dto.survey.SurveyCloneDto;
import org.befun.core.constant.ErrorCode;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.ResourceUpdateItemRequestDto;
import org.befun.core.utils.JsonHelper;
import org.h2.jdbc.JdbcSQLIntegrityConstraintViolationException;
import org.junit.Assert;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SurveyControllerIntegrationTest extends BaseSurveyTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    protected EntityManager entityManager;

    @Test
    public void findAllSurvey() throws Exception {
        mvc.perform(get("/surveys")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(3))
                .andExpect(jsonPath("$.items[0].title").value("survey_1"))
                .andExpect(jsonPath("$.items[0].numOfQuestions").value(3))
                .andExpect(jsonPath("$.items[0].numOfResponses").value(1))
                .andExpect(jsonPath("$.items[1].title").value("survey_2"))
                .andExpect(jsonPath("$.items[1].numOfQuestions").value(3))
                .andExpect(jsonPath("$.items[1].numOfResponses").value(2))
                .andExpect(jsonPath("$.items[0].lastResponseTime").isNotEmpty());
    }

    @Test
    public void findAllSurveyWithInvalidPageNo() throws Exception {
        mvc.perform(get("/surveys?_page=0")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    public void findAllSurveyByTitleContain() throws Exception {
        mvc.perform(get("/surveys?title_contain=2")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(1))
                .andExpect(jsonPath("$.items[0].title").value("survey_2"));
    }

    @Test
    public void findSurveyQuestions() throws Exception {
        mvc.perform(get(String.format("/surveys/%d/questions", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(3));
    }

    @Test
    public void updateSurvey() throws Exception {
        Map<String, Object> data = new HashMap<>();
        data.put("title", "s1_new");
        data.put("id", 123);
        data.put("questionNumberMode", "MANUALLY");

        mvc.perform(put(String.format("/surveys/%d", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d", s1.getId()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("s1_new"));

        Survey ns = surveyRepository.findById(s1.getId()).get();
        Assert.assertNotNull(ns);
        Assert.assertEquals(QuestionNumberMode.MANUALLY, ns.getQuestionNumberMode());
        Assert.assertEquals("s1_new", ns.getTitle());
    }

    @Test
    public void cloneSurvey() throws Exception {

        MvcResult result = mvc.perform(post(String.format("/surveys/%d/clone", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(new SurveyCloneDto()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value(String.format("%s_复制", s1.getTitle())))
                .andReturn();

        ResourceResponseDto cloneResult = objectMapper.readValue(result.getResponse().getContentAsString(), ResourceResponseDto.class);
        LinkedHashMap data = (LinkedHashMap) cloneResult.getData();
        Long ns_id = (Long) data.get("id");

        mvc.perform(get(String.format("/surveys/%d", ns_id))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.status").value(SurveyStatus.STOPPED.name()))
                .andExpect(jsonPath("$.data.questions.length()").value(s1.getQuestions().size()))
                .andExpect(jsonPath("$.data.logics.length()").value(s1.getLogics().size()));
    }

    @Test
    public void updateSurveyStyle() throws Exception {
        Map<String, Object> data = new HashMap<>();
        Map<String, Object> style = new HashMap<>();
        style.put("mainColor", "color_1");

        data.put("title", "s1_new");
        data.put("style", style);

        mvc.perform(put(String.format("/surveys/%d", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d", s1.getId()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("s1_new"))
                .andExpect(jsonPath("$.data.style.mainColor").value("color_1"));

        Survey new_s1 = surveyRepository.findById(s1.getId()).get();
        Assert.assertEquals("s1_new", new_s1.getTitle());
        Assert.assertEquals("color_1", new_s1.getStyle().getMainColor());
    }

    @Test
    public void updateSurveyQuestion() throws Exception {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "q1_new");
        mvc.perform(put(String.format("/surveys/%d/questions/%d", s1.getId(), s1_q1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d/questions/%d", s1.getId(), s1_q1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.name").value("q1_new"));
    }

    @Test
    public void updateSurveyQuestionItem() throws Exception {
        Map<String, Object> data = new HashMap<>();
        data.put("value", "c1_new");
        mvc.perform(put(String.format("/surveys/%d/questions/%d/items/%d", s1.getId(), s1_q2.getId(), s1_q2_c1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d/questions/%d/items/%d", s1.getId(), s1_q2.getId(), s1_q2_c1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.value").value("c1_new"));
    }


    @Test
    public void batchCreateSurveyQuestionItem() throws Exception {
        ArrayList<Object> data = new ArrayList<>(List.of(
                Map.of("value", "c1_new"),
                Map.of("value", "c2_new")
        ));


        mvc.perform(put(String.format("/surveys/%d/questions/%d/items", s1.getId(), s1_q2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.length()").value(data.size()));

        mvc.perform(get(String.format("/surveys/%d/questions/%d/items", s1.getId(), s1_q2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.items.length()").value(s1_q2.getItems().size() + data.size()));
    }

    @Test
    @Disabled
    public void deleteSurveyQuestion() throws JdbcSQLIntegrityConstraintViolationException, Exception {
        // TBD
        mvc.perform(delete(String.format("/surveys/%d/questions/%d", s1.getId(), s1_q1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d", s1.getId()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.numOfQuestions").value(2));
    }

    @Test
    public void deleteSurveyWrongQuestionShouldFailed() throws Exception {
        mvc.perform(delete(String.format("/surveys/%d/questions/%d", s1.getId(), s2_q1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(ErrorCode.ENTITY_NOT_FOUND.getValue()));
    }

    @Test
    public void deleteSurveyQuestionItem() throws Exception {
        mvc.perform(delete(String.format("/surveys/%d/questions/%d/items/%d", s1.getId(), s1_q2.getId(), s1_q2_c1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d/questions/%d/items/%d", s1.getId(), s1_q2.getId(), s1_q2_c1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(ErrorCode.ENTITY_NOT_FOUND.getValue()));
    }

    @Test
    public void findSurveyQuestionItems() throws Exception {
        List<SurveyQuestionItem> items = s1_q2.getItems().stream().collect(Collectors.toList());
        mvc.perform(get(String.format("/surveys/%d/questions/%d/items", s1.getId(), s1_q2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.meta.total").value(3))
                .andExpect(jsonPath("$.items[0].id").value(items.get(0).getId()))
                .andExpect(jsonPath("$.items[0].value").value(items.get(0).getValue()))
                .andExpect(jsonPath("$.items[0].text").value(items.get(0).getText()))
                .andExpect(jsonPath("$.items[1].id").value(items.get(1).getId()))
                .andExpect(jsonPath("$.items[1].value").value(items.get(1).getValue()))
                .andExpect(jsonPath("$.items[1].text").value(items.get(1).getText()))
                .andExpect(jsonPath("$.items[2].id").value(items.get(2).getId()))
                .andExpect(jsonPath("$.items[2].value").value(items.get(2).getValue()))
                .andExpect(jsonPath("$.items[2].text").value(items.get(2).getText()));
    }

    @Test
    public void createSurveyWithOneQuestion() throws Exception {
        List<Map<String, Object>> questions = new ArrayList<>();

        Map<String, Object> q1 = new HashMap<>();
        q1.put("name", "q1");
        q1.put("type", QuestionType.TEXT.name());
        questions.add(q1);

        Map<String, Object> data = new HashMap<>();
        data.put("title", "s4");
        data.put("description", "s4 description");
        data.put("questions", questions);

        MvcResult result = mvc.perform(post(String.format("/surveys"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("s4"))
                .andReturn();

        ResourceResponseDto newResult = objectMapper.readValue(result.getResponse().getContentAsString(), ResourceResponseDto.class);
        LinkedHashMap newData = (LinkedHashMap) newResult.getData();
        Long ns_id = (Long) newData.get("id");

        Assert.assertFalse(newData.get("createTime").toString().isEmpty());
        Assert.assertFalse(newData.get("modifyTime").toString().isEmpty());

        mvc.perform(get(String.format("/surveys/%d", ns_id))
                        .contentType(MediaType.APPLICATION_JSON)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1))

                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("s4"))
                .andExpect(jsonPath("$.data.questions.length()").value(1));

        Survey ns = surveyRepository.findById(Long.valueOf(ns_id)).get();
        Assert.assertNotNull(ns);
        Assert.assertEquals(mock_user_1.getOrgId(), ns.getOrgId());
        Assert.assertEquals(mock_user_1.getId(), ns.getUserId());
    }

    @Test
    public void createSurveyWithOneQuestionWithItems() throws Exception {
        List<Map<String, Object>> items = new ArrayList<>();
        Map<String, Object> c1 = new HashMap<>();
        c1.put("value", "c1");
        c1.put("text", "c1");
        items.add(c1);

        List<Map<String, Object>> questions = new ArrayList<>();

        Map<String, Object> q1 = new HashMap<>();
        q1.put("name", "q1");
        q1.put("type", QuestionType.TEXT.name());
        q1.put("items", items);
        questions.add(q1);

        Map<String, Object> data = new HashMap<>();
        data.put("title", "s4");
        data.put("description", "s4 description");
        data.put("questions", questions);

        MvcResult result = mvc.perform(post(String.format("/surveys"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("s4"))
                .andExpect(jsonPath("$.data.questions.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items[0].value").value("c1"))
                .andReturn();

        ResourceResponseDto newResult = objectMapper.readValue(result.getResponse().getContentAsString(), ResourceResponseDto.class);
        LinkedHashMap newData = (LinkedHashMap) newResult.getData();
        Long ns_id = (Long) newData.get("id");

        mvc.perform(get(String.format("/surveys/%d", ns_id))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("s4"))
                .andExpect(jsonPath("$.data.questions.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items[0].value").value("c1"));
    }

    @Test
    public void createSurveyAreaOneQuestion() throws Exception {
        List<Map<String, Object>> items = new ArrayList<>();
        items.addAll(List.of(
                Map.of(
                        "value", "c1",
                        "text", "省",
                        "sequence", 1
                ),
                Map.of(
                        "value", "c2",
                        "text", "市",
                        "sequence", 2
                ),
                Map.of(
                        "value", "c3",
                        "text", "区",
                        "sequence", 3
                ),
                Map.of(
                        "value", "c3",
                        "text", "地址",
                        "sequence", 4
                )
        ));

        List<Map<String, Object>> questions = new ArrayList<>();

        Map<String, Object> q1 = new HashMap<>();
        q1.put("name", "q1");
        q1.put("type", QuestionType.AREA.name());
        q1.put("items", items);
        questions.add(q1);

        Map<String, Object> data = new HashMap<>();
        data.put("title", "s4");
        data.put("description", "s4 description");
        data.put("questions", questions);

        mvc.perform(post(String.format("/surveys"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("s4"))
                .andExpect(jsonPath("$.data.questions.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items.length()").value(4))
                .andExpect(jsonPath("$.data.questions[0].items[0].text").value("省"))
                .andExpect(jsonPath("$.data.questions[0].items[1].text").value("市"))
                .andExpect(jsonPath("$.data.questions[0].items[2].text").value("区"))
                .andExpect(jsonPath("$.data.questions[0].items[3].text").value("地址"));

    }

    @Test
    public void softDeleteSurvey() throws Exception {
        Assert.assertEquals(3, questionRepository.findAllBySurvey(s1).size());

        mvc.perform(delete(String.format("/surveys/%d", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Optional<Survey> ns1 = surveyRepository.findById(s1.getId());
        Assert.assertTrue(ns1.isEmpty());

        Query q = entityManager.createNativeQuery(String.format("SELECT deleted FROM survey where id=%d", s1.getId()));
        List<Object> q_s = q.getResultList();
        Assert.assertTrue(q_s.size() == 1);
        Assert.assertTrue(((Boolean) q_s.get(0)) == true);
    }

    @Test
    public void createQuestionWithItem() throws Exception {
        s1.setStatus(SurveyStatus.STOPPED);
        surveyRepository.save(s1);
        Map<String, Object> data = new HashMap<>();
        data.put("name", "qm1");
        data.put("title", "qm1 title");
        mvc.perform(post(String.format("/surveys/%d/questions", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.name").value("qm1"))
                .andExpect(jsonPath("$.data.title").value("qm1 title"));
    }

    @Test
    public void batchUpdateChangeQuestionSequence() throws Exception {

        ResourceBatchUpdateRequestDto<SurveyQuestionDto> data = new ResourceBatchUpdateRequestDto<>();
        ResourceUpdateItemRequestDto<SurveyQuestionDto> s1_q1_change = new ResourceUpdateItemRequestDto<>();
        SurveyQuestionDto put = new SurveyQuestionDto();
        put.setSequence(BigDecimal.valueOf(2));
        s1_q1_change.setData(put);
        s1_q1_change.setId(s1_q1.getId());

        ResourceUpdateItemRequestDto<SurveyQuestionDto> s1_q2_change = new ResourceUpdateItemRequestDto<>();
        put = new SurveyQuestionDto();
        put.setSequence(BigDecimal.valueOf(1));
        s1_q2_change.setData(put);
        s1_q2_change.setId(s1_q2.getId());

        data.setChanges(List.of(s1_q1_change, s1_q2_change));

        mvc.perform(post(String.format("/surveys/%d/questions/batch", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        Optional<SurveyQuestion> new_s1_q1 = questionRepository.findById(s1_q1.getId());
        Assert.assertTrue(new_s1_q1.isPresent());
        Assert.assertTrue(new_s1_q1.get().getSequence().compareTo(BigDecimal.valueOf(2)) == 0);

        Optional<SurveyQuestion> new_s1_q2 = questionRepository.findById(s1_q2.getId());
        Assert.assertTrue(new_s1_q1.isPresent());
        Assert.assertTrue(new_s1_q2.get().getSequence().compareTo(BigDecimal.valueOf(1)) == 0);
    }

    @Test
    public void insertQuestionFirstAndMiddleAndLast() throws Exception {
        SurveyQuestionItem itemFirst = new SurveyQuestionItem();
        itemFirst.setText("i1");
        itemFirst.setValue("i1");

        SurveyQuestionItem itemMiddle = new SurveyQuestionItem();
        itemMiddle.setText("i2");
        itemMiddle.setValue("i2");

        SurveyQuestionItem itemLast = new SurveyQuestionItem();
        itemLast.setText("i3");
        itemLast.setValue("i3");

        SurveyQuestion questionFirst = new SurveyQuestion();
        questionFirst.setType(QuestionType.TEXT);
        questionFirst.setName("nq1");
        questionFirst.setTitle("nq1");
        questionFirst.setSequence(BigDecimal.valueOf(1));
        questionFirst.setItems(new ArrayList<>(Arrays.asList(new SurveyQuestionItem[]{itemFirst})));

        SurveyQuestion questionMiddle = new SurveyQuestion();
        questionMiddle.setType(QuestionType.TEXT);
        questionMiddle.setName("nq2");
        questionMiddle.setTitle("nq2");
        questionMiddle.setSequence(BigDecimal.valueOf(2));
        questionMiddle.setItems(new ArrayList<>(Arrays.asList(new SurveyQuestionItem[]{itemMiddle})));

        SurveyQuestion questionLast = new SurveyQuestion();
        questionLast.setType(QuestionType.TEXT);
        questionLast.setName("nq3");
        questionLast.setTitle("nq3");
        questionLast.setSequence(BigDecimal.valueOf(2));
        questionLast.setItems(new ArrayList<>(Arrays.asList(new SurveyQuestionItem[]{itemLast})));

        s4.setStatus(SurveyStatus.STOPPED);
        surveyRepository.save(s4);

        // 没有题型时插入新题型
        mvc.perform(post(String.format("/surveys/%d/insert", s4.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(questionFirst))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.data.questions.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].title").value("nq1"))
                .andExpect(jsonPath("$.data.questions[0].sequence").value(1));

        // 末尾插入新题型
        mvc.perform(post(String.format("/surveys/%d/insert", s4.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(questionLast))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.data.questions.length()").value(2))
                .andExpect(jsonPath("$.data.questions[1].title").value("nq3"))
                .andExpect(jsonPath("$.data.questions[0].sequence").value(1))
                .andExpect(jsonPath("$.data.questions[1].sequence").value(2));

        // 中间插入新题型
        mvc.perform(post(String.format("/surveys/%d/insert", s4.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(questionMiddle))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.data.questions.length()").value(3))
                .andExpect(jsonPath("$.data.questions[1].title").value("nq2"))
                .andExpect(jsonPath("$.data.questions[0].sequence").value(1))
                .andExpect(jsonPath("$.data.questions[1].sequence").value(2))
                .andExpect(jsonPath("$.data.questions[2].sequence").value(3));
    }

    @Test
    public void insertQuestion() throws Exception {
        SurveyQuestionItem item1 = new SurveyQuestionItem();
        item1.setText("i1");
        item1.setValue("i1");

        SurveyQuestionItem item2 = new SurveyQuestionItem();
        item2.setText("i2");
        item2.setValue("i2");

        SurveyQuestion question1 = new SurveyQuestion();
        question1.setType(QuestionType.TEXT);
        question1.setName("nq1");
        question1.setTitle("nq1");
        question1.setSequence(BigDecimal.valueOf(1));
        question1.setItems(new ArrayList<>(Arrays.asList(new SurveyQuestionItem[]{item1})));

        SurveyQuestion question2 = new SurveyQuestion();
        question2.setType(QuestionType.TEXT);
        question2.setName("nq2");
        question2.setTitle("nq2");
        question2.setSequence(BigDecimal.valueOf(5));
        question2.setItems(new ArrayList<>(Arrays.asList(new SurveyQuestionItem[]{item2})));

        s1.setStatus(SurveyStatus.STOPPED);
        surveyRepository.save(s1);

        mvc.perform(post(String.format("/surveys/%d/insert", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(question1))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.questions.length()").value(4))
                .andExpect(jsonPath("$.data.questions[0].name").value("nq1"))
                .andExpect(jsonPath("$.data.questions[0].sequence").value(1))
                .andExpect(jsonPath("$.data.questions[1].name").value(s1_q1.getName()));

        mvc.perform(post(String.format("/surveys/%d/insert", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(question2))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.questions.length()").value(5))
                .andExpect(jsonPath("$.data.questions[4].name").value("nq2"))
                .andExpect(jsonPath("$.data.questions[4].sequence").value(5))
                .andExpect(jsonPath("$.data.questions[1].name").value(s1_q1.getName()));

        Optional<SurveyQuestion> new_s1_q1 = questionRepository.findById(s1_q1.getId());
        Assert.assertTrue(new_s1_q1.isPresent());
        Assert.assertTrue(new_s1_q1.get().getSequence().compareTo(BigDecimal.valueOf(2)) == 0);

        Optional<SurveyQuestion> new_s1_q2 = questionRepository.findById(s1_q2.getId());
        Assert.assertTrue(new_s1_q2.isPresent());
        Assert.assertTrue(new_s1_q2.get().getSequence().compareTo(BigDecimal.valueOf(3)) == 0);

        Optional<SurveyQuestion> new_s1_q3 = questionRepository.findById(s1_q3.getId());
        Assert.assertTrue(new_s1_q3.isPresent());
        Assert.assertTrue(new_s1_q3.get().getSequence().compareTo(BigDecimal.valueOf(4)) == 0);

        Optional<SurveyQuestion> nq1 = questionRepository.findBySurveyAndName(s1, "nq1");
        Assert.assertTrue(nq1.isPresent());
        Assert.assertTrue(nq1.get().getSequence().compareTo(BigDecimal.valueOf(1)) == 0);
        Assert.assertEquals(1, nq1.get().getItems().size());
        Assert.assertEquals("i1", nq1.get().getItems().stream().findFirst().get().getText());

        Optional<SurveyQuestion> nq2 = questionRepository.findBySurveyAndName(s1, "nq2");
        Assert.assertTrue(nq2.isPresent());
        Assert.assertTrue(nq2.get().getSequence().compareTo(BigDecimal.valueOf(5)) == 0);
        Assert.assertEquals(1, nq2.get().getItems().size());
        Assert.assertEquals("i2", nq2.get().getItems().stream().findFirst().get().getText());
    }

    @Test
    public void analysisWithFilterSurvey() throws Exception {

        MeasureDto measure = MeasureDto.builder()
                .field("id")
                .name("count")
                .title("数量")
                .type(MeasureType.COUNT)
                .build();
        CubeDto cube = new CubeDto();
        cube.getMeasures().add(measure);

        mvc.perform(post(String.format("/surveys/%d/analysis", s1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(cube))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].count").value(3));

        mvc.perform(post(String.format("/surveys/%d/analysis", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(cube))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data[0].count").value(3));
    }

    @Test
    public void analysisSingleQuestion() throws Exception {
        String clientId = "random_client_01";
        Map<String, Object> answer = new HashMap<>();
        Map<String, Integer> matrixScore = Map.of(
                "c1", 5,
                "c2", 6,
                "c3", 7
        );

        answer.put("q3", "text");
        answer.put("q4", "c1");
        answer.put("q5", new String[]{"c1", "c2"});
        answer.put("q7", 10);
        answer.put("q8", 18);
        answer.put("q9", "13333333333");
        answer.put("q10", "<EMAIL>");
        answer.put("q11", 5);
        answer.put("q12", matrixScore);
        answer.put("q13", new String[]{"省", "市", "区"});

        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsEarlyCompleted(false);
        request.setIsCompleted(true);
        request.setDurationSeconds(10);
        request.setData(answer);

        MvcResult response = mvc.perform(post(String.format("/surveys/%d/submit", sf.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200)).andReturn();

        MvcResult total = mvc.perform(get(String.format("/surveys/%d/analysis", sf.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_2)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200)).andReturn();


        total.getResponse().setCharacterEncoding("UTF-8");
        List<Map> rows = (List) ((Map) objectMapper.readValue(total.getResponse().getContentAsString(), Map.class).get("data")).get("rows");
        Assert.assertEquals(rows.get(0).get("value"), 2);

        SingleAnalysisRequest data = new SingleAnalysisRequest();
        data.setSubmitDate(new Date());
        data.setEndDate(new Date());
        data.setProvince("广东");
        data.setCity("深圳");
        MvcResult singleChoice = mvc.perform(
                        post(String.format("/surveys/%d/analysis/%d",
                                sf.getId(), sf.getQuestions().stream().filter(q -> q.getType() == QuestionType.SINGLE_CHOICE).findFirst().get().getId()))
                                .header(HttpHeaders.AUTHORIZATION, mock_token_2)
                                .content(objectMapper.writeValueAsString(data))
                                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200)).andReturn();

        singleChoice.getResponse().setCharacterEncoding("UTF-8");
        List<Map> singleChoiceRows = (List) ((Map) objectMapper.readValue(singleChoice.getResponse().getContentAsString(), Map.class).get("data")).get("rows");
        //Assert.assertEquals(singleChoiceRows.get(0).get("value"), 1);

        // m2数据库不支持if函数不能做多选、矩阵分析

        MvcResult score = mvc.perform(
                        post(String.format("/surveys/%d/analysis/%d",
                                sf.getId(), sf.getQuestions().stream().filter(q -> q.getType() == QuestionType.SCORE).findFirst().get().getId()))
                                .header(HttpHeaders.AUTHORIZATION, mock_token_2)
                                .content(objectMapper.writeValueAsString(data))
                                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200)).andReturn();

        score.getResponse().setCharacterEncoding("UTF-8");
        List<Map> scoreRows = (List) ((Map) objectMapper.readValue(score.getResponse().getContentAsString(), Map.class).get("data")).get("rows");
        //Assert.assertEquals(scoreRows.get(10).get("value"), 1);

    }


    @Test
    public void surveyClientData() throws Exception {

        MvcResult resultData = mvc.perform(get(String.format("/surveys/%d/start/%s", s1.getId(), s1_r1.getClientId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(new SurveySubmitRequestDto()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(s1.getId()))
                .andReturn();

        Map resultDataMap = objectMapper.readValue(resultData.getResponse().getContentAsString(), Map.class);
        Map surveyClientResponseDto = objectMapper.convertValue(resultDataMap.get("data"), Map.class);
        Map<String, Object> cellData = (Map<String, Object>) surveyClientResponseDto.get("cellData");
        Assert.assertEquals(cellData.get(s1_q1.getName()), s1_r1_q1.getValue());
        Assert.assertEquals(cellData.get(s1_q2.getName()), s1_r1_q2.getValue());
        Assert.assertEquals(cellData.get(s1_q3.getName()), s1_r1_q3.getValue());
    }


    @Test
    @Disabled
    public void cacheTest() throws Exception {
        List<Map<String, Object>> items = new ArrayList<>();
        Map<String, Object> c1 = new HashMap<>();
        c1.put("value", "c1");
        c1.put("text", "c1");
        items.add(c1);

        List<Map<String, Object>> questions = new ArrayList<>();

        Map<String, Object> q1 = new HashMap<>();
        q1.put("name", "q1");
        q1.put("type", QuestionType.TEXT.name());
        q1.put("items", items);
        questions.add(q1);

        Map<String, Object> data = new HashMap<>();
        data.put("title", "cache");
        data.put("description", "cache description");
        data.put("questions", questions);

        MvcResult result = mvc.perform(post(String.format("/surveys"))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(data))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("cache"))
                .andExpect(jsonPath("$.data.questions.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items[0].value").value("c1"))
                .andReturn();

        ResourceResponseDto newResult = objectMapper.readValue(result.getResponse().getContentAsString(), ResourceResponseDto.class);
        LinkedHashMap newData = (LinkedHashMap) newResult.getData();
        Long ns_id = (Long) newData.get("id");

        mvc.perform(get(String.format("/surveys/%d", ns_id))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("cache"))
                .andExpect(jsonPath("$.data.questions.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items.length()").value(1))
                .andExpect(jsonPath("$.data.questions[0].items[0].value").value("c1"));

        Map<String, Object> updateSurvey = new HashMap<>();
        updateSurvey.put("title", "cacheUpdate");

        mvc.perform(put(String.format("/surveys/%d", ns_id))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(updateSurvey))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("cacheUpdate"));

        Map<String, Object> updateItems = new HashMap<>();
        updateItems.put("text", "c1Update");
        Map question = (Map) ((List) newData.get("questions")).get(0);
        Long i_id = (Long) ((Map) ((List) question.get("items")).get(0)).get("id");
        Long q_id = (Long) ((Map) ((List) newData.get("questions")).get(0)).get("id");

        mvc.perform(put(String.format("/surveys/%d/questions/%d/items/%d", ns_id, q_id, i_id))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(updateItems))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.text").value("c1Update"));


        mvc.perform(delete(String.format("/surveys/%d/questions/%d", ns_id, q_id))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d", ns_id))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("cacheUpdate"))
                .andExpect(jsonPath("$.data.questions.length()").value(0));
    }

}
