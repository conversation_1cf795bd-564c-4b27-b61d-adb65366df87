package cn.hanyi.survey.controller;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.service.ResponseService;
import cn.hanyi.survey.service.SurveyService;
import com.google.gson.Gson;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.utils.JsonHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.persistence.EntityManager;
import java.util.*;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class ChannelControllerTest extends BaseSurveyTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    protected EntityManager entityManager;

    @Autowired
    protected SurveyService surveyService;

    @Autowired
    protected ResponseService responseService;

    @Test
    public void surveyChannelOperate() throws Exception {
        responseService.cleanResponse(s2);

        SurveyChannel channelPhoneMsg = new SurveyChannel();
        channelPhoneMsg.setSid(s2.getId());
        channelPhoneMsg.setType(ChannelType.PHONE_MSG);
        //创建渠道
        //短信渠道
        mvc.perform(post(String.format("/surveys/%s/channel", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(channelPhoneMsg)))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.type").value(ChannelType.PHONE_MSG.toString()))
                .andExpect(jsonPath("$.data.status").value(ChannelStatus.UNSET.toString()))
                .andExpect(jsonPath("$.data.name").value(ChannelType.PHONE_MSG.getText() + 1))
                .andExpect(jsonPath("$.data.configure").isEmpty())
                .andExpect(jsonPath("$.data.createUser").value(mock_user_1.getUsername()));
        //短链接渠道
        SurveyChannel channelShortLink = new SurveyChannel();
        channelShortLink.setSid(s2.getId());
        channelShortLink.setType(ChannelType.SHORT_LINK);
        channelShortLink.setCreateUser("leo");
        MvcResult result = mvc.perform(post(String.format("/surveys/%s/channel", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(channelShortLink)))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.type").value(ChannelType.SHORT_LINK.toString()))
                .andExpect(jsonPath("$.data.status").value(ChannelStatus.RECOVERY.toString()))
                .andExpect(jsonPath("$.data.name").value(ChannelType.SHORT_LINK.getText() + 1))
                .andExpect(jsonPath("$.data.configure").isNotEmpty())
                .andExpect(jsonPath("$.data.createUser").value(mock_user_1.getUsername())).andReturn();
        //微信公众号
        SurveyChannel channelWechat = new SurveyChannel();
        channelWechat.setSid(s2.getId());
        channelWechat.setType(ChannelType.WECHAT_SERVICE);
        channelWechat.setCreateUser("leo");
        mvc.perform(post(String.format("/surveys/%s/channel", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(channelWechat)))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.type").value(ChannelType.WECHAT_SERVICE.toString()))
                .andExpect(jsonPath("$.data.status").value(ChannelStatus.UNSET.toString()))
                .andExpect(jsonPath("$.data.name").value(ChannelType.WECHAT_SERVICE.getText() + 1))
                .andExpect(jsonPath("$.data.configure").isEmpty())
                .andExpect(jsonPath("$.data.createUser").value(mock_user_1.getUsername()));
        //页面嵌入
        SurveyChannel channelInjectWeb = new SurveyChannel();
        channelInjectWeb.setSid(s2.getId());
        channelInjectWeb.setType(ChannelType.INJECT_WEB);
        channelInjectWeb.setCreateUser("leo");
        mvc.perform(post(String.format("/surveys/%s/channel", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(channelInjectWeb)))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.type").value(ChannelType.INJECT_WEB.toString()))
                .andExpect(jsonPath("$.data.status").value(ChannelStatus.RECOVERY.toString()))
                .andExpect(jsonPath("$.data.name").value(ChannelType.INJECT_WEB.getText() + 1))
                .andExpect(jsonPath("$.data.configure").isNotEmpty())
                .andExpect(jsonPath("$.data.createUser").value(mock_user_1.getUsername()));
        //获取渠道列表
        mvc.perform(get(String.format("/surveys/%s/channel", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.items.size()").value(4));
        //获取渠道详情
        ResourceResponseDto createResult = objectMapper.readValue(result.getResponse().getContentAsString(), ResourceResponseDto.class);
        LinkedHashMap data = (LinkedHashMap) createResult.getData();
        Long channelId = (Long) data.get("id");
        MvcResult result1 = mvc.perform(get(String.format("/surveys/%s/channel/%s", s2.getId(), channelId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(channelId))
                .andReturn();

        //更新渠道信息
        ResourceResponseDto findOneResult = objectMapper.readValue(result1.getResponse().getContentAsString(), ResourceResponseDto.class);
        SurveyChannel channelUpdate = objectMapper.convertValue(findOneResult.getData(), SurveyChannel.class);
        channelUpdate.setName("测试用例");
        mvc.perform(put(String.format("/surveys/%s/channel/%s", s2.getId(), channelId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new Gson().toJson(channelUpdate)))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.name").value(channelUpdate.getName()));

        //暂停问卷渠道
        mvc.perform(put(String.format("/surveys/%s/channel/%s/pause", s2.getId(), channelId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));


        //重启问卷渠道
        mvc.perform(put(String.format("/surveys/%s/channel/%s/restart", s2.getId(), channelId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        //关闭问卷渠道
        mvc.perform(put(String.format("/surveys/%s/channel/%s/close", s2.getId(), channelId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        //清除渠道数据
        mvc.perform(delete(String.format("/surveys/%s/channel/%s/wipe-data", s2.getId(), channelId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        //删除问卷渠道
        mvc.perform(delete(String.format("/surveys/%s/channel/%s/delete", s2.getId(), channelId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));


    }

    @Test
    public void surveyChannelVerifyDate() throws Exception {
        responseService.cleanResponse(s2);
        String clientId = "random_client_01";

        SurveyChannel surveyChannel = new SurveyChannel();
        surveyChannel.setSid(s2.getId());
        surveyChannel.setType(ChannelType.PHONE_MSG);
        surveyChannel.setName("短信渠道1");
        surveyChannel.setStatus(ChannelStatus.RECOVERY);
        surveyChannel.setEnableDateLimit(true);

        Date today = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.DAY_OF_MONTH, -1);
        Date yesterday = c.getTime();
        surveyChannel.setEndTime(yesterday);
        surveyChannel.setEnableDateLimit(true);

        SurveyChannel savedChannel = channelRepository.save(surveyChannel);
        SurveySubmitRequestDto submitData = new SurveySubmitRequestDto();
        submitData.setChannelId(savedChannel.getId());

        mvc.perform(get(String.format("/surveys/%d/start/%s?channelId=%d", s2.getId(), clientId, savedChannel.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(submitData))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(SurveyErrorCode.SURVEY_IS_END.getValue()));

    }

    @Test
    public void surveyChannelVerifyIp() throws Exception {
        responseService.cleanResponse(s2);
        String clientId = "random_client_01";

        SurveyChannel surveyChannel = new SurveyChannel();
        surveyChannel.setSid(s2.getId());
        surveyChannel.setType(ChannelType.PHONE_MSG);
        surveyChannel.setName("短信渠道1");
        surveyChannel.setStatus(ChannelStatus.RECOVERY);
        surveyChannel.setEnableDateLimit(true);
        surveyChannel.setEnableIpLimit(true);

        SurveyChannel savedChannel = channelRepository.save(surveyChannel);

        mvc.perform(get(String.format("/surveys/%d/start/%s?channelId=%d", s2.getId(), clientId, savedChannel.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(new SurveySubmitRequestDto()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));


        Map<String, Object> answer = new HashMap<>();
        answer.put("q1", "text");

        SurveySubmitRequestDto request = new SurveySubmitRequestDto();
        request.setClientId(clientId);
        request.setIsEarlyCompleted(false);
        request.setIsCompleted(true);
        request.setDurationSeconds(10);
        request.setData(answer);

        mvc.perform(post(String.format("/surveys/%d/submit", s2.getId()))
                        .content(objectMapper.writeValueAsString(request))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%d/start/%s?channelId=%d", s2.getId(), clientId, savedChannel.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(new SurveySubmitRequestDto()))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(SurveyErrorCode.ALREADY_SUBMIT.getValue()));


    }

}
