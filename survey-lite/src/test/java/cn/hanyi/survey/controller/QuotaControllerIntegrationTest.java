package cn.hanyi.survey.controller;

import cn.hanyi.survey.BaseSurveyTest;
import cn.hanyi.survey.TestRedisConfiguration;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.entity.SurveyQuota;
import cn.hanyi.survey.service.ResponseService;
import cn.hanyi.survey.service.SurveyService;
import org.befun.core.dto.ResourceResponseDto;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.persistence.EntityManager;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class QuotaControllerIntegrationTest extends BaseSurveyTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    protected EntityManager entityManager;

    @Autowired
    protected SurveyService surveyService;

    @Autowired
    protected ResponseService responseService;

    @Test
    public void quotaAndSubmitResponse() throws Exception {
        responseService.cleanResponse(s2);

        SurveyQuota surveyQuota = new SurveyQuota();
        surveyQuota.setSurvey(s2);
        surveyQuota.setMax(2);
        surveyQuota.setName("测试用例配额1");
        surveyQuota.setExpression("q2.selected(\"c1\")");
        surveyQuota.setQuestionNames(List.of("q2"));

        mvc.perform(post(String.format("/surveys/%s/stop", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        MvcResult result = mvc.perform(post(String.format("/surveys/%s/quotas", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(surveyQuota))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.name").value(surveyQuota.getName()))
                .andExpect(jsonPath("$.data.max").value(surveyQuota.getMax()))
                .andExpect(jsonPath("$.data.expression").value(surveyQuota.getExpression())).andReturn();

        ResourceResponseDto newResult = objectMapper.readValue(result.getResponse().getContentAsString(), ResourceResponseDto.class);
        LinkedHashMap newData = (LinkedHashMap) newResult.getData();
        Long quotaId = (Long) newData.get("id");

        mvc.perform(post(String.format("/surveys/%s/quotas/enable", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        // 等待配额同步
        TimeUnit.SECONDS.sleep(1);

        mvc.perform(post(String.format("/surveys/%s/publish", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.completed").value(true))
                .andExpect(jsonPath("$.data.process").value(100))
                .andExpect(jsonPath("$.data.status").value("COLLECTING"));


        SurveySubmitRequestDto submitRequestDto = new SurveySubmitRequestDto();
        submitRequestDto.setClientId("c1");
        submitRequestDto.setDurationSeconds(1);
        submitRequestDto.setIsCompleted(true);
        submitRequestDto.setData(Map.of(
                "q2", "c1"
        ));

        mvc.perform(post(String.format("/surveys/%s/submit", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(submitRequestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/surveys/%s/quotas", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.items.size()").value(1))
                .andExpect(jsonPath("$.items[0].name").value(surveyQuota.getName()))
                .andExpect(jsonPath("$.items[0].current").value(1));

        submitRequestDto.setClientId("c2");
        mvc.perform(post(String.format("/surveys/%s/submit", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(submitRequestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));

        submitRequestDto.setClientId("c3");
        mvc.perform(post(String.format("/surveys/%s/submit", s2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(submitRequestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(30002))
                .andExpect(jsonPath("$.message").value("不好意思，该问卷配额已满"));

        surveyQuota.setMax(3);
        mvc.perform(put(String.format("/surveys/%s/quotas/%s/", s2.getId(), quotaId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(surveyQuota))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.id").value(quotaId))
                .andExpect(jsonPath("$.data.current").value(2))
                .andExpect(jsonPath("$.data.max").value(3));

        mvc.perform(delete(String.format("/surveys/%s/quotas/%s/", s2.getId(), quotaId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
    }
}
