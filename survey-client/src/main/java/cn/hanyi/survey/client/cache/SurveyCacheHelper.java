package cn.hanyi.survey.client.cache;

import cn.hanyi.survey.client.cache.bean.ParamSurvey;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 每个问卷的数据存在一个单独的hash结构中，通过hashKey（entity）获取数据
 */
@Slf4j
@Service
public class SurveyCacheHelper extends BaseCacheHelper<ParamSurvey, ClientSurvey> {

    private static final String K = "cache:survey:%d";
    private static final String K_LOCK = "cache:survey:%d-lock";
    private static final String HK_ENTITY = "entity";
    private static final String HK_ID = "id";
    private static final String HK_STATUS = "status";

    public SurveyCacheHelper() {
        super("问卷", false);
    }

    @Autowired
    private ISubmitStorage storage;
    @Autowired
    private LimitSurveyCacheHelper limitSurveyCacheHelper;

    public void stopSurvey(Long surveyId) {
        delete(key(surveyId));
        log.info("问卷{}缓存已清除", surveyId);
    }

    public void publishSurvey(Long surveyId) {
        delete(key(surveyId));
        log.info("问卷{}缓存已清除", surveyId);
    }

    public void deleteSurvey(Long surveyId) {
        delete(key(surveyId));
        log.info("问卷{}缓存已清除", surveyId);
    }

    public ClientSurvey getSurvey(Long surveyId) {
        if (!enableCache) {
            return getDbValue(new ParamSurvey(surveyId));
        }
        return getOrSync(new ParamSurvey(surveyId));
    }

    private String key(Long surveyId) {
        return String.format(K, surveyId);
    }

    @Override
    protected String key(ParamSurvey param) {
        return key(param.surveyId);
    }

    @Override
    protected String lockKey(String key, ParamSurvey param) {
        return String.format(K_LOCK, param.surveyId);
    }

    @Override
    protected ClientSurvey getCacheValue(String key, ParamSurvey param) {
        String cache = hashOpt().get(key, HK_ENTITY);
        if (cache != null) {
            return JsonHelper.toObject(cache, ClientSurvey.class);
        } else {
            List<String> caches = hashOpt().multiGet(key, List.of(HK_ID, HK_STATUS));
            if (CollectionUtils.isNotEmpty(caches) && caches.size() == 2) {
                String id = caches.get(0);
                if (id != null && id.equals(NOT_EXIST_FLAG)) {
                    throw new EntityNotFoundException(Survey.class);
                }
                String status = caches.get(1);
                if (status != null && status.equals(SurveyStatus.STOPPED.name())) {
                    log.info("问卷停止收集 {}", param.surveyId);
                    throw new BadRequestException("不好意思，您访问的问卷已停止收集");
                }
            }
        }
        return null;
    }

    @Override
    protected ClientSurvey getDbValue(ParamSurvey param) {
        return storage.getSurvey(param.surveyId);
    }

    @Override
    protected ClientSurvey syncValue(String key, ParamSurvey param) {
        ClientSurvey survey = getDbValue(param);
        if (survey != null) {
            Map<String, String> cache = Map.of(
                    HK_ENTITY, JsonHelper.toJson(survey),
                    HK_ID, param.surveyId.toString(),
                    HK_STATUS, survey.getStatus().name()
            );
            stringRedisTemplate.opsForHash().putAll(key, cache);
//            limitSurveyCacheHelper.async(survey.getId()); // 开始异步缓存答卷限制数据
            return survey;
        } else {
            stringRedisTemplate.opsForHash().put(key, HK_ID, NOT_EXIST_FLAG);
            log.info("问卷无效，并标记 {}", param.surveyId);
            throw new EntityNotFoundException(Survey.class);
        }
    }
}
