package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.cache.LimitSurveyCacheHelper;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

@Order(1020)
@Component
@Slf4j
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class BeforeLimiterSurvey implements IBeforeLimiter {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private LimitSurveyCacheHelper limitSurveyCacheHelper;

    @Override
    public void check(SubmitContext context) {
        ClientSurvey survey = loader.requireSurvey(context);
        checkStatus(survey);
        checkIp(context, survey);
        checkCount(context, survey);
        checkDateRange(survey);
    }

    private void checkStatus(ClientSurvey survey) {
        if (survey.getStatus() != SurveyStatus.COLLECTING) {
            throw new BadRequestException("不好意思，您访问的问卷已停止收集");
        }
    }

    private void checkIp(SubmitContext context, ClientSurvey survey) {
        String ip = loader.getIp(context);
        log.info("请求ip:{},clientId:{}", ip, context.getData().getClientId());
        if (!unboxBool(survey.getEnableIpLimit())
                || survey.getIpLimit() == null
                || StringUtils.isEmpty(ip)) {
            return;
        }
        LocalDateTime lastTime = limitSurveyCacheHelper.lastTimeByIp(survey.getId(), ip);
        if (lastTime == null) {
            return;
        }
        if (lastTime.isAfter(survey.getIpLimit().limitTime())) {
            switch (survey.getIpLimit()) {
                case EVERY_DAY -> throw new SurveyErrorException(SurveyErrorCode.IP_DAY_SUBMIT);
                case EVERY_WEEK -> throw new SurveyErrorException(SurveyErrorCode.IP_WEEK_SUBMIT);
                case EVERY_MONTH -> throw new SurveyErrorException(SurveyErrorCode.IP_MONTH_SUBMIT);
                default -> throw new SurveyErrorException(SurveyErrorCode.IP_ALREADY_SUBMIT);
            }
        }
    }

    private void checkCount(SubmitContext context, ClientSurvey survey) {
        if (!unboxBool(survey.getEnableResponseLimit()) || unboxInt(survey.getResponseAmount()) <= 0) {
            return;
        }
//        long count = limitSurveyCacheHelper.count(survey.getId());
        if (survey.getResponseFinishNum() >= survey.getResponseAmount()) {
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);
        }
    }

    private void checkDateRange(ClientSurvey survey) {
        if (unboxBool(survey.getEnableDateLimit())) {
            IBeforeLimiter.checkDateRange(survey.getStartTime(), survey.getEndTime());
        }
    }

}
