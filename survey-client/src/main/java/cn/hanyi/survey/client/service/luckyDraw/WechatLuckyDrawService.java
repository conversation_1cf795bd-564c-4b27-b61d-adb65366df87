package cn.hanyi.survey.client.service.luckyDraw;

import cn.hanyi.survey.client.constant.SurveyLotteryConstant;
import cn.hanyi.survey.client.exception.LotteryException;
import cn.hanyi.survey.client.properties.SendRedPackProperties;
import cn.hanyi.survey.client.service.TrackingService;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.constant.lottery.PrizeSendStatus;
import cn.hanyi.survey.core.constant.lottery.SurveyLotteryStatus;
import cn.hanyi.survey.core.dto.lottery.RedPackResult;
import cn.hanyi.survey.core.dto.lottery.SendRedPackParam;
import cn.hanyi.survey.core.dto.lottery.SendRedPackResult;
import cn.hanyi.survey.core.dto.lottery.SurveyLotteryParam;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.*;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import cn.hutool.core.util.RandomUtil;
import com.github.binarywang.wxpay.bean.request.WxPaySendRedpackRequest;
import com.github.binarywang.wxpay.bean.result.WxPayRedpackQueryResult;
import com.github.binarywang.wxpay.bean.result.WxPaySendRedpackResult;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.service.WxPayService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.GenerateCodeType;
import org.befun.auth.utils.GeneratorHelper;
import org.befun.core.exception.BadRequestException;
import org.befun.core.generator.Snowflake;
import org.befun.extension.service.WeChatPayService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2022/11/7 15:51:52
 */
@Service
@Slf4j
public class WechatLuckyDrawService implements LuckyDrawService {

    @Value("${befun.extension.wechat-pay.app-id}")
    private String appid;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyLotteryRepository surveyLotteryRepository;

    @Autowired
    private SurveyLotteryRepository lotteryRepository;

    @Autowired
    private SurveyLotteryPrizeRepository lotteryPrizeRepository;

    @Autowired
    private SurveyLotteryPrizeWinnerRepository prizeWinnerRepository;

    @Autowired
    private SendRedPackProperties redPackProperties;

    @Autowired
    private TrackingService trackingService;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Autowired
    private SurveyLotteryRedPackSendRecordRepository redPackSendRecordRepository;

    @Autowired
    private ISurveyEventTrigger surveyEventTrigger;

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private WxPayService wxPayService;

    @Autowired
    private HttpServletRequest request;

    private static Snowflake snowflake = new Snowflake();

    @Override
    public LotteryType type() {
        return LotteryType.WECHAT;
    }

    public Survey requireSurvey(@NotNull Long surveyId) {
        Optional<Survey> surveyOptional = surveyRepository.findById(surveyId);
        Survey survey = surveyOptional.orElse(null);
        if (survey == null) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
        }
        return survey;
    }


    @Transactional(rollbackFor = Exception.class)
    public RedPackResult luckyDraw(Long lotteryId, SurveyLotteryParam param) {

        log.info("微信红包开始啦...");
        Long responseId = param.getResponseId();
        SurveyResponse response = surveyResponseRepository.findById(responseId).orElseThrow(() -> new LotteryException("答卷不存在"));
        //判断抽奖次数是否为0
        checkLotteryNum(response.getLotteryNum());
        //活动是否在活动期限内
        SurveyLottery lottery = surveyLotteryRepository.findById(lotteryId).orElseThrow(() -> new LotteryException("活动已结束"));
        checkLottery(lottery);

        //是否正常结束判断
        if (lottery.getIsComplete() && ResponseStatus.EARLY_COMPLETED.equals(response.getStatus())) {
            throw new LotteryException("非常抱歉，您不满足本次抽奖条件");
        }

        checkLotteryStatus(responseId, redisTemplate);

        String key = String.format(SurveyLotteryConstant.RED_PACT_KEY, lotteryId);
        String money = null;
        Boolean isExe = false;
        try {
            //抽奖次数
            response.setLotteryNum(response.getLotteryNum() - 1);
            surveyResponseRepository.save(response);

            RedPackResult result = new RedPackResult();
            //当日奖品限额
            Long winnerSum = redisTemplate.opsForValue().increment(String.format(SurveyLotteryConstant.TODAY_PRIZE_LIMIT, lotteryId, getYearMonthDay()));
            isExe = true;
            if (lottery.getIsLimit() && lottery.getPrizeLimit() != null && winnerSum > lottery.getPrizeLimit()) {
                log.info("当日红包数量达到限制,中奖数量：{}", winnerSum);
                redisTemplate.opsForValue().decrement(String.format(SurveyLotteryConstant.TODAY_PRIZE_LIMIT, lotteryId, getYearMonthDay()));
                return result;
            }
            List<SurveyLotteryPrize> prizes = lotteryPrizeRepository.findByLotteryId(lotteryId);
            if (!existRedPack(prizes)) {
                return result;
            }

            //中奖金额，为null红包被抽完
            money = redisTemplate.opsForList().leftPop(key);
            if (money == null) {
                log.info("红包已被抽完");
                return result;
            }

            //todo 是否开启审核 开启审核不直接发，在奖励核销发，未开启审核直接发

            //更新中奖金额
            SurveyLotteryPrize prize = prizes.get(0);
            prize.setWinnerMoney(Integer.parseInt(money) + prize.getWinnerMoney());
            prize.setWinnerNum(prize.getWinnerNum() + 1);
            lotteryPrizeRepository.save(prize);

            //保存中奖记录
            int amount = Integer.parseInt(money);
            SurveyLotteryPrizeWinner winner = prizeWinnerRepository.save(getPrizeWinner(prize, lotteryId, responseId, amount));

            lottery.setTotalWinnerNum(lottery.getTotalWinnerNum() + 1);
            lotteryRepository.save(lottery);

            result.setMoney(Integer.parseInt(money));
            result.setIsWinner(true);
            result.setIsVerify(true);
            result.setRedirectUrl(param.getRedirectUrl());
            result.setWinnerId(winner.getId());
            result.setAppid(appid);
            if (param.getSurveyId() != null) {
                Survey survey = this.requireSurvey(param.getSurveyId());
                result.setShowBrand(survey.getShowBrand());
                result.setBrandLogo(survey.getBrandLogo());
            }
            return result;
        } catch (Exception e) {
            log.info("发红包异常：{}", e);
            if (money != null) {
                redisTemplate.opsForList().leftPush(key, money);
            }
            if (isExe) {
                redisTemplate.opsForValue().decrement(String.format(SurveyLotteryConstant.TODAY_PRIZE_LIMIT, lotteryId, getYearMonthDay()));
            }
            //抽奖次数
            response.setLotteryNum(response.getLotteryNum() - 1);
            surveyResponseRepository.save(response);

            return new RedPackResult();
        } finally {
            redisTemplate.delete(String.format(SurveyLotteryConstant.LAST_LOTTERY_OVER_LOCK, responseId));
        }
    }

    private Boolean existRedPack(List<SurveyLotteryPrize> prizes) {

        if (prizes.isEmpty()) {
            log.info("红包不存在");
            return false;
        }
        SurveyLotteryPrize prize = prizes.get(0);
        //抽完红包未中奖
        if (prize.getWinnerNum() >= prize.getNumber()) {
            log.info("红包已经抽完");
            return false;
        }
        //未中奖直接返回
        if (!isWinner(prize.getPercent())) {
            log.info("未中奖");
            return false;
        }
        return true;
    }


    /**
     * 判断是否中奖
     *
     * @param percent
     * @return
     */
    public Boolean isWinner(Float percent) {
        percent = percent == null ? 0 : percent;
        double random = RandomUtil.randomDouble(0, 100);
        if (percent > random) {
            return true;
        }
        return false;
    }

    public SurveyLotteryPrizeWinner getPrizeWinner(SurveyLotteryPrize prize, Long lotteryId, Long responseId, int amount) {
        SurveyLotteryPrizeWinner winner = new SurveyLotteryPrizeWinner();
        winner.setType(prize.getType());
        winner.setStatus(PrizeSendStatus.NOT_SEND);
        winner.setLotteryId(lotteryId);
        winner.setResponseId(responseId);
        winner.setPrizeId(prize.getId());
        winner.setSurveyId(prize.getLottery().getSid());
        winner.setPrizeName("微信红包");
        winner.setAmount(amount);
        return winner;
    }

    @Transactional(rollbackFor = Exception.class)
    public SendRedPackResult SendRedPackTask(SendRedPackParam data) {
        SurveyLotteryPrizeWinner winner = prizeWinnerRepository.findById(data.getWinnerId()).orElseThrow(() -> new BadRequestException("中奖信息不存在"));
        if (StringUtils.isNotEmpty(winner.getOpenid())) {
            // 说明已经被扫码领取了
            return new SendRedPackResult();
        }
        winner.setOpenid(data.getOpenid());
        prizeWinnerRepository.save(winner);
        SurveyLottery lottery = lotteryRepository.findById((winner.getLotteryId())).orElseThrow(() -> new BadRequestException("活动不存在"));
        SendRedPackResult result = new SendRedPackResult();
        if (lottery.getIsVerify()) {
            result.setIsVerify(true);
            result.setIsAllow(true);
            result.setMoney(winner.getAmount());
            Survey survey = requireSurvey(winner.getSurveyId());
            surveyEventTrigger.sendRedPackDelay(
                    survey.getOrgId(),
                    winner.getId(),
                    data.getOpenid(),
                    Duration.ofMinutes(redPackProperties.getRefundRedPackDelay())
            );
        } else {
            result = SendRedPack(data);
        }
        return result;
    }

    /**
     * 发红包
     *
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public SendRedPackResult SendRedPack(SendRedPackParam data) {
        SendRedPackResult result = new SendRedPackResult();

        SurveyLotteryPrizeWinner winner = prizeWinnerRepository.findById(data.getWinnerId()).orElseThrow(() -> new BadRequestException("中奖信息不存在"));
        //已被领取
        if (winner.getIsReceived()) {
            return result;
        }
        //已作废
        if (winner.getStatus() == PrizeSendStatus.INVALID) {
            result.setIsSuccess(false);
            return result;
        }

        String key = String.format(SurveyLotteryConstant.SEND_RED_PACK_LOCK, data.getWinnerId(), data.getOpenid());
        Boolean lock = redisTemplate.opsForValue().setIfAbsent(key, "1", 10, TimeUnit.SECONDS);
        if (!lock) {
            return result;
        }
        WxPaySendRedpackRequest wxPaySendRedpackRequest = new WxPaySendRedpackRequest();
        WxPaySendRedpackResult wxPaySendRedpackResult = null;
        SurveyLottery require = lotteryRepository.findById((winner.getLotteryId())).orElseThrow(() -> new BadRequestException("活动不存在"));
        final Survey survey = this.requireSurvey(require.getSid());
        try {
            SurveyResponse response = surveyResponseRepository.findById(winner.getResponseId()).orElseThrow(() -> new BadRequestException("答卷不存在"));
            if (SurveyLotteryStatus.CLOSED.equals(require.getStatus())) {
                throw new BadRequestException("活动已结束");
            }

            buildWxPaySendRedpackRequest(wxPaySendRedpackRequest, response.getIp(), require.getSendLotteryName(), data.getOpenid(), winner.getAmount());
            //发红包
            wxPaySendRedpackResult = weChatPayService.getRedpackService().sendRedpack(wxPaySendRedpackRequest);

            //todo 需要审核不发,超过48小时自动发放，已发放则不发

            result = checkRedSendStatus(wxPaySendRedpackResult, wxPaySendRedpackRequest, winner, winner.getId(), data.getOpenid());
            if (result.getIsAllow()) {
                //24小时未领取，回退红包到余额
                surveyEventTrigger.refundRedPack(survey.getOrgId(), require.getSid(), winner.getLotteryId(), require.getOrderId(), winner.getId(), wxPaySendRedpackResult.getMchBillNo(), Duration.ofMinutes(redPackProperties.getRefundRedPack()));
            }
            return result;
        } catch (WxPayException e) {
            log.info("红包发送失败：{} " + e);
            saveRedPackSendRecord(wxPaySendRedpackRequest.toXML(), data.getWinnerId(), false, e.toString());
            result.setErrCode(e.getErrCode());
            surveyEventTrigger.refundRedPack(survey.getOrgId(), require.getSid(), winner.getLotteryId(), require.getOrderId(), winner.getId(), wxPaySendRedpackRequest.getMchBillNo(), Duration.ofMinutes(redPackProperties.getRefundRedPack()));
            return result;
        } finally {
            redisTemplate.delete(key);
        }

    }

    /**
     * 查询红包信息
     *
     * @param request
     * @param mchBillNo 商户号
     * @return
     */
    public WxPayRedpackQueryResult gethbinfo(HttpServletRequest request, String mchBillNo) {
        WxPayRedpackQueryResult result = null;
        try {
            result = wxPayService.getRedpackService().queryRedpack(mchBillNo);
        } catch (WxPayException e) {
            log.info("查询异常：{}", e);
        }
        return result;
    }

    public void buildWxPaySendRedpackRequest(WxPaySendRedpackRequest wxPaySendRedpackRequest, String clientIp, String sendName, String openId, int amount) {
        wxPaySendRedpackRequest.setClientIp(clientIp);
        String mchBillno = GeneratorHelper.generatorVerifyCode(28, GenerateCodeType.NUMBER);
        wxPaySendRedpackRequest.setMchBillNo(mchBillno);
        wxPaySendRedpackRequest.setReOpenid(openId);
        wxPaySendRedpackRequest.setNonceStr(GeneratorHelper.generatorPayNo());
        wxPaySendRedpackRequest.setNonceStr(UUID.randomUUID().toString().replace("-", ""));
        wxPaySendRedpackRequest.setTotalAmount(amount);
        wxPaySendRedpackRequest.setTotalNum(redPackProperties.getTotalNum());
        wxPaySendRedpackRequest.setSendName(StringUtils.isNotEmpty(sendName) ? sendName : redPackProperties.getSendName());
        wxPaySendRedpackRequest.setWishing(redPackProperties.getWishing());
        wxPaySendRedpackRequest.setActName(redPackProperties.getActName());
        wxPaySendRedpackRequest.setRemark(redPackProperties.getRemark());
        wxPaySendRedpackRequest.setSceneId(redPackProperties.getSceneId());
        wxPaySendRedpackRequest.setRiskInfo(redPackProperties.getRiskInfo());
    }

    public SendRedPackResult checkRedSendStatus(WxPaySendRedpackResult result, WxPaySendRedpackRequest request, SurveyLotteryPrizeWinner winner, Long winnerId, String openId) {
        SendRedPackResult redPackResult = new SendRedPackResult();
        String returnCode = result.getReturnCode();
        if ("SUCCESS".equals(returnCode)) {
            String resultCode = result.getResultCode();
            if ("SUCCESS".equals(resultCode)) {
                saveWinner(winner);
                saveRedPackSendRecord(request.toXML(), winnerId, true, "发送成功,openId=" + openId);
                redPackResult.setIsAllow(true);
                redPackResult.setIsSuccess(true);
                redPackResult.setMoney(result.getTotalAmount());
                return redPackResult;
            } else {
                log.info("result_code:{}", returnCode);
                saveRedPackSendRecord(request.toXML(), winnerId, false, result.getErrCode() + ":" + result.getErrCodeDes());
                return redPackResult;
            }
        } else {
            log.info("return_code:{}", returnCode);
            saveRedPackSendRecord(request.toXML(), winnerId, false, result.getReturnMsg());
            return redPackResult;
        }
    }

    void saveWinner(SurveyLotteryPrizeWinner winner) {
        // 更新红包领取状态
        winner.setStatus(PrizeSendStatus.IS_SENT);
        winner.setIsReceived(true);
        winner.setSendTime(new Date());
        prizeWinnerRepository.save(winner);
    }

    public void saveRedPackSendRecord(String requestData, Long winnerId, Boolean isSuccess, String errorInfo) {
        SurveyLotteryRedPackSendRecord redPackSendRecord = new SurveyLotteryRedPackSendRecord();
        redPackSendRecord.setWinnerId(winnerId);
        redPackSendRecord.setIsSuccess(isSuccess);
        redPackSendRecord.setRequestData(requestData);
        redPackSendRecord.setErrorInfo(errorInfo);
        redPackSendRecordRepository.save(redPackSendRecord);
    }
}








