package cn.hanyi.survey.client.dto;

import cn.hanyi.survey.core.constant.lottery.LotteryType;
import cn.hanyi.survey.core.entity.SurveyLotteryDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2022/9/22 16:18:32
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResultDto {
    @Schema(description = "答卷id")
    private Long responseId;

    @Schema(description = "是否展示抽奖活动")
    private Boolean showLottery = false;

    @Schema(description = "抽奖次数")
    private Integer lotteryNum = 0;

    @Schema(description = "是否仅正常结束可参与抽奖")
    Boolean isComplete = true;

    @Schema(description = "活动类型")
    LotteryType lotteryType;

    @Schema(description = "抽奖活动")
    private SurveyLotteryDto surveyLottery;
}





















