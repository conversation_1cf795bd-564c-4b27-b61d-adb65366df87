package cn.hanyi.survey.client.service;

import cn.hanyi.survey.client.cache.CacheHelper;
import cn.hanyi.survey.client.cache.UpdateCacheHelper;
import cn.hanyi.survey.client.cache.bean.CacheApiKey;
import cn.hanyi.survey.client.dto.SurveySubmitRequestEncryptedDto;
import cn.hanyi.survey.client.service.submit.*;
import cn.hanyi.survey.client.utils.EncryptDecryptHelper;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;

@Slf4j
@Service
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class SubmitService {

    @Autowired(required = false)
    private List<IBeforeLimiter> beforeLimiters;
    @Autowired(required = false)
    private List<IAfterLimiter> afterLimiters;

    @Autowired(required = false)
    private List<ICompletedProcessor> completedProcessors;
    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private ISubmitStorage storage;
    @Autowired
    private CacheHelper cacheHelper;
    @Autowired
    private UpdateCacheHelper updateCacheHelper;
    @Autowired
    private LinkService linkService;
    @Autowired
    private EncryptDecryptHelper encryptDecryptHelper;

    public SubmitContext submit(boolean submit, String shortCode, SurveySubmitRequestEncryptedDto encryptedData) {
        // 从shortCode中获取surveyId, 和参数
        return submit1(submit, context -> {
            try {
                Map<String, Object> params = linkService.getSurveyParamsByCode(shortCode);
                Long surveyId = (Long) params.get("surveyId");
                if (surveyId != null) {
                    String clientId = (String) params.get("clientId");
                    context.setSurveyId(surveyId);
                    SurveySubmitRequestDto data = encryptDecryptHelper.decrypt(encryptedData);
                    if (data != null) {
                        log.info("问卷提交解密后的明文：{}", JsonHelper.toJson(data));
                        context.setEncrypted(true);
                        decryptField(context, data);
                        context.setData(data); // 先解密提交的数据
                        data.mergeParams(params); // 然后在合并数据
                        if (StringUtils.isNotEmpty(clientId)) {
                            context.setClientId(clientId);
                        }
                    } else {
                        log.error("解密失败");
                    }
                } else {
                    log.error("短链编码解析失败");
                }
            } catch (Throwable e) {
                if (submit) {
                    log.error("获取问卷失败", e);
                } else {
                    log.error("提交答卷失败", e);
                }
            }
        });
    }


    public SubmitContext submit(boolean submit, Long surveyId, SurveySubmitRequestEncryptedDto encryptedData) {
        return submit1(submit, context -> {
            context.setSurveyId(surveyId);
            SurveySubmitRequestDto data = encryptDecryptHelper.decrypt(encryptedData);
            decryptField(context, data);
            context.setData(data);
        });
    }

    private void decryptField(SubmitContext context, SurveySubmitRequestDto data) {
        CacheApiKey apiKey = loader.getApikey(context);
        if (apiKey != null) {
            data.decryptField(apiKey.getSecret());
        }
    }

    public SubmitContext submit(boolean submit, Long surveyId, SurveySubmitRequestDto data) {
        return submit1(submit, context -> {
            context.setSurveyId(surveyId);
            SurveySubmitRequestDto requestDto = StringUtils.isNotEmpty(data.getEncryptedData())
                    ? encryptDecryptHelper.decrypt(new SurveySubmitRequestEncryptedDto(data.getEncryptType(), data.getEncryptedKey(), data.getEncryptedData()))
                    : data;
            decryptField(context, requestDto);
            context.setData(requestDto);
        });
    }

    private SubmitContext submit1(boolean submit, Consumer<SubmitContext> updateContext) {
        SubmitContext context = new SubmitContext(submit);
        try {
            updateContext.accept(context);
            context.checkParams();
            String clientId = loader.requireClientId(context);
            Long surveyId = loader.requireSurveyId(context);
            if (!cacheHelper.lockSurveySubmit(surveyId, clientId)) {
                throw new BadRequestException("请求中，请勿重复提交");
            }
            submit0(context);
        } catch (Throwable e) {
            context.setException(e);
            log.error("submit1 error {}", e.getMessage());
            throw e;
        } finally {
            // 如果处理过程中抛出了异常，则跳过后置处理流程
            // 后置处理
            Optional.ofNullable(completedProcessors).ifPresent(processors -> processors.forEach(i -> {
                if (!i.skip(context)) {
                    i.onCompleted(context);
                }
            }));
        }
        return context;
    }

    private void submit0(SubmitContext context) {
        try {
            loader.requireOrgId(context);
            // 调整所有的 limiter 的 order
            // 1000 智能验证     BeforeLimiterSmartVerify
            // 1010 答卷超时     BeforeLimiterClientIdExpire
            // 1020 问卷        BeforeLimiterSurvey
            // 1030 答卷状态     BeforeLimiterResponse
            // 1040 渠道        BeforeLimiterChannel
            // 1050 手机题      BeforeLimiterMobileQuestionVerify
            Optional.ofNullable(beforeLimiters).ifPresent(limiters -> limiters.forEach(i -> i.check(context)));

            // 加载全部的数据
            loader.loadAllParams(context);

            if (context.isSubmit()) {
                // 把本次提交的数据和上次提交的数据比较，计算出新增修改删除答案
                loader.compareCells(context);

                // 调整所有的 limiter 的 order
                // 1010 配额              AfterLimiterQuota
                // 1020 问卷免打扰         AfterLimiterSurveyDisturb
                Optional.ofNullable(afterLimiters).ifPresent(limiters -> limiters.forEach(i -> i.check(context)));

                // 答卷状态
                // 使用配额
                // 保存 response cell
                loader.sync(context);
            } else {
                loader.startSurvey(context);
                loader.updateCollectorMethod(context);
            }
            updateCacheHelper.changeBySubmit(context);
        } finally {
            // release repeat lock
            cacheHelper.unlockSurveySubmit(loader.requireSurveyId(context), loader.requireClientId(context));
        }
    }


}




















