package cn.hanyi.survey.client.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SurveySubmitRequestEncryptedDto {

    @Schema(description = "key的加密方式：/aes(默认)，sm2/sm4")
    private String encryptType = "/aes";

    @Schema(description = "已经用公钥加密过的key")
    private String encryptedKey;

    @Schema(description = "已经用key加密过的答卷数据")
    private String encryptedData;
}
