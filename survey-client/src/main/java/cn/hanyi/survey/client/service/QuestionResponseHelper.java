package cn.hanyi.survey.client.service;

import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.client.service.submit.validator.IQuestionValidator;
import cn.hanyi.survey.client.service.submit.validator.QuestionValidatorHelper;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.dto.question.DynamicQuestionItemDto;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.service.expression.ExpressionService;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
@Validated
public class QuestionResponseHelper {

    @Autowired
    private QuestionValidatorHelper questionValidatorHelper;

    @Autowired
    private ExpressionService expressionService;

    private static final String SUFFIX_TAG = "-Tags";
    public static final String SUFFIX_COMMENT = "-Comment";

    public List<SurveyResponseCell> convertToCells(SubmitContext context, Long responseId, Map<String, ClientQuestion> questionNameMap) {
        List<SurveyResponseCell> responses = new ArrayList<>();
        SurveySubmitRequestDto data = context.getData();
        Map<String, Object> items = data.getData();
        Map<String, Integer> scores = data.getCellScore();
        Long surveyId = context.getSurveyId();
        List<IQuestionValidator.ErrorInfo> errorMessages = new ArrayList<>();
        items.forEach((k, v) -> {
            if (v == null) {
                return;
            }
            if (k.endsWith(SUFFIX_TAG) || k.endsWith(SUFFIX_COMMENT)) {
                return;
            }
            ClientQuestion question = questionNameMap.get(k);
            if (question == null) {
                return;
            }
            Integer score = scores.get(k);
            String comment = (String) items.get(k + SUFFIX_COMMENT);
            Object rawTags = items.get(k + SUFFIX_TAG);
            String tags = castTags(rawTags);
            IQuestionValidator.ErrorInfo errorInfo = questionValidatorHelper.validate(question, v, score, comment, rawTags);
            if (errorInfo != null) {
                errorMessages.add(errorInfo);
            }
            responses.add(convertToCell(surveyId, responseId, question, v, score, comment, tags));
        });
        questionValidatorHelper.throwIfHasError(errorMessages);
        return responses;
    }

    @SuppressWarnings("unchecked")
    private String castTags(Object tags) {
        if (!(tags instanceof List)) {
            return null;
        }
        return ((List<Object>) tags).stream().map(Object::toString).collect(Collectors.joining(","));
    }

    /**
     * 给NPS题 s_val赋值
     * @param cell
     * @param question
     * @param value
     */
    public void castNpsSValue(SurveyResponseCell cell, ClientQuestion question, Object value) {
        if (question.getType() == QuestionType.NPS && question.getItems().size() == 3) {
            Integer intValue = (Integer) value;
            switch (intValue) {
                case 0:
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                    //贬损者（0-6分）
                    cell.setStrValue(question.getItems().get(0).getValue());
                    break;
                case 7:
                case 8:
                    //中立者（7-8分）
                    cell.setStrValue(question.getItems().get(1).getValue());
                    break;
                case 9:
                case 10:
                    //推荐者（9-10分）
                    cell.setStrValue(question.getItems().get(2).getValue());
                    break;
                default:
            }
        }
    }

    private SurveyResponseCell convertToCell(Long surveyId, Long responseId, ClientQuestion question, Object value, Integer score, String comment, String tags) {
        if (question == null || value == null) {
            return null;
        }
        SurveyResponseCell cell = new SurveyResponseCell(surveyId, responseId, question.getId(), question.getType(), value);
        cell.setCellScore(score);
        cell.setCommentValue(comment);
        cell.setTags(tags);
        cell.setQName(question.getName());
        castNpsSValue(cell,question,value);
        return cell;
    }

    /**
     * 比较旧的列表和新的列表，计算出 新增 修改 删除 的答案
     */
    public void compare(
            @NotNull List<SurveyResponseCell> oldCells,
            @NotNull List<SurveyResponseCell> newCells,
            @NotNull List<SurveyResponseCell> addCells,
            @NotNull List<SurveyResponseCell> updateCells,
            @NotNull List<SurveyResponseCell> deleteCells) {
        if (oldCells.isEmpty()) {
            addCells.addAll(newCells);
            return;
        }
        if (newCells.isEmpty()) {
            deleteCells.addAll(oldCells);
            return;
        }
        Map<String, SurveyResponseCell> oldMap = oldCells.stream().filter(i -> i.getQName() != null).collect(Collectors.toMap(SurveyResponseCell::getQName, Function.identity(), (o1, o2) -> o2));
        Map<String, SurveyResponseCell> newMap = new HashMap<>();
        // 遍历一次新提交的答案，找出新增加的cell, 和修改了的cell
        newCells.forEach(newCell -> {
            newMap.put(newCell.getQName(), newCell);
            SurveyResponseCell oldCell = oldMap.get(newCell.getQName());
            if (oldCell == null) {
                addCells.add(newCell);                          // 新增的答案
            } else {
                newCell.setId(oldCell.getId());
                if (!oldCell.compareAndCopyFrom(newCell)) {    // 比较一下 ，如果答案内容不同，则把最新的答案复制过来
                    updateCells.add(oldCell);                  // 修改的答案
                }
            }
        });
        // 遍历一次旧的答案，找出删除的cell
        oldCells.forEach(oldCell -> {
            // 如果问题被删除了，历史提交的答题也删掉
            if (oldCell.getQName() == null || !newMap.containsKey(oldCell.getQName())) {
                deleteCells.add(oldCell);
            }
        });
    }

    /**
     * 把旧的数据复制到新的，获取问卷时，需要获取历史填答数据
     */
    public void copyOldToNew(@NotNull List<SurveyResponseCell> oldCells, @NotNull List<SurveyResponseCell> newCells) {
        newCells.clear();
        if (oldCells.isEmpty()) {
            return;
        }
        newCells.addAll(oldCells.stream().filter(i -> StringUtils.isNotEmpty(i.getQName())).collect(Collectors.toList()));
    }

    public Map<String, Object> getSubmitData(List<SurveyResponseCell> cells) {
        Map<String, Object> cellData = new HashMap<>();
        cells.forEach(i -> {
            buildSubmitCellData(cellData, i);
        });
        return cellData;
    }

    private void buildSubmitCellData(Map<String, Object> cellData, SurveyResponseCell cell) {
        String name = cell.getQName();
        if (StringUtils.isEmpty(name)) {
            return;// 问题不存在，被删除了，
        }
        Object value = cell.getValue();
        Object otherValue = cell.getCommentValue();
        String tags = cell.getTags();
        cellData.put(name, value);
        if (otherValue != null) {
            cellData.put(String.format("%s-Comment", name), otherValue);
        }
        if (tags != null) {
            cellData.put(String.format("%s-Tags", name), Arrays.asList(tags.split(",")));
        }
    }

    public Map<String, Object> getExpressionData(List<SurveyResponseCell> newCells) {
        Map<String, Object> expressions = new HashMap<>();
        newCells.forEach(i -> {
            expressions.putAll(convertToExpression(i));
        });
        return expressions;
    }

    private Map<String, Object> convertToExpression(SurveyResponseCell cell) {
        Map<String, Object> content = new HashMap<>();

        List<Map> items = new ArrayList<>();
        expressionService.findAllByQuestion(cell.getQuestionId()).forEach(
                i -> {
                    DynamicQuestionItemDto d = new DynamicQuestionItemDto();
                    d.setText(i.getText());
                    d.setValue(i.getValue());
                    items.add(JsonHelper.toMap(d));
                }
        );
        content.put("items", items);
        content.put("value", cell.getValue());
        Optional.ofNullable(cell.getCommentValue()).ifPresent(i -> content.put("comment", i));
        Optional.ofNullable(castTags(cell.getTags())).ifPresent(i -> content.put("tags", i));
        return Map.of(cell.getQName(), content);
    }

    private List<String> castTags(String tags) {
        return StringUtils.isEmpty(tags) ? null : Arrays.stream(tags.split(",")).collect(Collectors.toList());
    }
}
