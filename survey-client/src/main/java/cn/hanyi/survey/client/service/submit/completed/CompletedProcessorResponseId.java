package cn.hanyi.survey.client.service.submit.completed;

import cn.hanyi.ctm.dto.customer.CustomerClientIdParamDto;
import cn.hanyi.survey.client.service.submit.ICompletedProcessor;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

@Order(100)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class CompletedProcessorResponseId implements ICompletedProcessor {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public Boolean skip(SubmitContext context) {
        return context.getException() != null && context.getResponse() == null;
    }

    @Override
    public void onCompleted(SubmitContext context) {
        context.getResponseResultDto().setResponseId(loader.requireResponse(context).getId());
        if (context.isSubmit() && context.getData().getIsCompleted()) {
            String expireKey = CustomerClientIdParamDto.clientIdParamKey(loader.requireClientId(context));
            stringRedisTemplate.delete(expireKey);
        }
    }

}
