package cn.hanyi.survey.client.service;

import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.dto.ClientSurveyDisturbDto;
import cn.hanyi.survey.client.dto.SurveyDisturbQueryDto;
import cn.hanyi.survey.client.dto.SurveyDisturbRuleSimpleDto;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.core.constant.channel.ChannelStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.disturb.DisturbMethod;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.disturb.LastSurveyDisturbRecord;
import cn.hanyi.survey.core.dto.disturb.LastSurveyDisturbRecordId;
import cn.hanyi.survey.core.dto.disturb.LastSurveyDisturbRecordTime;
import cn.hanyi.survey.core.dto.ext.SurveyEmbedExtDto;
import cn.hanyi.survey.core.dto.survey.SurveyDisturbFilterDto;
import cn.hanyi.survey.core.entity.*;
import cn.hanyi.survey.core.repository.SurveyDisturbRecordRepository;
import cn.hanyi.survey.core.repository.SurveyDisturbRuleRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import com.sun.istack.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceCustomQueryDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SurveyDisturbRuleService extends BaseService<SurveyDisturbRule, SurveyDisturbRuleDto, SurveyDisturbRuleRepository> {

    @Autowired
    private UserService userService;

    @Autowired
    private SurveyDisturbRecordRepository surveyDisturbRecordRepository;

    @Autowired
    private SurveyDisturbRuleRepository repository;

    @Autowired
    private SurveyResponseRepository responseRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public <S extends ResourceCustomQueryDto> Page<SurveyDisturbRuleDto> findAll(S query) {
        SurveyDisturbQueryDto dto = (SurveyDisturbQueryDto) query;
        String sql = "select * from survey_disturb_rule sdr ";

        String where = " where sdr.org_id = " + TenantContext.getCurrentTenant();
        if (StringUtils.isNotEmpty(dto.getName())) {
            where = where + " and sdr.`name` like '%" + dto.getName() + "%'";
        }
        if (StringUtils.isNotEmpty(dto.getSids())) {
            where = where + " and sdr.sids like '%" + dto.getSids() + "%'";
        }
        if (StringUtils.isNotEmpty(dto.getTitles())) {
            where = where + " and sdr.titles like '%" + dto.getTitles() + "%'";
        }
        if (dto.getStatus() != null) {
            if (dto.getStatus()) where = where + " and sdr.status = 1 ";
            else where = where + " and sdr.status = 0 ";
        }
        int pageStart = (dto.getPage() - 1 ) * dto.getLimit();
        sql = sql + where + " order by sdr.modify_time desc limit " + pageStart + "," + dto.getLimit();

        List<SurveyDisturbRuleSimpleDto> simpleRuleList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SurveyDisturbRuleSimpleDto.class));
        List<SurveyDisturbRule> ruleList = repository.findByIdInOrderByModifyTimeDesc(simpleRuleList.stream().map(SurveyDisturbRuleSimpleDto::getId).collect(Collectors.toList()));

        if (ruleList.isEmpty()) {
            // 如果 ruleList 为空，创建一个空的 PageImpl 对象
            return new PageImpl<>(Collections.emptyList(), PageRequest.of(0, 1), 0);
        } else {
            // 如果 ruleList 不为空，正常创建 PageImpl 对象
            List<SurveyDisturbRuleDto> ruleDtoList = mapToDto(ruleList);
            Pageable pageable = PageRequest.of(dto.getPage() - 1, ruleDtoList.size());
            Long total = jdbcTemplate.queryForObject("select count(*) from survey_disturb_rule sdr " + where, Long.class);
            return new PageImpl<>(ruleDtoList, pageable, total);
        }
    }

//    @Override
//    public <S extends ResourceCustomQueryDto> Page<SurveyDisturbRuleDto> findAll(S query) {
//        SurveyDisturbQueryDto dto = (SurveyDisturbQueryDto) query;
//        ResourceEntityQueryDto<SurveyDisturbRuleDto> params = dto.transform();
//
//        params.getQueryCriteriaList().add(new ResourceQueryCriteria("orgId", TenantContext.getCurrentTenant(), QueryOperator.EQUAL));
//
//        if (StringUtils.isNotEmpty(dto.getQ())) {
//            params.getQueryCriteriaList().add(new ResourceQueryCriteria("sids", dto.getQ(), QueryOperator.CONTAIN));
//        }
//
//        return super.findAll(params);
//    }


    @Override
    public List<SurveyDisturbRuleDto> mapToDto(List<SurveyDisturbRule> entity) {
        List<SurveyDisturbRuleDto> dto = super.mapToDto(entity);
        Set<Long> updateUserIds = new HashSet<>();
        dto.forEach(disturb -> {
            Optional.ofNullable(disturb.getEditorId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(disturb.getUserId()).ifPresent(updateUserIds::add);
        });
        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
        dto.forEach(disturb -> {
            Optional.ofNullable(disturb.getEditorId()).ifPresent(i -> disturb.setEditorUser(userMap.get(i)));
            Optional.ofNullable(disturb.getUserId()).ifPresent(i -> disturb.setCreator(userMap.get(i)));
        });
        return dto;
    }

    @Override
    public <S extends BaseEntityDTO<SurveyDisturbRule>> SurveyDisturbRuleDto create(S data) {
        ((SurveyDisturbRuleDto) data).setUserId(TenantContext.getCurrentUserId());
        return super.create(data);
    }

    @Override
    public void afterMapToDto(List<SurveyDisturbRule> entity, List<SurveyDisturbRuleDto> dto) {
        super.afterMapToDto(entity, dto);
        Set<Long> updateUserIds = new HashSet<>();
        dto.forEach(disturb -> {
            Optional.ofNullable(disturb.getEditorId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(disturb.getUserId()).ifPresent(updateUserIds::add);
        });
        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
        dto.forEach(disturb -> {
            Optional.ofNullable(disturb.getEditorId()).ifPresent(i -> disturb.setEditorUser(userMap.get(i)));
            Optional.ofNullable(disturb.getUserId()).ifPresent(i -> disturb.setCreator(userMap.get(i)));
        });
    }

    /**
     * 新增行为记录 如果已存在 叠加停留时间和返回次数
     *
     * @param surveyId
     * @param disturbDto
     */
    public void addSurveyDisturbRecord(Long surveyId, ClientSurveyDisturbDto disturbDto) {
        if (surveyId == null || disturbDto == null || disturbDto.getResponseId() == null || disturbDto.getDisturbMethod() == null) {
            log.info("survey disturb, surveyId:{}, responseId:{}, disturbDto:{}", surveyId, disturbDto.getResponseId(), JsonHelper.toJson(disturbDto));
            return;
        }
        Optional<SurveyResponse> responseOptional = responseRepository.findById(disturbDto.getResponseId());
        if (responseOptional.isPresent()) {
            if(StringUtils.isEmpty(responseOptional.get().getExternalUserId())
                    || !Arrays.asList(SurveyCollectorMethod.EMBEDDED,SurveyCollectorMethod.MP, SurveyCollectorMethod.APP).contains(responseOptional.get().getCollectorMethod())) {
                return;
            }
            surveyDisturbRecordRepository.save(new SurveyDisturbRecord(responseOptional.get().getOrgId(), surveyId, responseOptional.get().getExternalUserId(),
                    disturbDto.getDisturbMethod(), responseOptional.get().getChannelId(), responseOptional.get().getClientId()));
        }
    }

    /**
     * *免打扰时 增加记录
     * @param surveyId
     * @param orgId
     * @param channelId
     * @param externalUserId
     * @param clientId
     */
    public void addSurveyDisturbRecord(Long surveyId, Long orgId, Long channelId, String externalUserId, String clientId) {
        if (surveyId == null || channelId == null || StringUtils.isEmpty(externalUserId)) return;
        SurveyDisturbRecord record = new SurveyDisturbRecord();
        record.setSid(surveyId);
        record.setDisturbMethod(DisturbMethod.DISTURB);
        record.setExternalUserId(externalUserId);
        record.setOrgId(orgId);
        record.setChannelId(channelId);
        record.setClientId(clientId);
        surveyDisturbRecordRepository.save(record);
    }

    /**
     * *问卷免打扰判断
     * @param surveyId
     * @param orgId
     * @param doNotDisturb
     * @param externalUserId
     * @param surveyChannel
     * @return
     */
    public Boolean isDisturbSurvey(Long surveyId, Long orgId, Boolean doNotDisturb, String externalUserId, SurveyChannel surveyChannel) {

        if(doNotDisturb) return true;
        if (StringUtils.isEmpty(externalUserId) || surveyChannel == null) return false;

        //校验用户是否在答题端勾选了不再显示
        if (checkUserDisturbSetting(surveyId, orgId, surveyChannel, externalUserId)) return true;

        List<SurveyDisturbRule> disturbRuleList = matchRules(surveyId,orgId);
        if(disturbRuleList == null || disturbRuleList.isEmpty()) return false;

        ArrayList<Boolean> disturbList = new ArrayList<>();
        for(SurveyDisturbRule disturbRule : disturbRuleList) {
            if (disturbRule.getSids() == null || disturbRule.getSids().isEmpty() || !disturbRule.getSids().contains(surveyId)
                    || disturbRule.getDisturbFilter() == null || disturbRule.getDisturbFilter().isEmpty() || disturbRule.getChannels() == null || disturbRule.getChannels().isEmpty()) continue;

            //免打扰渠道没配置 规则不生效
            if (!disturbRule.getChannels().contains(convertChannelType(surveyChannel.getType()))) continue;

            switch(disturbRule.getDisturbType()){
                case DATE:
                    doNotDisturb = checkDisturbTypeByDateNew(disturbRule, externalUserId, disturbRule.getSids());
                    log.info("问卷:{} 规则:{} 免打扰:{}", surveyId, disturbRule.getName(), doNotDisturb);
                    break;
                case TIMES:
                    doNotDisturb = checkDisturbTypeByTimesNew(disturbRule, externalUserId, disturbRule.getSids());
                    log.info("问卷:{} 规则:{} 免打扰:{}", surveyId, disturbRule.getName(), doNotDisturb);
                    break;
                default:
                    continue;
            }
            if (doNotDisturb) {
                disturbList.add(true);
                break;
            }
        }
        log.info("问卷:{} 免打扰:{}", surveyId, !disturbList.isEmpty());
        return !disturbList.isEmpty();
    }

    public Boolean checkResponseStatus(ClientSurvey survey, SurveyChannel channel) {

        if(survey.getStatus() != SurveyStatus.COLLECTING || channel.getStatus() != ChannelStatus.RECOVERY)
            return true;
        //调查期限内
        if(survey.getEnableDateLimit()) {
            try {
                IBeforeLimiter.checkDateRange(survey.getStartTime(), survey.getEndTime());
            } catch (Exception e) {
                return true;
            }
        }
        return false;
    }

    /**
     * *校验用户是否在答题端勾选了不再显示
     * @param surveyId
     * @param orgId
     * @param channel
     * @param externalUserId
     * @return
     */
    public Boolean checkUserDisturbSetting(Long surveyId, Long orgId, SurveyChannel channel, String externalUserId) {
        if (channel == null || channel.getType() != ChannelType.INJECT_WEB || StringUtils.isEmpty(channel.getConfigure()) || StringUtils.isEmpty(externalUserId)) return false;
        SurveyEmbedExtDto dto = JsonHelper.toObject(channel.getConfigure(),SurveyEmbedExtDto.class);
        if (dto == null || !dto.getDoNotDisturbSwitch()) return false;
        Optional<SurveyDisturbRecord> optional = surveyDisturbRecordRepository.findFirstByOrgIdAndSidAndExternalUserIdAndDisturbMethodOrderByIdDesc(orgId, surveyId, externalUserId, DisturbMethod.CHOOSE);
        //判断当前时间 是否在免打扰期限内
        if (optional.isPresent()) {
            SurveyDisturbRecord record = optional.get();
            Duration delay = Duration.between(DateHelper.toLocalDateTime(record.getCreateTime()), LocalDateTime.now());
            if (delay.getSeconds() > 0 && delay.getSeconds() < dto.getDoNotDisturbTime() * 24 * 60 * 60) {
                return true;
            }
        }
        return false;
    }

    /**
     * *匹配符合免打扰的问卷id
     * @param list
     * @param surveyId
     * @return
     */
    public List<Long> matchDisturbRuleSurveyId(List<SurveyDisturbRule> list, Long surveyId) {
        List<Long> matchSurveyIds = new ArrayList<>();
        if(list == null || list.size() == 0 || surveyId == null) return matchSurveyIds;
        list.stream().forEach(rule -> {
            if(rule.getSids().contains(surveyId)) {
                matchSurveyIds.addAll(rule.getSids());
            }
        });
        return matchSurveyIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 分离最后一次弹出/提交的免打扰记录
     * @param disturbRecordList
     */
    private Map<String, SurveyDisturbRecord> initSurveyDisturbRecord(List<SurveyDisturbRecord> disturbRecordList) {
        Map<String, SurveyDisturbRecord> map = new HashMap<>();
        if (disturbRecordList == null || disturbRecordList.isEmpty()) return map;
        disturbRecordList.forEach(disturbRecord -> {
            Long sid = disturbRecord.getSid();
            switch (disturbRecord.getDisturbMethod()){
                case VISIT:
                    String visitKey = DisturbMethod.VISIT.name();
                    if (!map.containsKey(visitKey)) {
                        map.put(visitKey, disturbRecord);
                    }
                    if (!map.containsKey(visitKey + sid)) {
                        map.put(visitKey + sid, disturbRecord);
                    }
                    break;
                case OPEN:
                    String openKey = DisturbMethod.OPEN.name();
                    if (!map.containsKey(openKey)) {
                        map.put(openKey, disturbRecord);
                    }
                    if (!map.containsKey(openKey + sid)) {
                        map.put(openKey + sid, disturbRecord);
                    }
                    break;
                case SUBMIT:
                    String submitKey = DisturbMethod.SUBMIT.name();
                    if (!map.containsKey(submitKey)) {
                        map.put(submitKey, disturbRecord);
                    }
                    if (!map.containsKey(submitKey + sid)) {
                        map.put(submitKey + sid, disturbRecord);
                    }
                    break;
                case CLOSE:
                    String closeKey = DisturbMethod.CLOSE.name();
                    if (!map.containsKey(closeKey)) {
                        map.put(closeKey, disturbRecord);
                    }
                    if (!map.containsKey(closeKey + sid)) {
                        map.put(closeKey + sid, disturbRecord);
                    }
                    break;
                case DISTURB:
                    String disturbKey = DisturbMethod.DISTURB.name();
                    if (!map.containsKey(disturbKey)) {
                        map.put(disturbKey, disturbRecord);
                    }
                    if (!map.containsKey(disturbKey + sid)) {
                        map.put(disturbKey + sid, disturbRecord);
                    }
                    break;
                default:
            }

        });
        return map;
    }

    /**
     * 按时间免打扰机制 false弹出问卷 true不弹出免打扰
     * @param disturbRule
     * @return
     */
    private Boolean checkDisturbTypeByDate(SurveyDisturbRule disturbRule, Map<String, SurveyDisturbRecord> disturbRecordMap, Long surveyId, Boolean isSingleUnion) {
        if(disturbRule == null || disturbRule.getDisturbFilter().isEmpty()) return false;

        for (SurveyDisturbFilterDto filterDto : disturbRule.getDisturbFilter()) {
            String key = filterDto.getDisturbMethod().name();
            if (isSingleUnion) {
                key = key + surveyId;
            }
            SurveyDisturbRecord record = disturbRecordMap.get(key);
            log.info("问卷:{} 规则:{} key:{} 免打扰记录:{}", surveyId, disturbRule.getName(), key, record == null ? null : record.getId());
            if (record != null) {
                Duration delay = Duration.between(DateHelper.toLocalDateTime(record.getCreateTime()), LocalDateTime.now());
                if (delay.getSeconds() < filterDto.getDisturbDays() * 24 * 60 * 60) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 按次数免打扰机制 false弹出问卷 true不弹出免打扰（新）
     * @param disturbRule
     * @return
     */
    private Boolean checkDisturbTypeByTimes(SurveyDisturbRule disturbRule, Map<String, SurveyDisturbRecord> disturbRecordMap, Long surveyId, Boolean isSingleUnion) {
        if(disturbRule == null || disturbRule.getDisturbFilter().isEmpty()) return false;

        for (SurveyDisturbFilterDto filterDto : disturbRule.getDisturbFilter()) {
            String key = filterDto.getDisturbMethod().name();
            if (isSingleUnion) {
                key = key + surveyId;
            }
            SurveyDisturbRecord record = disturbRecordMap.get(key);
            log.info("问卷:{} 规则:{} key:{} 免打扰记录:{}", surveyId, disturbRule.getName(), key, record == null ? null : record.getId());
            if (record != null) {
                long times = surveyDisturbRecordRepository.countByIdGreaterThanAndOrgIdAndSidAndExternalUserIdAndDisturbMethod(record.getId(), record.getOrgId(), record.getSid(), record.getExternalUserId(), DisturbMethod.DISTURB);
                if (times < filterDto.getDisturbDays()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 按次数免打扰机制 false弹出问卷 true不弹出免打扰（新）
     * @param disturbRule
     * @return
     */
    private Boolean checkDisturbTypeByTimesNew(SurveyDisturbRule disturbRule, String externalUserId, List<Long> sids) {
        if(disturbRule == null || disturbRule.getDisturbFilter().isEmpty()) return false;

        for (SurveyDisturbFilterDto filterDto : disturbRule.getDisturbFilter()) {
            //如果配置的条件为空 继续下一个条件判断
            if (filterDto.getDisturbDays() == null || filterDto.getDisturbMethod() == null) continue;
            LastSurveyDisturbRecord lastSurveyDisturbRecord = surveyDisturbRecordRepository.findFirstByOrgIdAndSidIsInAndExternalUserIdAndDisturbMethodOrderByCreateTimeDesc(disturbRule.getOrgId(), sids, externalUserId,  filterDto.getDisturbMethod());
            if (lastSurveyDisturbRecord != null) {
                long times = surveyDisturbRecordRepository.countByIdGreaterThanAndOrgIdAndSidInAndExternalUserIdAndDisturbMethod(lastSurveyDisturbRecord.getId(), disturbRule.getOrgId(), sids, externalUserId, DisturbMethod.DISTURB);
                if (times < filterDto.getDisturbDays()) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 按时间免打扰机制 false弹出问卷 true不弹出免打扰
     * @param disturbRule
     * @return
     */
    private Boolean checkDisturbTypeByDateNew(SurveyDisturbRule disturbRule, String externalUserId, List<Long> sids) {
        if(disturbRule == null || disturbRule.getDisturbFilter().isEmpty()) return false;

        for (SurveyDisturbFilterDto filterDto : disturbRule.getDisturbFilter()) {
            //如果配置的条件为空 继续下一个条件判断
            if (filterDto.getDisturbDays() == null || filterDto.getDisturbMethod() == null) continue;
            //获取最后一条记录的时间
            LastSurveyDisturbRecord lastSurveyDisturbRecord = surveyDisturbRecordRepository.findFirstByOrgIdAndSidIsInAndExternalUserIdAndDisturbMethodOrderByCreateTimeDesc(disturbRule.getOrgId(), sids, externalUserId,  filterDto.getDisturbMethod());
            if (lastSurveyDisturbRecord != null) {
                Duration delay = Duration.between(DateHelper.toLocalDateTime(lastSurveyDisturbRecord.getCreateTime()), LocalDateTime.now());
                if (delay.getSeconds() < filterDto.getDisturbDays() * 24 * 60 * 60) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * * 匹配规则
     * @param surveyId
     * @param orgId
     * @return
     */
    private List<SurveyDisturbRule> matchRules(Long surveyId, Long orgId) {
        if(surveyId == null || orgId == null) return null;
        String sql = "select * from survey_disturb_rule where org_id = " + orgId + " and status = 1 and sids like '%" +surveyId + "%';";
        List<SurveyDisturbRuleSimpleDto> ruleList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SurveyDisturbRuleSimpleDto.class));
        return repository.findAllById(ruleList.stream().map(SurveyDisturbRuleSimpleDto::getId).collect(Collectors.toList()));
    }

    /**
     * *渠道类型 转换成收集方式
     * @param channel
     * @return
     */
    public String convertChannelType(ChannelType channel) {
        String collectorMethod = "LINK";
        switch(channel) {
            case COMMON:
                collectorMethod = SurveyCollectorMethod.LINK.toString();break;
            case SHORT_LINK:
                collectorMethod = SurveyCollectorMethod.SHORT_LINK.toString();break;
            case PHONE_MSG:
                collectorMethod = SurveyCollectorMethod.PHONE_MSG.toString();break;
            case WECHAT_SERVICE:
                collectorMethod = SurveyCollectorMethod.WECHAT_SERVICE.toString();break;
            case INJECT_WEB:
                collectorMethod = "embed";break;
            case SURVEY_PLUS:
                collectorMethod = SurveyCollectorMethod.SURVEY_PLUS.toString();break;
            case SCENE_INTERACTION:
                collectorMethod = SurveyCollectorMethod.SCENE_INTERACTION.toString();break;
            case MP:
                collectorMethod = SurveyCollectorMethod.MP.toString();break;
            case APP:
                collectorMethod = SurveyCollectorMethod.APP.toString();break;
            case EMAIL:
                collectorMethod = SurveyCollectorMethod.EMAIL.toString();break;
            default:
        }
        return collectorMethod.toLowerCase(Locale.ROOT);
    }

    /**
     * *查询免打扰打开、提交、关闭最后的时间
     * @param orgId
     * @param externalUserId
     * @param sids
     * @return
     */
    private LastSurveyDisturbRecordTime findLastTime(@NotNull Long orgId, @NotNull String externalUserId, @NotNull List<Long> sids){
        String sql = String.format("select " +
                "MAX(CASE WHEN disturb_method = 1 THEN create_time END) AS lastOpenTime, " +
                "MAX(CASE WHEN disturb_method = 2 THEN create_time END) AS lastSubmitTime, " +
                "MAX(CASE WHEN disturb_method = 3 THEN create_time END) AS lastCloseTime " +
                "from survey_disturb_record where org_id=%d and euid='%s' and s_id in (%s);", orgId, externalUserId, StringUtils.join(sids, ","));
        return jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(LastSurveyDisturbRecordTime.class));
    }

    /**
     * *最新的record id
     * @param orgId
     * @param externalUserId
     * @param sids
     * @return
     */
    private LastSurveyDisturbRecordId findLastId(@NotNull Long orgId, @NotNull String externalUserId, @NotNull List<Long> sids){
        String sql = String.format("select " +
                "MAX(CASE WHEN disturb_method = 1 THEN id END) AS lastOpenId, " +
                "MAX(CASE WHEN disturb_method = 2 THEN id END) AS lastSubmitId, " +
                "MAX(CASE WHEN disturb_method = 3 THEN id END) AS lastCloseId " +
                "from survey_disturb_record where org_id=%d and euid='%s' and s_id in (%s);", orgId, externalUserId, StringUtils.join(sids, ","));
        return jdbcTemplate.queryForObject(sql, new BeanPropertyRowMapper<>(LastSurveyDisturbRecordId.class));
    }
}






















