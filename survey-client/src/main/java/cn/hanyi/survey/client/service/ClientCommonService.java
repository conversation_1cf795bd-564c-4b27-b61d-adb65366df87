package cn.hanyi.survey.client.service;

import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.service.SendManageService;
import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.dto.SendMangeResDto;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.dto.SubmitAdditionDataDto;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveySendRecordRepository;
import cn.hanyi.survey.core.service.SurveyBaseEntityService;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.MapperService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Random;

/**
 * 临时的解决方法
 * 更新渠道填答状态
 */
@Service
public class ClientCommonService {

    @Autowired
    private SurveyBaseEntityService baseEntityService;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private SurveySendRecordRepository surveySendRecordRepository;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyChannelRepository channelRepository;
    @Autowired(required = false)
    private SendManageService sendManageService;
    @Autowired(required = false)
    private WechatConfigureService wechatConfigureService;


    /**
     * 更新问卷推送记录的 填答状态
     */
    @Async
    public void updateRecodeReplyStatus(SurveySendRecord record, Long responseId, Integer durationInSeconds) {
        surveySendRecordRepository.save(record);
    }

    public ClientSurvey requireSurvey(Long surveyId, Long channelId) {
        Survey entity = baseEntityService.get(Survey.class, surveyId);
        if (entity != null) {
            ClientSurvey dto = mapperService.map(entity, ClientSurvey.class);
            if (channelId != null) {
                SurveyChannel channel = baseEntityService.get(SurveyChannel.class, channelId);
                Optional.ofNullable(channel).ifPresent(c -> dto.setChannel(mapperService.map(c, ClientChannel.class)));
            }
            return dto;
        }
        throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
    }
    public SendMangeResDto sendManage(String sendToken) {
        List<SendManage> sendManageList = sendManageService.getRepository().findAll((r, q, c) -> c.equal(r.get("sendToken"), sendToken));
        if (sendManageList.isEmpty()) {
            throw new BadRequestException("sendToken不存在");
        }
        SendManage sendManage = sendManageList.get(0);

        if (!sendManage.getEnable()) {
            throw new BadRequestException("已经停用当前发送");
        }

        List<Long> sids = sendManage.getSendSids();
        List<Long> cids = sendManage.getSendCids();

        surveyRepository.findByIdIn(sids).forEach(survey -> {
            int idx = sids.indexOf(survey.getId());
            if (!SurveyStatus.COLLECTING.equals(survey.getStatus())) {
                sids.remove(idx);
                cids.remove(idx);
            } else {
                Long channelId = cids.get(idx);
                if (!channelRepository.existsById(channelId)) {
                    sids.remove(idx);
                    cids.remove(idx);
                }
            }
        });

        if (sids.isEmpty()) {
            throw new BadRequestException("无可用问卷");
        }

        int index = new Random().nextInt(sids.size());
        Long sid = sids.get(index);
        Long cid = cids.get(index);

        return new SendMangeResDto(sid, cid);
    }

    /**
     * 如果问卷id不存在 通过sendToken 获取到问卷id和channelId
     * @param reqDto
     */
    public void getSendManageSurveyId(SurveySubmitRequestDto reqDto) {
        if (reqDto == null || (reqDto.getSurveyId() == null && StringUtils.isEmpty(reqDto.getSendToken())))
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_ERROR);
        if (StringUtils.isNotEmpty(reqDto.getSendToken())) {
            SendMangeResDto resDto = sendManage(reqDto.getSendToken());
            reqDto.setSurveyId(resDto.getSurveyId());
            reqDto.setChannelId(resDto.getChannelId());
            reqDto.setAdditionData(new SubmitAdditionDataDto(reqDto.getSendToken()));
        }
    }

}
