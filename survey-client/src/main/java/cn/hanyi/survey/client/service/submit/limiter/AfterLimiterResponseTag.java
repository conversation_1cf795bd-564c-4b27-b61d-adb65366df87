package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.service.QuestionResponseHelper;
import cn.hanyi.survey.client.service.submit.IAfterLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.core.service.expression.ExpressionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Order(1011)
@Component
@Slf4j
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class AfterLimiterResponseTag implements IAfterLimiter {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private ExpressionService expressionService;

    @Autowired
    private QuestionResponseHelper questionResponseHelper;

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    private SurveyResponseRepository responseRepository;

    public void addTags(SurveyResponse response){

    }

    @Override
    public void check(SubmitContext context) {
        try {
            surveyRepository.findById(context.getSurveyId()).ifPresent(survey -> {
                if (!survey.getEnableTag()) {
                    return;
                }
                Map<String, Object> content = questionResponseHelper.getExpressionData(loader.requireNewCells(context));
                if(content.isEmpty()){
                    return;
                }

                List<String> tags = new ArrayList<>();
                survey.getTags().forEach(tag -> {
                    if(expressionService.triggerExpression(tag.getExpression(), content)){
                        tags.add(tag.getName());
                    }
                });

                if(!tags.isEmpty()){
                    SurveyResponse response = loader.requireResponse(context);
                    response.setTags(tags);
                    responseRepository.save(response);
                }
            });
        }catch (Exception e){
            log.error("addTags error",e);
        }
    }

}
