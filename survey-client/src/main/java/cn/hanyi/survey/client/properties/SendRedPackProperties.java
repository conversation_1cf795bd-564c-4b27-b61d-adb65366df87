package cn.hanyi.survey.client.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration
@ConfigurationProperties(value = "survey.lottery.red-pack")
public class SendRedPackProperties {

    private String sendName = "体验家";// 商户名称
    private int totalNum = 1;// 红包发放总人数
    private String wishing = "答卷奖励";// 红包祝福语
    private String actName = "答问卷抽红包 ";// 活动名称
    private String remark;// 备注
    private String sceneId;//场景id 非必填
    private String riskInfo;//活动信息 非必填


    //未领取退还红包时间 单位：分钟
    private int refundRedPack = 25 * 60;

    // 审核时间
    private int refundRedPackDelay = 48 * 60;

    //单个红包最小金额默认1元、最大金额20元，单位分
    private int minMoney = 100;
    private int maxMoney = 2000;

}

























