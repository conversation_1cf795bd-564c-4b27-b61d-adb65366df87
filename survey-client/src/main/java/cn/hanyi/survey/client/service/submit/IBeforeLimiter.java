package cn.hanyi.survey.client.service.submit;

import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import org.befun.core.utils.DateHelper;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Optional;

public interface IBeforeLimiter {
    void check(SubmitContext context);

    static void checkDateRange(Date start, Date end) {
        checkDateRange(start, end, "该问卷的答题时间还未开始，请您晚点再来", "您来晚啦，您访问的问卷已停止收集");
    }

    static void checkDateRange(Date start, Date end, String beforeMsg, String afterMsg) {
        LocalDateTime now = null;
        LocalDateTime startDate;
        if ((startDate = DateHelper.toLocalDateTime(start)) != null && (now = LocalDateTime.now()).isBefore(startDate)) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_NOT_BEGIN);
        }
        LocalDateTime endDate;
        if ((endDate = DateHelper.toLocalDateTime(end)) != null && (Optional.ofNullable(now).orElse(LocalDateTime.now())).isAfter(endDate)) {
            throw new SurveyErrorException(SurveyErrorCode.SURVEY_IS_END);
        }
    }

    default boolean unboxBool(Boolean bool) {
        return bool != null && bool;
    }

    default int unboxInt(Integer i) {
        return i == null ? 0 : i;
    }

    default long unboxLong(Long l) {
        return l == null ? 0 : l;
    }
}
