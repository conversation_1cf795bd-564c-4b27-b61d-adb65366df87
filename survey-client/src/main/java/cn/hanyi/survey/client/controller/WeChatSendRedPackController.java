package cn.hanyi.survey.client.controller;

import cn.hanyi.survey.client.service.TrackingService;
import cn.hanyi.survey.client.service.luckyDraw.WechatLuckyDrawService;
import cn.hanyi.survey.core.dto.lottery.SendRedPackParam;
import cn.hanyi.survey.core.dto.lottery.SendRedPackResult;
import com.github.binarywang.wxpay.bean.result.WxPayRedpackQueryResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @date 2022/11/11 16:29:32
 */
@Tag(name = "发红包")
@RestController
public class WeChatSendRedPackController {

    @Autowired
    private WechatLuckyDrawService wechatLuckyDrawService;
    @Autowired
    private TrackingService trackingService;

    @PostMapping("/surveys/{sid}/lottery/{lotteryId}/sendRedPack")
    @Operation(summary = "发送红包")
    public ResourceResponseDto<SendRedPackResult> sendRedPack(@RequestBody SendRedPackParam data) {
        return new ResourceResponseDto<>(wechatLuckyDrawService.SendRedPackTask(data));
    }

    @GetMapping("/surveys/lottery/gethbinfo")
    @Operation(summary = "获取红包信息")
    public ResourceResponseDto<WxPayRedpackQueryResult> gethbinfo(HttpServletRequest request, String mchBillNo) {
        return new ResourceResponseDto<>(wechatLuckyDrawService.gethbinfo(request, mchBillNo));
    }
}
