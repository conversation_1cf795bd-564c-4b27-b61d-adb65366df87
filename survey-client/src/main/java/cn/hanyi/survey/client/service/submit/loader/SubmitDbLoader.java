package cn.hanyi.survey.client.service.submit.loader;

import cn.hanyi.survey.client.cache.bean.CacheApiKey;
import cn.hanyi.survey.client.cache.bean.CacheDepartment;
import cn.hanyi.survey.client.cache.hash.ApiKeyCacheHelper;
import cn.hanyi.survey.client.cache.hash.DepartmentCacheHelper;
import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.dto.ClientQuestion;
import cn.hanyi.survey.client.dto.ClientQuota;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.QuestionResponseHelper;
import cn.hanyi.survey.client.service.TrackingService;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.ResponseStatus;
import cn.hanyi.survey.core.constant.channel.ChannelSendStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.dto.SubmitAdditionDataDto;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import cn.hanyi.survey.core.dto.SurveyTrackingDataDto;
import cn.hanyi.survey.core.dto.task.AdminXResponseDto;
import cn.hanyi.survey.core.entity.SurveyResponse;
import cn.hanyi.survey.core.entity.SurveyResponseCell;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.repository.SurveyRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import cn.hanyi.survey.service.quota.QuotaLimit;
import cn.hanyi.survey.workertrigger.ISurveyEventTrigger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.service.OrganizationService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RestUtils;
import org.befun.extension.sms.ISmsAccountService;
import org.befun.task.mq.ITaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class SubmitDbLoader implements ISubmitLoader {

    private static final String ADMINX_RESPONSE_VIEW_KEY = "adminx-response-view";

    private static final String ADMINX_RESPONSE_SUBMIT_KEY = "adminx-response-submit";

    @Autowired
    private ITaskService taskService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SurveyRepository surveyRepository;
    @Autowired
    private SurveyResponseRepository surveyResponseRepository;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private TrackingService trackingService;
    @Autowired
    private QuotaLimit quotaLimit;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    protected ISubmitStorage storage;
    @Autowired
    protected QuestionResponseHelper questionResponseHelper;
    @Autowired
    protected DepartmentCacheHelper departmentCacheHelper;
    @Autowired
    protected ApiKeyCacheHelper apiKeyCacheHelper;
    @Autowired
    protected ISurveyEventTrigger surveyEventTrigger;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private OrganizationService organizationService;

    public Long requireOrgId(SubmitContext context) {
        return context.requireProperty("orgId", context::getOrgId, context::setOrgId,
                () -> requireSurvey(context).getOrgId());
    }

    @Override
    public Long requireSurveyId(SubmitContext context) {
        return context.getSurveyId();
    }

    @Override
    public String requireClientId(SubmitContext context) {
        return context.getClientId();
    }

    @Override
    public ClientSurvey requireSurvey(SubmitContext context) {
        return context.requireProperty("survey", context::getSurvey, context::setSurvey,
                () -> {
                    ClientSurvey survey = storage.getSurvey(context.getSurveyId());
                    if (survey == null) {
                        throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
                    }
                    return survey;
                });
    }

    @Override
    public List<ClientQuota> getQuotas(SubmitContext context) {
        return context.requireProperty("quotas", context::getQuotas, context::setQuotas,
                () -> requireSurvey(context).getQuotas());
    }

    @Override
    public List<ClientQuestion> requireQuestions(SubmitContext context) {
        return context.requireProperty("questions", context::getQuestions, context::setQuestions,
                () -> {
                    List<ClientQuestion> questions = requireSurvey(context).getQuestions();
                    if (questions == null) {
                        throw new SurveyErrorException(SurveyErrorCode.SURVEY_DELETE);
                    }
                    return questions;
                });
    }

    @Override
    public Map<String, ClientQuestion> requireQuestionNameMap(SubmitContext context) {
        return context.requireProperty("questionNameMap", context::getQuestionNameMap, context::setQuestionNameMap,
                () -> requireQuestions(context).stream().collect(Collectors.toMap(ClientQuestion::getName, Function.identity(), (o1, o2) -> o1)));
    }

    @Override
    public Map<Long, ClientQuestion> requireQuestionIdMap(SubmitContext context) {
        return context.requireProperty("questionIdMap", context::getQuestionIdMap, context::setQuestionIdMap,
                () -> requireQuestions(context).stream().collect(Collectors.toMap(ClientQuestion::getId, Function.identity(), (o1, o2) -> o1)));
    }

    @Override
    public String requireExternalUserId(SubmitContext context) {
        return context.getData().getExternalUserId();
    }

    @Override
    public SurveyCollectorMethod requireSurveyCollectorMethod(SubmitContext context) {
        return context.getData().getCollectorMethod();
    }

    @Override
    public SurveyResponse requireResponse(SubmitContext context) {
        return context.getProperty("response", context::getResponse, context::setResponse,
                () -> {
                    Long orgId = requireOrgId(context);
                    Long surveyId = requireSurveyId(context);
                    SurveyResponse response;
                    if (context.getData().getResponseId() != null && context.getData().getResponseId() != 0l) {
                        response = storage.getResponse(context.getData().getResponseId());
                    } else {
                        response = storage.getResponse(surveyId, context.getData().getClientId());
                    }

                    if (response == null) {
                        response = storage.addResponse(buildResponse(context));
                        surveyEventTrigger.responseCreate(orgId, surveyId, response.getId());
                    }
                    surveyEventTrigger.responseView(orgId, surveyId, response.getId());
                    if (context.getData().getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS && !context.isSubmit()) {
                        Long channelId = context.getData().getChannelId();
                        taskService.addTask(ADMINX_RESPONSE_VIEW_KEY, new AdminXResponseDto(context.getSurveyId(), response.getId(), channelId, false));
                    }
                    return response;
                });
    }

    /**
     * 从提交参数中构建 response
     */
    protected SurveyResponse buildResponse(SubmitContext context) {
        SurveySubmitRequestDto data = context.getData();
        buildSubmitDepartment(context);
        SurveyTrackingDataDto trackingDataDto = trackingService.parseRequest(request);
        Long orgId = requireOrgId(context);
        Long surveyId = context.getSurveyId();
        return new SurveyResponse(orgId, surveyId, trackingDataDto, data);
    }

    private void buildSubmitDepartment(SubmitContext context) {
        SurveySubmitRequestDto data = context.getData();
        CacheDepartment department = getDepartment(context);
        if (department != null) {
            data.setDepartmentId(department.getId());
            data.setDepartmentName(department.getName());
            data.setDepartmentCode(department.getCode());
        }
    }

    @Override
    public ClientChannel getChannel(SubmitContext context) {
        return context.getProperty("channel", context::getChannel, context::setChannel,
                () -> storage.getChannel(context.getData().getChannelId()));
    }

    @Override
    public SurveySendRecord getChannelRecord(SubmitContext context) {
        return context.getProperty("channelRecord", context::getChannelRecord, context::setChannelRecord,
                () -> storage.getChannelRecord(context.getSurveyId(), context.getData().getChannelId(), context.getData().getClientId()));
    }

    @Override
    public String getIp(SubmitContext context) {
        return context.getProperty("ip", context::getIp, context::setIp,
                () -> {
                    String ip = RestUtils.getClientIpAddress(request);
                    // 如果下次提交的时候，ip 变了，则更新 response 的 ip
//                    if (StringUtils.isNotEmpty(ip)) {
//                        SurveyResponse response = requireResponse(context);
//                        if (!ip.equals(response.getIp())) {
//                            response.setIp(ip);
//                        }
//                    }
                    return ip;
                });
    }

    @Override
    public List<SurveyResponseCell> requireOldCells(SubmitContext context) {
        return context.requireProperty("oldCells", context::getOldCells, context::setOldCells,
                () -> {
                    SurveyResponse response = requireResponse(context);
                    return storage.getCells(context.getSurveyId(), response.getId(), requireQuestionIdMap(context));
                });
    }

    @Override
    public List<SurveyResponseCell> requireNewCells(SubmitContext context) {
        return context.requireProperty("newCells", context::getNewCells, context::setNewCells,
                () -> {
                    SurveyResponse response = requireResponse(context);
                    return questionResponseHelper.convertToCells(context, response.getId(), requireQuestionNameMap(context));
                });
    }

    @Override
    public CacheDepartment getDepartment(SubmitContext context) {
        return context.getProperty("department", context::getDepartment, context::setDepartment,
                () -> {
                    Long id = context.getData().getDepartmentId();
                    String code = context.getData().getDepartmentCode();
                    if (id == null && StringUtils.isEmpty(code)) {
                        return null;
                    }
                    return departmentCacheHelper.get(id, requireOrgId(context), code);
                });
    }

    @Override
    public CacheApiKey getApikey(SubmitContext context) {
        return context.getProperty("apikey", context::getApiKey, context::setApiKey,
                () -> apiKeyCacheHelper.get(requireOrgId(context)));
    }

    @Override
    public void loadAllParams(SubmitContext context) {
        SurveySubmitRequestDto surveySubmitRequestDto = context.getData();
        SurveyResponse response = context.getResponse();
        SubmitAdditionDataDto responseAdditionDataDto = JsonHelper.toObject(response.getAdditionData(), SubmitAdditionDataDto.class);
        SubmitAdditionDataDto mergedAdditionDataDto = context.getData().castAdditionData(JsonHelper.toMap(surveySubmitRequestDto.getAdditionData()), responseAdditionDataDto);

        surveySubmitRequestDto.setAdditionData(mergedAdditionDataDto);
        response.setAdditionData(JsonHelper.toJson(mergedAdditionDataDto));
    }

    @Override
    public void startSurvey(SubmitContext context) {
        questionResponseHelper.copyOldToNew(requireOldCells(context), requireNewCells(context));
        context.getSurvey().setCellData(questionResponseHelper.getSubmitData(requireNewCells(context)));
        SubmitAdditionDataDto additionData = JsonHelper.toObject(requireResponse(context).getAdditionData(), SubmitAdditionDataDto.class);
        if (additionData == null) {
            additionData = new SubmitAdditionDataDto();
        }
        context.getSurvey().setAdditionData(additionData);
        context.getSurvey().setResponseId(context.getResponse().getId());
        context.getSurvey().setDoNotDisturb(context.getDoNotDisturb());
        boolean hasCheckMobile = requireQuestions(context).stream().anyMatch(i -> i.getIsVerifyMobile() != null && i.getIsVerifyMobile());
        if (hasCheckMobile) {
            context.getSurvey().setHasSmsBalance(smsAccountService.hasBalance(requireOrgId(context), 1));
        }
        context.getSurvey().setOrgVersion(organizationService.parseOrgVersion(context.getOrgId()).name());

    }

    @Override
    public void compareCells(SubmitContext context) {
        if (context.getLoaded().contains("addCells")) {
            return;
        }
        context.getLoaded().add("addCells");
        questionResponseHelper.compare(
                requireOldCells(context),
                requireNewCells(context),
                context.getAddCells(),
                context.getUpdateCells(),
                context.getDeleteCells()
        );
    }

    @Override
    public void sync(SubmitContext context) {
        if (context.getData().getIsCompleted()) {
            // 渠道记录状态,
            updateChannel(context);
            // 答卷状态
            updateStatus(context);
            // 使用配额
            useQuota(context);
        }
        updateCollectorMethod(context);
        Throwable e = null;
        try {
            // 保存 response cell
            storage.save(context);
            // 下一页提交和最终提交
            var finalSubmit = context.getData().getIsCompleted();
            if (finalSubmit) {
                surveyEventTrigger.responseSubmitFinal(context.getOrgId(), context.getSurveyId(), requireResponse(context).getId(), finalSubmit);
                //处理AdminX事件
                if (context.getData().getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) {
                    Long channelId = context.getData().getChannelId();
                    taskService.addTask(ADMINX_RESPONSE_SUBMIT_KEY, new AdminXResponseDto(context.getSurveyId(), requireResponse(context).getId(), channelId, true));
                }
            } else {
                surveyEventTrigger.responseSubmit(context.getOrgId(), context.getSurveyId(), requireResponse(context).getId(), finalSubmit);
            }
            if (context.getResponse().getStatus() == ResponseStatus.QUOTA_FUll) {
                throw new SurveyErrorException(SurveyErrorCode.QUOTA_OVER_LIMIT);
            }
        } catch (Throwable ex) {
            e = ex;
            log.error("提交问卷失败", ex);
            throw ex;
        } finally {
            if (e != null && context.getSuccessUseQuotas() != null && context.getSuccessUseQuotas()) {
                // rollback quota
                quotaLimit.deleteResponse(context.getSurveyId(), context.getResponse().getId(), context.getMatchQuotaIds());
            }
        }
    }


    private void updateChannel(SubmitContext context) {
        if (context.getChannelRecord() != null) {
            SurveySendRecord record = context.getChannelRecord();
            record.setReplyStatus(ReplyStatus.SUBMIT);
            record.setStatus(ChannelSendStatus.COMPLETE);
            context.setNeedUpdateChannelRecord(true);
            context.setChannelRecord(record);
        }
    }

    private void updateStatus(SubmitContext context) {
        SurveySubmitRequestDto dto = context.getData();
        SurveyResponse response = context.getResponse();
        response.setIsCompleted(true);
        if (dto.getIsEarlyCompleted()) {
            response.setStatus(ResponseStatus.EARLY_COMPLETED);
        } else {
            if (dto.getCollectorMethod() == SurveyCollectorMethod.SURVEY_PLUS) {
                response.setStatus(ResponseStatus.WAIT_AUDIT);
            } else {
                response.setStatus(ResponseStatus.FINAL_SUBMIT);
            }
            response.setSequence(getSurveyResponseSequence(context.getSurveyId()));
        }
        if (context.getChannelRecord() != null) {
            if (context.getChannel().getType() == ChannelType.PHONE_MSG) {
                response.setName(context.getChannelRecord().getName());
                response.setPhone(context.getChannelRecord().getAccount());
            }
            if (context.getChannel().getType() == ChannelType.EMAIL) {
                response.setName(context.getChannelRecord().getName());
                response.setEmail(context.getChannelRecord().getAccount());
            }
        }
        response.setFinishTime(new Date());
        if (dto.getDurationSeconds() == null || dto.getDurationSeconds() == 0) {
            // 计算两个Instant对象之间的Duration（持续时间）
            Duration duration = Duration.between(response.getCreateTime().toInstant(), response.getFinishTime().toInstant());
            response.setDurationSeconds((int) duration.getSeconds());
        } else {
            response.setDurationSeconds(dto.getDurationSeconds());
        }

        buildSubmitDepartment(context);
        response.fillUpSurveySubmitRequestDto(response, context.getData());
        context.setNeedUpdateResponse(true);
    }

    @Override
    public void updateCollectorMethod(SubmitContext context) {
        SurveyResponse response = context.getResponse();
        response.setCollectorMethod(context.getData().getCollectorMethod());
        storage.addResponse(response);
    }

    private Long getSurveyResponseSequence(@NotNull long surveyId) {
        String key = String.format("survey_response_sequence:%d", surveyId);
        return stringRedisTemplate.opsForValue().increment(key, 1);
    }

    private void useQuota(SubmitContext context) {
        if (CollectionUtils.isNotEmpty(context.getMatchQuotaIds())
                && List.of(ResponseStatus.FINAL_SUBMIT, ResponseStatus.WAIT_AUDIT).contains(context.getResponse().getStatus())) {
            boolean ok = quotaLimit.useQuota(context.getSurveyId(), context.getResponse().getId(), context.getMatchQuotaIds());
            if (ok) {
                context.setSuccessUseQuotas(true);
            } else {
                SurveyResponse response = context.getResponse();
                response.setIsCompleted(true);
                response.setStatus(ResponseStatus.QUOTA_FUll);
                context.setNeedUpdateResponse(true);
            }
        }
    }
}
