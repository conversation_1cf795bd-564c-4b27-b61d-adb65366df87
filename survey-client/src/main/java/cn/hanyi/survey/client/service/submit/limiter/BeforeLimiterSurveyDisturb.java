package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.service.SurveyDisturbRuleService;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * The class description
 *
 * @Author: Leo
 * @Date: 2023/8/31 17:36
 */
@Order(999)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class BeforeLimiterSurveyDisturb implements IBeforeLimiter {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private SurveyDisturbRuleService surveyDisturbRuleService;

    @Override
    public void check(SubmitContext context) {
        convertChannelType(context,loader.getChannel(context));
        //免打扰
        if (Arrays.asList(SurveyCollectorMethod.MP, SurveyCollectorMethod.APP, SurveyCollectorMethod.EMBEDDED).contains(context.getData().getCollectorMethod()) && !context.isSubmit()) {
            Boolean doNotDisturb = surveyDisturbRuleService.isDisturbSurvey(loader.requireSurveyId(context), loader.requireOrgId(context), false, loader.requireExternalUserId(context), loader.getChannel(context));
            if (doNotDisturb) {
                //增加免打扰记录
                surveyDisturbRuleService.addSurveyDisturbRecord(context.getSurveyId(), context.getOrgId(), context.getChannel().getId(), loader.requireExternalUserId(context), context.getClientId());
                throw new BadRequestException("问卷触发免打扰");
            }
        }
    }

    /**
     * *渠道类型 转换成收集方式
     * @param channel
     * @return
     */
    public void convertChannelType(SubmitContext context, ClientChannel channel) {
        SurveyCollectorMethod collectorMethod = SurveyCollectorMethod.LINK;
        if(channel == null) {
            // 一点一码没有channel
            if (context.getData().getCollectorMethod() == SurveyCollectorMethod.SHOP_QRCODE) {
                context.getData().setCollectorMethod(SurveyCollectorMethod.SHOP_QRCODE);
            } else {
                context.getData().setCollectorMethod(collectorMethod);
            }
            return;
        }
        switch(channel.getType()) {
            case COMMON:
                collectorMethod = SurveyCollectorMethod.LINK;break;
            case SHORT_LINK:
                collectorMethod = SurveyCollectorMethod.SHORT_LINK;break;
            case PHONE_MSG:
                collectorMethod = SurveyCollectorMethod.PHONE_MSG;break;
            case WECHAT_SERVICE:
                collectorMethod = SurveyCollectorMethod.WECHAT_SERVICE;break;
            case INJECT_WEB:
                collectorMethod = SurveyCollectorMethod.EMBEDDED;break;
            case SURVEY_PLUS:
                collectorMethod = SurveyCollectorMethod.SURVEY_PLUS;break;
            case SCENE_INTERACTION:
                collectorMethod = SurveyCollectorMethod.SCENE_INTERACTION;break;
            case MP:
                collectorMethod = SurveyCollectorMethod.MP;break;
            case APP:
                collectorMethod = SurveyCollectorMethod.APP;break;
            case EMAIL:
                collectorMethod = SurveyCollectorMethod.EMAIL;break;
            default:
        }
        context.getData().setCollectorMethod(collectorMethod);
    }
}
