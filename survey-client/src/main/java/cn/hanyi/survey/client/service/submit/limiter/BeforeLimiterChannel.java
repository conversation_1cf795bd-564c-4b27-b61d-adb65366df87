package cn.hanyi.survey.client.service.submit.limiter;

import cn.hanyi.survey.client.cache.LimitChannelCacheHelper;
import cn.hanyi.survey.client.dto.ClientChannel;
import cn.hanyi.survey.client.dto.ClientSurvey;
import cn.hanyi.survey.client.service.submit.IBeforeLimiter;
import cn.hanyi.survey.client.service.submit.ISubmitLoader;
import cn.hanyi.survey.client.service.submit.ISubmitStorage;
import cn.hanyi.survey.client.service.submit.SubmitContext;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.channel.ChannelSendStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.constant.error.SurveyErrorCode;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.dto.ext.SurveyEmbedExtDto;
import cn.hanyi.survey.core.entity.SurveyChannel;
import cn.hanyi.survey.core.entity.SurveySendRecord;
import cn.hanyi.survey.core.exception.SurveyErrorException;
import cn.hanyi.survey.core.service.expression.ExpressionService;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Order(1030)
@Component
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class BeforeLimiterChannel implements IBeforeLimiter {

    @Autowired
    private ISubmitLoader loader;
    @Autowired
    private ISubmitStorage storage;
    @Autowired
    private LimitChannelCacheHelper limitChannelCacheHelper;
    @Autowired
    private ExpressionService expressionService;

    @Override
    public void check(SubmitContext context) {
        ClientSurvey survey = loader.requireSurvey(context);
        ClientChannel channel = loader.getChannel(context);
        if (channel == null) {
            if (context.getData().getChannelId() != null) {
                throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);
            }
            survey.setChannel(new ClientChannel());
            return;
        }
        survey.setChannel(channel);
        checkType(survey, channel);
        checkIp(context, survey, channel);
        checkDateRange(channel);
        checkStatus(channel);
        checkOpenId(context, survey, channel);
        checkRecord(context, survey, channel);
        convertChannelType(context, channel);
        WhiteBlackListCheck(context, channel);
    }

    private void checkOpenId(SubmitContext context, ClientSurvey survey, ClientChannel channel) {
        String openid = context.getData().getOpenid();
        if (List.of(ChannelType.SHORT_LINK, ChannelType.SURVEY_PLUS).contains(channel.getType()) && channel.getEnableWechatReplyOnly()) {
            //开启了每个微信只能填答一次 需要验证该微信openid 是否已经填答了
            if (StringUtils.isNotEmpty(openid) && limitChannelCacheHelper.lastTimeByOpenId(survey.getId(), channel.getId(), openid) != null) {
                throw new SurveyErrorException(SurveyErrorCode.ALREADY_WECHAT_SUBMIT);
            }
        }
    }

    private void checkStatus(ClientChannel channel) {
        //校验渠道状态
        switch (channel.getStatus()) {
            case UNSET:
            case COMPLETE:
                throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_COMPLETE);
            case PAUSE:
                throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_PAUSE);
            case RECOVERY:
                break;
            default:
                throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_ERROR);
        }
    }

    private void checkType(ClientSurvey survey, ClientChannel channel) {
        //校验问卷渠道
        if (!channel.getSid().equals(survey.getId())) {
            throw new SurveyErrorException(SurveyErrorCode.CHANNEL_TYPE_ERROR);
        }
    }

    private void checkDateRange(ClientChannel channel) {
        if (unboxBool(channel.getEnableDateLimit())) {
            IBeforeLimiter.checkDateRange(channel.getStartTime(), channel.getEndTime());
        }
    }

    private void checkIp(SubmitContext context, ClientSurvey survey, ClientChannel channel) {
        String ip = loader.getIp(context);
        if (!unboxBool(channel.getEnableIpLimit()) || StringUtils.isEmpty(ip)) {
            return;
        }
        if (limitChannelCacheHelper.lastTimeByIp(survey.getId(), channel.getId(), ip) != null) {
            throw new SurveyErrorException(SurveyErrorCode.IP_ALREADY_SUBMIT);
        }
    }

    private void checkRecord(SubmitContext context, ClientSurvey survey, ClientChannel channel) {
        //根据client_id 获取问卷推送记录 问卷推送只有短信和微信服务号渠道 才有记录
        // 未访问,未提交,未完成
        SurveySendRecord record = loader.getChannelRecord(context);
        if (record != null) {
            //问卷已完成
            if (record.getStatus() == ChannelSendStatus.COMPLETE)
                throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
            //问卷已提交
            if (record.getReplyStatus() == ReplyStatus.SUBMIT)
                throw new SurveyErrorException(SurveyErrorCode.ALREADY_SUBMIT);
            //如果问卷ReplyStatus是未访问状态 更新ReplyStatus为未提交
            boolean saveRecord = false;
            if (record.getReplyStatus() == ReplyStatus.UN_VISIT) {
                record.setReplyStatus(ReplyStatus.UN_SUBMIT);
                saveRecord = true;
            }
            //如果问卷ReceiveStatus是未知状态 更新ReceiveStatus为已接收
            if (record.getReceiveStatus() == ReceiveStatus.UNKNOWN) {
                record.setReceiveStatus(ReceiveStatus.SUCCESS);
                saveRecord = true;
            }
            if (saveRecord) {
                storage.updateChannelRecord(record);
            }
        }
    }

    /**
     * *渠道类型 转换成收集方式
     * @param channel
     * @return
     */
    public void convertChannelType(SubmitContext context, ClientChannel channel) {
        SurveyCollectorMethod collectorMethod = SurveyCollectorMethod.LINK;
        if(channel == null) {
            context.getData().setCollectorMethod(collectorMethod);
            return;
        }
        switch(channel.getType()) {
            case COMMON:
                collectorMethod = SurveyCollectorMethod.LINK;break;
            case SHORT_LINK:
                collectorMethod = SurveyCollectorMethod.SHORT_LINK;break;
            case PHONE_MSG:
                collectorMethod = SurveyCollectorMethod.PHONE_MSG;break;
            case WECHAT_SERVICE:
                collectorMethod = SurveyCollectorMethod.WECHAT_SERVICE;break;
            case INJECT_WEB:
                collectorMethod = SurveyCollectorMethod.EMBEDDED;break;
            case SURVEY_PLUS:
                collectorMethod = SurveyCollectorMethod.SURVEY_PLUS;break;
            case SCENE_INTERACTION:
                collectorMethod = SurveyCollectorMethod.SCENE_INTERACTION;break;
            case MP:
                collectorMethod = SurveyCollectorMethod.MP;break;
            case APP:
                collectorMethod = SurveyCollectorMethod.APP;break;
            case EMAIL:
                collectorMethod = SurveyCollectorMethod.EMAIL;break;
            default:
        }
        context.getData().setCollectorMethod(collectorMethod);
    }

    private void WhiteBlackListCheck(SubmitContext context, SurveyChannel channel) {
        Optional.ofNullable(channel).flatMap(c -> Optional.ofNullable(channel.getConfigure())).ifPresent(configure -> {
            SurveyEmbedExtDto embed = JsonHelper.toObject(configure, SurveyEmbedExtDto.class);
            WhiteBlackListCheck(embed, context.getData().getExternalUserId(), context.getData().getExternalCompanyId(), context.getData().getParameters());
        });
    }

    public void WhiteBlackListCheck(SurveyEmbedExtDto embed, String externalUserId, String externalCompanyId, Map<String, Object> parameters) {

        Optional.ofNullable(embed).ifPresent(e -> {
            // 按钮嵌入不需要检测
            // 本来用embedWhiteList就可以做总开关，然后通过embedListType就可以区分是使用黑名单还是白名单的  不知道为啥前端还要添加个字段
            // 做人呢  开心最重要
            if ("PART".equals(e.getEmbedShow()) && (e.getEmbedWhiteList() || e.getEmbedBlackList()) && !"2".equals(e.getInjectType())) {
                boolean throwError = false;
                parameters.putAll((Map) parameters.getOrDefault("parameters", Map.of()));
                switch (e.getEmbedListType()) {
                    // 只有做了配置才会做判断
                    case WHITELIST -> {
                        if (
                                (!e.getEmbedUserId().isEmpty() && !e.getEmbedUserId().contains(externalUserId))
                                        || (!e.getEmbedCompanyId().isEmpty() && !e.getEmbedCompanyId().contains(externalCompanyId))
                                        || (!StringUtils.isEmpty(e.getConditionalExpression()) && !expressionService.triggerExpression(e.getConditionalExpression(), parameters))
                        ) {
                            throwError = true;
                        }
                    }
                    case BLACKLIST -> {
                        if (
                                (e.getEmbedUserId().contains(externalUserId))
                                        || (e.getEmbedCompanyId().contains(externalCompanyId))
                                        || (expressionService.triggerExpression(e.getConditionalExpression(), parameters))
                        ) {
                            throwError = true;
                        }
                    }
                }
                if (throwError) {
                    throw new SurveyErrorException(SurveyErrorCode.CHANNEL_WHITE_BLACK_LIST_ERROR);
                }
            }
        });


    }


}
