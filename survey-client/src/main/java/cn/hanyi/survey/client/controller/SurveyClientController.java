package cn.hanyi.survey.client.controller;

import cn.hanyi.survey.client.dto.*;
import cn.hanyi.survey.client.service.*;
import cn.hanyi.survey.client.service.submit.limiter.BeforeLimiterMobileQuestionVerify;
import cn.hanyi.survey.client.service.submit.limiter.BeforeLimiterSmartVerify;
import cn.hanyi.survey.client.utils.EncryptDecryptHelper;
import cn.hanyi.survey.core.dto.SurveySubmitRequestDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.extension.dto.SmartVerifyRequestDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


@Validated
@Tag(name = "问卷-客户端")
@RestController("surveyClientController2")
@ConditionalOnProperty(value = "survey.client.enabled", havingValue = "true")
public class SurveyClientController {
    @Autowired
    private SubmitService submitService;
    @Autowired
    private SurveyBehaviorService behaviorService;
    @Autowired
    private WechatMpService wechatMpService;
    @Autowired
    private WechatConfigureService wechatConfigureService;
    @Autowired
    private SurveyRandomResultService randomResultService;
    @Autowired
    private EncryptDecryptHelper encryptDecryptHelper;
    @Autowired
    private SurveyDisturbRuleService surveyDisturbRuleService;
    @Autowired
    private BeforeLimiterSmartVerify beforeLimiterSmartVerify;
    @Autowired
    private BeforeLimiterMobileQuestionVerify beforeLimiterMobileQuestionVerify;

    @Autowired
    private ClientCommonService clientCommonService;

    @Operation(summary = "1.9.0-短链编号-获取问卷")
    @PostMapping(value = "/surveys/start/{shortCode}")
    public ResourceResponseDto<ClientSurvey> startByShortCode(@PathVariable String shortCode, @RequestBody SurveySubmitRequestEncryptedDto requestDto) {
        return new ResourceResponseDto<>(submitService.submit(false, shortCode, requestDto).getSurvey());
    }

    @Operation(summary = "1.11.2-获取问卷")
    @PostMapping("/surveys/{surveyId}/start")
    public ResourceResponseDto<ClientSurvey> startBySurveyId(@PathVariable Long surveyId, @RequestBody SurveySubmitRequestEncryptedDto requestDto, HttpServletRequest request) {
        boolean submit = request.getRequestURI().endsWith("/submit");
        return new ResourceResponseDto<>(submitService.submit(submit, surveyId, requestDto).getSurvey());
    }

    @PostMapping("/surveys/submit/{shortCode}")
    @Operation(summary = "1.9.0-短链编号-提交问卷")
    public ResourceResponseDto<ResponseResultDto> submitByShortCode(@PathVariable String shortCode, @RequestBody SurveySubmitRequestEncryptedDto requestDto) {
        return new ResourceResponseDto<>(submitService.submit(true, shortCode, requestDto).getResponseResultDto());
    }

    @PostMapping("/surveys/params/{shortCode}")
    @Operation(summary = "1.9.0-短链编号-获取外参")
    public ResourceResponseDto<SurveyParamsResponseEncryptedDto> paramsByShortCode(@PathVariable String shortCode, @RequestBody SurveyParamsRequestEncryptedDto requestDto) {
        return new ResourceResponseDto<>(encryptDecryptHelper.encryptParams(shortCode, requestDto));
    }


    @RequestMapping("/surveys/{surveyId}/start/{clientId}")
    @Operation(summary = "获取问卷")
    public ResourceResponseDto<ClientSurvey> start(@PathVariable Long surveyId,
                                                   @PathVariable String clientId,
                                                   @RequestBody(required = false) SurveySubmitRequestDto requestDto,
                                                   HttpServletResponse response) {
        if (requestDto == null) requestDto = new SurveySubmitRequestDto();
        if (requestDto.getClientId() == null && clientId != null) requestDto.setClientId(clientId);
        return new ResourceResponseDto<>(submitService.submit(false, surveyId, requestDto).getSurvey());
    }

    @PostMapping("/surveys/{surveyId}/submit")
    @Operation(summary = "提交问卷")
    public ResourceResponseDto<ResponseResultDto> submit(@PathVariable Long surveyId, @RequestBody SurveySubmitRequestDto requestDto) {
        return new ResourceResponseDto<>(submitService.submit(true, surveyId, requestDto).getResponseResultDto());
    }

    @PostMapping("/surveys/{surveyId}/behavior-record")
    @Operation(summary = "行为记录")
    public ResourceResponseDto behaviorRecord(@PathVariable Long surveyId, @RequestBody ClientSurveyBehaviorDto requestDto) {
        behaviorService.addSurveyBehaviorDto(surveyId, requestDto);
        return new ResourceResponseDto<>();
    }

    @PostMapping("/surveys/{surveyId}/channel/{channelId}/wechat-code")
    @Operation(
            summary = "获取微信授权链接，通过链接可以获取code",
            description = "获取微信授权链接，通过链接可以获取code"
    )
    public ResourceResponseDto<WechatAuthorizeDto> getWechatCode(@PathVariable long surveyId, @PathVariable long channelId,
                                                                 @RequestBody WechatAuthorizeDto params) {
        return wechatMpService.getWechatCode(surveyId, channelId, params);
    }

    @PostMapping("/surveys/{surveyId}/channel/{channelId}/wechat-openid")
    @Operation(
            summary = "通过微信code获取openid",
            description = "通过微信code获取openid"
    )
    public ResourceResponseDto<WechatAuthorizeDto> getWechatOpenId(@PathVariable long surveyId, @PathVariable long channelId,
                                                                   @RequestBody WechatAuthorizeDto params) {
        return wechatMpService.getWechatOpenId(surveyId, channelId, params);
    }

    @GetMapping("/surveys/{surveyId}/channel/{channelId}/preview")
    @Operation(
            summary = "渠道预览",
            description = "渠道预览"
    )
    public ResourceResponseDto<ClientSurvey> getWechatOpenId(@PathVariable long surveyId, @PathVariable long channelId) {
        ClientSurvey survey = clientCommonService.requireSurvey(surveyId, channelId);
        return new ResourceResponseDto(survey);
    }

    @GetMapping("/surveys/lottery/accessToken")
    @Operation(summary = "获取openid")
    public ResourceResponseDto<WechatAuthorizeDto> accessToken(String code) {
        String openId = wechatConfigureService.getAccessToken(code);
        WechatAuthorizeDto wechatAuthorizeDto = new WechatAuthorizeDto();
        wechatAuthorizeDto.setOpenId(openId);
        return new ResourceResponseDto(wechatAuthorizeDto);
    }

    @PostMapping("/surveys/{surveyId}/response/{responseId}/random-result")
    @Operation(summary = "保存问卷随机结果表")
    public ResourceResponseDto<Boolean> saveRandomResult(@PathVariable long surveyId, @PathVariable long responseId,
                                                         @RequestBody List<SurveyRandomResultRequest> data) {
        return new ResourceResponseDto<>(randomResultService.saveRandomResult(surveyId, responseId, data));
    }

    @PostMapping("/surveys/{surveyId}/survey-disturb-record")
    @Operation(summary = "问卷免打扰记录")
    public ResourceResponseDto saveRandomResult(@PathVariable long surveyId, @RequestBody ClientSurveyDisturbDto requestDto) {
        surveyDisturbRuleService.addSurveyDisturbRecord(surveyId, requestDto);
        return new ResourceResponseDto<>();
    }


    @PostMapping("/surveys/{surveyId}/check-smart-verify")
    @Operation(summary = "智能验证校验")
    public ResourceResponseDto<Boolean> checkSmartVerify(@PathVariable long surveyId, @RequestBody SmartVerifyRequestDto smartVerify) {
        return new ResourceResponseDto<>(beforeLimiterSmartVerify.checkSmartVerify(smartVerify));
    }

    @Validated
    @PostMapping("/surveys/{surveyId}/questions/{questionId}/responses/{responseId}/send-verify-code")
    @Operation(summary = "手机题-发送验证码")
    public ResourceResponseDto<Boolean> sendVerifyCodeByMobileQuestion(
            @PathVariable long surveyId,
            @PathVariable long questionId,
            @PathVariable long responseId,
            @Valid @RequestBody MobileQuestionSendVerifyCodeRequestDto dto) {
        return new ResourceResponseDto<>(beforeLimiterMobileQuestionVerify.sendVerifyCode(surveyId, questionId, responseId, dto.getMobile()));
    }

    @Validated
    @GetMapping("/surveys/{surveyId}/questions/{questionId}/responses/{responseId}/check-verify-code")
    @Operation(summary = "手机题-校验验证码")
    public ResourceResponseDto<Boolean> checkVerifyCodeByMobileQuestion(
            @PathVariable long surveyId,
            @PathVariable long questionId,
            @PathVariable long responseId,
            @NotEmpty @RequestParam String mobile,
            @NotEmpty @RequestParam String code) {
        return new ResourceResponseDto<>(beforeLimiterMobileQuestionVerify.checkVerifyCode(surveyId, questionId, responseId, mobile, code));
    }

    @Validated
    @GetMapping("/surveys/{surveyId}/questions/{questionId}/check-mobile-deduplication")
    @Operation(summary = "手机题-手机号去重")
    public ResourceResponseDto<Boolean> checkDeduplicationByMobileQuestion(
            @PathVariable long surveyId,
            @PathVariable long questionId,
            @NotEmpty @RequestParam String mobile) {
        return new ResourceResponseDto<>(beforeLimiterMobileQuestionVerify.checkMobileDeduplication(surveyId, questionId, mobile));
    }


    @GetMapping("/surveys/send-manage/{sendToken}")
    @Operation(summary = "嵌入发送管理")
    public ResourceResponseDto<SendMangeResDto> sendManage(@PathVariable String sendToken) {
        return new ResourceResponseDto<>(clientCommonService.sendManage(sendToken));
    }

    @PostMapping("/surveys/union-start")
    @Operation(summary = "合并发送管理，打开问卷")
    public ResourceResponseDto<ClientSurvey> unionStart(@RequestBody @NotNull SurveySubmitRequestDto requestDto) {
        SurveySubmitRequestDto dto = StringUtils.isNotEmpty(requestDto.getEncryptedData())
                ? encryptDecryptHelper.decrypt(new SurveySubmitRequestEncryptedDto(requestDto.getEncryptType(), requestDto.getEncryptedKey(), requestDto.getEncryptedData()))
                : requestDto;
        clientCommonService.getSendManageSurveyId(dto);
        return new ResourceResponseDto<>(submitService.submit(false, dto.getSurveyId(), dto).getSurvey());
    }

}



























